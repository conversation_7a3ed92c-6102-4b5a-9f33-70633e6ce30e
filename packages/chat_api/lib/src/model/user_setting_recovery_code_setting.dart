//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_recovery_code_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingRecoveryCodeSetting {
  /// Returns a new [UserSettingRecoveryCodeSetting] instance.
  UserSettingRecoveryCodeSetting({
    this.enable,
  });

  @JsonKey(
    name: r'enable',
    required: false,
    includeIfNull: false,
  )
  final bool? enable;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingRecoveryCodeSetting && other.enable == enable;

  @override
  int get hashCode => enable.hashCode;

  factory UserSettingRecoveryCodeSetting.fromJson(Map<String, dynamic> json) =>
      _$UserSettingRecoveryCodeSettingFromJson(json);

  Map<String, dynamic> toJson() => _$UserSettingRecoveryCodeSettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
