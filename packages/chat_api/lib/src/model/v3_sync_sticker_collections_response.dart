//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_sync_sticker_collections_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SyncStickerCollectionsResponse {
  /// Returns a new [V3SyncStickerCollectionsResponse] instance.
  V3SyncStickerCollectionsResponse({
    this.data,
    this.collectionDeleted,
    this.syncTime,
  });

  /// List id of new collection or updated.
  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<String>? data;

  /// List of collection deleted.
  @JsonKey(
    name: r'collectionDeleted',
    required: false,
    includeIfNull: false,
  )
  final List<String>? collectionDeleted;

  @<PERSON>son<PERSON>ey(
    name: r'syncTime',
    required: false,
    includeIfNull: false,
  )
  final String? syncTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SyncStickerCollectionsResponse &&
          other.data == data &&
          other.collectionDeleted == collectionDeleted &&
          other.syncTime == syncTime;

  @override
  int get hashCode =>
      data.hashCode + collectionDeleted.hashCode + syncTime.hashCode;

  factory V3SyncStickerCollectionsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3SyncStickerCollectionsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3SyncStickerCollectionsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
