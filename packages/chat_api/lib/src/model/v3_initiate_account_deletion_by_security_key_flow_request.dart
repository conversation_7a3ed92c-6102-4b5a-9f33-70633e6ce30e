//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_account_deletion_by_security_key_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateAccountDeletionBySecurityKeyFlowRequest {
  /// Returns a new [V3InitiateAccountDeletionBySecurityKeyFlowRequest] instance.
  V3InitiateAccountDeletionBySecurityKeyFlowRequest({
    this.username,
  });

  @JsonKey(
    name: r'username',
    required: false,
    includeIfNull: false,
  )
  final String? username;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateAccountDeletionBySecurityKeyFlowRequest &&
          other.username == username;

  @override
  int get hashCode => username.hashCode;

  factory V3InitiateAccountDeletionBySecurityKeyFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateAccountDeletionBySecurityKeyFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateAccountDeletionBySecurityKeyFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
