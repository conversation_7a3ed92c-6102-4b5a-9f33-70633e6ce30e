import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart';
import 'package:cross_file/cross_file.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../common/di/di.dart';
import '../../data/models/invitable_user.dart';
import '../invite_to_channel/widgets/invitable_users_list_view.dart';
import '../invite_to_channel/widgets/searched_users_list_view.dart';
import 'bloc/create_channel_bloc.dart';

typedef onTakeAvatar = void Function(XFile file);

class CreateChannelBottomSheet extends StatefulWidget {
  const CreateChannelBottomSheet({
    super.key,
    this.onCreatedChannel,
    this.onTapTakePhoto,
    this.onTapOpenGallery,
    this.openImageViewPage,
  });

  final void Function({
    required String channelId,
    required String workspaceId,
  })? onCreatedChannel;
  final VoidCallback? onTapTakePhoto;
  final VoidCallback? onTapOpenGallery;
  final void Function(Uint8List avatarData)? openImageViewPage;

  @override
  State<CreateChannelBottomSheet> createState() =>
      _CreateChannelBottomSheetState();
}

class _CreateChannelBottomSheetState
    extends BasePageState<CreateChannelBottomSheet, CreateChannelBloc> {
  final _appEventBus = GetIt.instance.get<AppEventBus>();
  late StreamSubscription _subscription;

  Uint8List? _avatar;
  String? _avatarPath;
  String _channelName = '';
  List<String> _userIDsInvited = [];
  Map<String, InvitableUser> _selectedAccounts = {};
  bool _isSearching = false;
  String _keyword = '';
  bool _isSending = false;

  @override
  void initState() {
    _subscription = _appEventBus.on<ChooseAvatarEvent>().listen((event) async {
      if (event.avatarType != AvatarType.channel) return;
      _avatarPath = event.filePath;
      final bytes = await XFile(event.filePath).readAsBytes();
      bloc.add(ChangeAvatarChannelEvent(bytes));
    });
    super.initState();
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<CreateChannelBloc, CreateChannelState>(
      builder: (BuildContext context, state) {
        state.when(
          initial: () {},
          channelNameChanged: (channelName) {
            _channelName = channelName;
          },
          channelAvatarChanged: (avatarData) {
            _avatar = avatarData;
          },
          userIDsInvitedUpdated: (List<String> userIDsInvited) {
            _userIDsInvited = List.from(userIDsInvited);
          },
          searchingStatusChanged: (bool isSearching) {
            _isSearching = isSearching;
          },
          searchTextChanged: (String keyword) {
            _keyword = keyword;
          },
        );

        return ui.CreateChannelBottomSheet(
          parentContext: context,
          onSetChannelAvatar: _showActionSheetSelectAvatarOptions,
          onCancel: _onCancelCreateChannel,
          onCreateChannel: _onCreateChannel,
          onRemoveNameChannelButtonPressed: _onRemoveNameChannelButtonPressed,
          onChangeChannelName: _onChangeChannelName,
          avatarData: _avatar,
          allAccounts: [],
          selectedAccounts: {},
          onInviteButtonPressed: () {},
          onAccountSelectedToInviteCardPressed: (_, __) {},
          onRemoveAccountSelectedButtonPressed: (_, __) {},
          onChangedTextField: _onChangeSearchText,
          onRemoveSearchButtonPressed: (
            BuildContext,
            TextEditingController,
          ) {
            _onChangeSearchText(context, '');
          },
          filteredAccounts: [],
          onSearchStatusChanged: ({bool isSearching = false}) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              bloc.add(ChangeSearchingStatusEvent(isSearching));
            });
          },
          logicWidget: _buildInvitableUsers(context),
        );
      },
    );
  }

  Widget _buildInvitableUsers(BuildContext context) {
    bool showSearchedUsers = !_isSearching || _keyword.isEmpty;
    return Expanded(
      child: Column(
        children: [
          if (_selectedAccounts.isNotEmpty) _buildListAccountSelected(context),
          Expanded(
            child: Stack(
              children: [
                Offstage(
                  offstage: !showSearchedUsers,
                  child: InvitableUsersListView(
                    selectedUserIds: _userIDsInvited,
                    onTapUser: _onSelectAccount,
                  ),
                ),
                Offstage(
                  offstage: showSearchedUsers,
                  child: SearchedUsersListView(
                    keyword: _keyword,
                    selectedUserIds: _userIDsInvited,
                    onTapUser: _onSelectAccount,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListAccountSelected(BuildContext context) {
    final selectedAccounts = _selectedAccounts.values.toList();
    return Padding(
      padding: EdgeInsets.only(bottom: 10.h),
      child: Container(
        width: double.infinity,
        height: 56.h,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.015),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: selectedAccounts.length,
          itemBuilder: (context, index) {
            final item = selectedAccounts[index];
            return ui.AccountSelectedWithXIcon(
              onTap: () {
                _onRemoveAccount(item);
              },
              avatarUrl: UrlUtils.parseAvatar(item.avatar),
              name: item.name,
            );
          },
        ),
      ),
    );
  }

  void _onCancelCreateChannel(BuildContext context) {
    Navigator.of(context).pop();
  }

  Future<void> _onCreateChannel(BuildContext context) async {
    if (_isSending) {
      return;
    }

    LoadingOverlayHelper.showLoading(context);
    FocusScope.of(context).unfocus();

    bloc.add(
      CreateNewChannelEvent(
        name: _channelName,
        avatarPath: _avatarPath,
        userIDsInvited: _userIDsInvited,
        onCreated: ({
          required String channelId,
          required String workspaceId,
        }) async {
          _isSending = false;
          LoadingOverlayHelper.hideLoading(context);
          Navigator.of(context).pop();
          widget.onCreatedChannel?.call(
            channelId: channelId,
            workspaceId: workspaceId,
          );
        },
        onError: (error) {
          _isSending = false;
          LoadingOverlayHelper.hideLoading(context);
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.of(context).pop();
            },
          );
        },
      ),
    );
  }

  void _onRemoveNameChannelButtonPressed(
    BuildContext context,
    TextEditingController searchController,
  ) {
    searchController.clear();
    bloc.add(ChangeNameChannelEvent(''));
  }

  void _showActionSheetSelectAvatarOptions(BuildContext context) {
    FocusManager.instance.primaryFocus?.unfocus();
    // final appLocalizations = AppLocalizations.of(context)!;

    ui.ActionSheetUtil.showSetAvatarChannel1NlActionSheet(
      context,
      isRoleMember: false,
      onTapOpenGallery: _onTapOpenGallery,
      onTapTakePhoto: _onTapTakePhoto,
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      hasAvatar: _avatar != null,
      onTapViewAvatar: () {
        Navigator.of(context).pop();
        widget.openImageViewPage?.call(_avatar!);
      },
      onTapRemove: () {
        Navigator.of(context).pop();
        bloc.add(ChangeAvatarChannelEvent(null));
        _avatarPath = null;
      },
    );
  }

  void _onChangeChannelName(String value) {
    _channelName = value;
  }

  Future<void> _onTapTakePhoto() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      widget.onTapTakePhoto?.call();
    }
  }

  Future<void> _onTapOpenGallery() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      widget.onTapOpenGallery?.call();
    }
  }

  void _onChangeSearchText(BuildContext context, String keyword) {
    bloc.add(ChangeSearchTextEvent(keyword));
  }

  void _onSelectAccount(InvitableUser item) {
    if (_selectedAccounts[item.userId] == null) {
      _userIDsInvited.add(item.userId);
      _selectedAccounts[item.userId] = item;
    } else {
      _selectedAccounts.removeWhere((key, value) => key == item.userId);
      _userIDsInvited.remove(item.userId);
    }
    bloc.add(UpdateUserIDsInvitedUserEvent(userIDsInvited: _userIDsInvited));
  }

  void _onRemoveAccount(InvitableUser item) {
    _selectedAccounts.removeWhere((key, value) => key == item.userId);
    _userIDsInvited.remove(item.userId);
    bloc.add(UpdateUserIDsInvitedUserEvent(userIDsInvited: _userIDsInvited));
  }
}

class ViewAvatarAppbarWidgetImplementation
    extends ui.ViewAvatarAppbarWidgetInterface {
  ViewAvatarAppbarWidgetImplementation({
    required this.context,
    required this.onSaveToGallery,
  });

  final BuildContext context;
  final VoidCallback onSaveToGallery;

  @override
  void onBackButtonClicked() {
    Navigator.pop(context);
  }

  @override
  void onDownloadButtonClicked() {
    onSaveToGallery();
  }
}
