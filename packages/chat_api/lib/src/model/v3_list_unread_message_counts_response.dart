//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_list_unread_message_counts_response_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_unread_message_counts_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListUnreadMessageCountsResponse {
  /// Returns a new [V3ListUnreadMessageCountsResponse] instance.
  V3ListUnreadMessageCountsResponse({
    this.ok,
    this.data,
    this.error,
    this.paging,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3ListUnreadMessageCountsResponseData>? data;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListUnreadMessageCountsResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + paging.hashCode;

  factory V3ListUnreadMessageCountsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListUnreadMessageCountsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListUnreadMessageCountsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
