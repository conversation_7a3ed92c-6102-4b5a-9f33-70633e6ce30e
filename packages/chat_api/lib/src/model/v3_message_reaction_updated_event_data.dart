//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:chat_api/src/model/message_reaction_updated_event_data_message_reaction_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_message_reaction_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3MessageReactionUpdatedEventData {
  /// Returns a new [V3MessageReactionUpdatedEventData] instance.
  V3MessageReactionUpdatedEventData({
    this.workspaceId,
    this.channelId,
    this.messageId,
    this.reactions,
    this.destination,
    this.jsonReactions,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @JsonKey(
    name: r'reactions',
    required: false,
    includeIfNull: false,
  )
  final List<MessageReactionUpdatedEventDataMessageReactionData>? reactions;

  @JsonKey(
    name: r'destination',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelDestinationCloudEvent? destination;

  @JsonKey(
    name: r'jsonReactions',
    required: false,
    includeIfNull: false,
  )
  final Map<String, MessageReactionUpdatedEventDataMessageReactionData>?
      jsonReactions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3MessageReactionUpdatedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.messageId == messageId &&
          other.reactions == reactions &&
          other.destination == destination &&
          other.jsonReactions == jsonReactions;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      messageId.hashCode +
      reactions.hashCode +
      destination.hashCode +
      jsonReactions.hashCode;

  factory V3MessageReactionUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3MessageReactionUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3MessageReactionUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
