// user_provided_data/private_data_handler.dart
import 'dart:async';

import 'package:app_core/core.dart';
import 'package:auth/auth.dart' as auth;
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';

class PrivateDataHandler {
  PrivateDataHandler();

  final AppRouter _router = GetIt.instance<AppRouter>();
  late final StreamSubscription<PrivateDataEvent>? _privateDataSubscription;

  void setupPrivateDataHandler() {
    _privateDataSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<PrivateDataEvent>()
        .listen(_onPrivateDataEvent);
  }

  void dispose() {
    _privateDataSubscription?.cancel();
  }

  // Event dispatcher
  void _onPrivateDataEvent(PrivateDataEvent event) {
    if (event is SetAliasNameEvent) {
      _handleSetAliasNameEvent(event);
      return;
    }
    if (event is SetAliasNameSuccessEvent) {
      _handleSetAliasNameSuccessEvent();
      return;
    }
    if (event is RetrySetAliasNameEvent) {
      _handleRetrySetAliasNameEvent(event);
      return;
    }
    if (event is InsertAliasNameEvent) {
      _handleInsertAliasNameEvent(event);
      return;
    }
    if (event is AddPinChannelSuccessEvent) {
      _handleAddPinChannelSuccessEvent();
      return;
    }
    if (event is RetryPinChannelEvent) {
      _handleRetryPinChannelEvent(event);
      return;
    }
    if (event is AddPinChannelEvent) {
      _handleAddPinChannelEvent(event);
      return;
    }
    if (event is UnPinChannelEvent) {
      _handleUnPinChannelEvent(event);
      return;
    }
    if (event is UpdatePinChannelEvent) {
      _handleUpdatePinChannelEvent(event);
    }
  }

  // Event handlers
  void _handleSetAliasNameEvent(SetAliasNameEvent event) {
    _setAliasName(userId: event.userId, aliasName: event.aliasName);
    AppEventBus.publish(SetAliasNameSuccessEvent(success: true));
  }

  void _handleSetAliasNameSuccessEvent() {
    if (_router.current.name == UserProfileRoute.name) {
      _router.popUntilRouteWithName(UserProfileRoute.name);
    }
  }

  void _handleRetrySetAliasNameEvent(RetrySetAliasNameEvent event) {
    // The data from the event is the server's current state.
    final userPrivateData = _createUserPrivateData(event.data);
    _retrySetAliasName(userPrivateData);
  }

  void _handleInsertAliasNameEvent(InsertAliasNameEvent event) {
    final userPrivateData = _createUserPrivateData(event.data);
    _insertAliasName(userPrivateData);
  }

  void _handleAddPinChannelSuccessEvent() {
    if (_router.current.name == HomeRoute.name) {
      _router.popUntilRouteWithName(HomeRoute.name);
    }
  }

  void _handleRetryPinChannelEvent(RetryPinChannelEvent event) {
    // The data from the event is the server's current state.
    final channelPrivateData = _createChannelPrivateData(event.data);
    _retryPinChannel(channelPrivateData);
  }

  void _handleAddPinChannelEvent(AddPinChannelEvent event) {
    _addPinChannel(channelId: event.channelId, isPin: event.isPin);
  }

  void _handleUnPinChannelEvent(UnPinChannelEvent event) {
    _unPinChannel(event.channelId);
  }

  void _handleUpdatePinChannelEvent(UpdatePinChannelEvent event) {
    final channelPrivateData = _createChannelPrivateData(event.data);
    _updatePinChannel(channelPrivateData);
  }

  // Data creation helpers
  UserPrivateData _createUserPrivateData(Map<String, dynamic> data) {
    final sessionKey = auth.Config.getInstance().activeSessionKey ?? '';
    data['sessionKey'] = sessionKey;
    return UserPrivateData.fromJson(data);
  }

  ChannelPrivateData _createChannelPrivateData(Map<String, dynamic> data) {
    final sessionKey = auth.Config.getInstance().activeSessionKey ?? '';
    return ChannelPrivateData(
      sessionKey: sessionKey,
      channelId: data['id'] as String? ?? '',
      version: (data['version'] as num?)?.toInt() ?? 0,
      source: data['source'] as String? ?? '',
      unreadCount: (data['unreadCount'] as num?)?.toInt() ?? 0,
      lastSeenMessageId: data['lastSeenMessageId'] as String? ?? '',
      pinned: data['pinned'] as bool? ?? false,
      sort: (data['sort'] as num?)?.toInt() ?? 0,
    );
  }

  // Usecase execution methods
  Future<void> _setAliasName({
    required String userId,
    String? aliasName,
  }) async {
    final output = await getIt<CreateAliasNameForUpdateUseCase>().execute(
      CreateAliasNameForUpdateInput(
        userId: userId,
        newAliasName: aliasName ?? '',
      ),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _retrySetAliasName(UserPrivateData data) async {
    final output = await getIt<RetrySetAliasNameDataForUpdateUseCase>().execute(
      RetrySetAliasNameDataForUpdateInput(userPrivateData: data),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _insertAliasName(UserPrivateData data) async {
    await getIt<InsertAliasNameUseCase>().execute(
      InsertAliasNameInput(userPrivateData: data),
    );
  }

  Future<void> _addPinChannel({
    required String channelId,
    required bool isPin,
  }) async {
    final output = await getIt<CreatePinChannelForUpdateUseCase>().execute(
      CreatePinChannelForUpdateInput(channelId: channelId, isPin: isPin),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _retryPinChannel(ChannelPrivateData data) async {
    final output = await getIt<RetryPinChannelDataForUpdateUseCase>().execute(
      RetryPinChannelDataForUpdateInput(channelPrivateData: data),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _unPinChannel(String channelId) async {
    final channel = await getIt<GetChannelIdPrivateDataUseCase>().execute(
      GetChannelIdPrivateDataUseCaseInput(channelId),
    );

    // Early return if channel data is null
    if (channel.data == null) {
      return;
    }

    final output = await getIt<UnPinChannelDataForUpdateUseCase>().execute(
      UnPinChannelDataForUpdateInput(channelId: channelId),
    );
    if (output.cloudEvent != null) {
      getIt<WebSocketManager>().sendMessage(output.cloudEvent!);
    }
  }

  Future<void> _updatePinChannel(ChannelPrivateData data) async {
    await getIt<UpdatePinChannelUseCase>().execute(
      UpdatePinChannelInput(channelPrivateData: data),
    );
  }
}
