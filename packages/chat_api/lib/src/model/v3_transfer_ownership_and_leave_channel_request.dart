//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_transfer_ownership_and_leave_channel_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3TransferOwnershipAndLeaveChannelRequest {
  /// Returns a new [V3TransferOwnershipAndLeaveChannelRequest] instance.
  V3TransferOwnershipAndLeaveChannelRequest({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3TransferOwnershipAndLeaveChannelRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + userId.hashCode;

  factory V3TransferOwnershipAndLeaveChannelRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3TransferOwnershipAndLeaveChannelRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3TransferOwnershipAndLeaveChannelRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
