//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

enum V3AudioTextToSpeechEncodingEnum {
  @JsonValue(0)
  AUDIO_ENCODING_UNSPECIFIED('0'),
  @JsonValue(1)
  LINEAR16('1'),
  @JsonValue(2)
  MP3('2'),
  @JsonValue(3)
  OGG_OPUS('3'),
  @JsonValue(4)
  MULAW('4'),
  @JsonValue(5)
  ALAW('5');

  const V3AudioTextToSpeechEncodingEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
