//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_user_key_auth_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateUserKeyAuthFlowRequest {
  /// Returns a new [V3InitiateUserKeyAuthFlowRequest] instance.
  V3InitiateUserKeyAuthFlowRequest({
    this.userKey,
    this.reqChallenge,
    this.deviceId,
  });

  @JsonKey(
    name: r'userKey',
    required: false,
    includeIfNull: false,
  )
  final String? userKey;

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateUserKeyAuthFlowRequest &&
          other.userKey == userKey &&
          other.reqChallenge == reqChallenge &&
          other.deviceId == deviceId;

  @override
  int get hashCode =>
      userKey.hashCode + reqChallenge.hashCode + deviceId.hashCode;

  factory V3InitiateUserKeyAuthFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateUserKeyAuthFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateUserKeyAuthFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
