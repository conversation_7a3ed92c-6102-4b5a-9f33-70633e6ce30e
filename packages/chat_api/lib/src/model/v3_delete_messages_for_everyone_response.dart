//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_delete_messages_for_everyone_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3DeleteMessagesForEveryoneResponse {
  /// Returns a new [V3DeleteMessagesForEveryoneResponse] instance.
  V3DeleteMessagesForEveryoneResponse({
    this.ok,
    this.error,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3DeleteMessagesForEveryoneResponse &&
          other.ok == ok &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + error.hashCode;

  factory V3DeleteMessagesForEveryoneResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3DeleteMessagesForEveryoneResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3DeleteMessagesForEveryoneResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
