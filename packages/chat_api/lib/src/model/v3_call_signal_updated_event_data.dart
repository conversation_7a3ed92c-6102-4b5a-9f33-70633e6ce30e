//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_rtc_session_description.dart';
import 'package:chat_api/src/model/call_signal_updated_event_data_recipient_info.dart';
import 'package:chat_api/src/model/v3_call_signal_intent_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_call_signal_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CallSignalUpdatedEventData {
  /// Returns a new [V3CallSignalUpdatedEventData] instance.
  V3CallSignalUpdatedEventData({
    this.callId,
    this.userId,
    this.deviceId,
    this.recipientInfo,
    this.rtcIceCandidate,
    this.rtcSessionDescription,
    this.intent,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'callId',
    required: false,
    includeIfNull: false,
  )
  final String? callId;

  @Json<PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @JsonKey(
    name: r'recipientInfo',
    required: false,
    includeIfNull: false,
  )
  final CallSignalUpdatedEventDataRecipientInfo? recipientInfo;

  @JsonKey(
    name: r'rtcIceCandidate',
    required: false,
    includeIfNull: false,
  )
  final String? rtcIceCandidate;

  @JsonKey(
    name: r'rtcSessionDescription',
    required: false,
    includeIfNull: false,
  )
  final V3RTCSessionDescription? rtcSessionDescription;

  @JsonKey(
    name: r'intent',
    required: false,
    includeIfNull: false,
  )
  final V3CallSignalIntentEnum? intent;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CallSignalUpdatedEventData &&
          other.callId == callId &&
          other.userId == userId &&
          other.deviceId == deviceId &&
          other.recipientInfo == recipientInfo &&
          other.rtcIceCandidate == rtcIceCandidate &&
          other.rtcSessionDescription == rtcSessionDescription &&
          other.intent == intent;

  @override
  int get hashCode =>
      callId.hashCode +
      userId.hashCode +
      deviceId.hashCode +
      recipientInfo.hashCode +
      rtcIceCandidate.hashCode +
      rtcSessionDescription.hashCode +
      intent.hashCode;

  factory V3CallSignalUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3CallSignalUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CallSignalUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
