//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_assertion_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_confirm_account_deletion_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConfirmAccountDeletionRequest {
  /// Returns a new [V3ConfirmAccountDeletionRequest] instance.
  V3ConfirmAccountDeletionRequest({
    this.reqId,
    this.reqVerifier,
    this.assertion,
  });

  @Json<PERSON><PERSON>(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'assertion',
    required: false,
    includeIfNull: false,
  )
  final CommonAssertionResult? assertion;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ConfirmAccountDeletionRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.assertion == assertion;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + assertion.hashCode;

  factory V3ConfirmAccountDeletionRequest.fromJson(Map<String, dynamic> json) =>
      _$V3ConfirmAccountDeletionRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ConfirmAccountDeletionRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
