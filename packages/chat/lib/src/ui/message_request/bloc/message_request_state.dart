part of 'message_request_bloc.dart';

@freezed
sealed class MessageRequestState extends BaseBlocState
    with _$MessageRequestState {
  const MessageRequestState._();

  factory MessageRequestState({
    @Default([]) List<data.Channel> channels,
    @Default(false) bool closedWarning,
    @Default({}) Map<String, String?> translatedMessages,
    @Default(false) bool noMoreItems,
  }) = _MessageRequestState;
}
