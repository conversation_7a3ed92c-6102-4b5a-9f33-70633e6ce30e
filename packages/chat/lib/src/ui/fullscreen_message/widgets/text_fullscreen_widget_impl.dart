import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class TextFullscreenWidgetImpl extends BaseFullscreenWidget {
  TextFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    required this.messageContent,
    this.quoteMessageItem,
    this.onQuoteMessageClicked,
    this.onButtonTranslateClick,
    this.hasShimmerEffect = false,
    super.key,
  });

  final String messageContent;
  final QuoteMessage? quoteMessageItem;
  final void Function()? onQuoteMessageClicked;
  final void Function(MessageItem messageItem)? onButtonTranslateClick;
  final bool hasShimmerEffect;

  @override
  Widget build(BuildContext context) {
    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    final translateButtonStatus =
        messageItem.translateButtonStatus != TranslateButtonStatus.active;

    return ViewFullScreenTextWidget(
      interface: this,
      messageItem: messageItem,
      messageContent: messageContent,
      emojiList: listReactions,
      hasShimmerEffect: hasShimmerEffect,
      translateButtonStatus: translateButtonStatus,
      totalReactions: ValueNotifier(totalReactions),
      quickReact: quickReact,
      onQuoteClicked: onQuoteMessageClicked,
      quoteMessage: quoteMessageItem,
      onUsernameClicked: (String username) {
        onUsernameClicked(context, username);
      },
      mentionRegex: GlobalConfig.mentionRegex,
      transitionStatus: messageItem.translateButtonStatus,
    );
  }

  @override
  void onTranslateButtonClicked() {
    onButtonTranslateClick?.call(messageItem);
  }
}
