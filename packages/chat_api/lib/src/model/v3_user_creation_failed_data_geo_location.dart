//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_creation_failed_data_geo_location.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserCreationFailedDataGeoLocation {
  /// Returns a new [V3UserCreationFailedDataGeoLocation] instance.
  V3UserCreationFailedDataGeoLocation({
    this.countryCode,
  });

  @JsonKey(
    name: r'countryCode',
    required: false,
    includeIfNull: false,
  )
  final String? countryCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserCreationFailedDataGeoLocation &&
          other.countryCode == countryCode;

  @override
  int get hashCode => countryCode.hashCode;

  factory V3UserCreationFailedDataGeoLocation.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserCreationFailedDataGeoLocationFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserCreationFailedDataGeoLocationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
