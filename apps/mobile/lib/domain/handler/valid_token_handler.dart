import 'dart:async';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';
import '../../utils/config_data_utils.dart';

class ValidTokenHandler {
  static bool bottomSheetInvalidTokenShowed = false;

  StreamSubscription? _subscription;

  ValidTokenHandler() {
    bottomSheetInvalidTokenShowed = false;
  }

  void register(BuildContext context) {
    _subscription = getIt<AppEventBus>().on<OnTokenInvalid>().listen((event) {
      _onTokenInvalid(context);
    });
  }

  void cancel() {
    _subscription?.cancel();
    _subscription = null;
  }

  void _onTokenInvalid(BuildContext context) async {
    getIt<AppBloc>().add(AppInvalidToken());
    ConfigDataUtils.clearConfigData();

    await GetIt.I<AppRouter>().replaceAll(
      [WelcomeLastRoute(invalidToken: true)],
      updateExistingRoutes: false,
    );
  }

  static void showInvalidTokenSnackBar(BuildContext context) async {
    if (bottomSheetInvalidTokenShowed) return;

    bottomSheetInvalidTokenShowed = true;

    final appLocalizations = getIt<AppLocalizations>();

    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (_) {
        return ui.SnackBarUtilV2.showFloatingSnackBar(
          context: context,
          content: appLocalizations.sessionHasExpiredPleaseLogInAgain,
          snackBarType: SnackBarType.warning,
          action: SnackBarAction(
            label: appLocalizations.login,
            onPressed: () {
              GetIt.I<AppRouter>().push(AuthRoute());
            },
          ),
        );
      },
    );
  }
}
