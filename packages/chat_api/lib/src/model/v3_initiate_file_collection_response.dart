//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_initiate_file_collection_response_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_file_collection_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateFileCollectionResponse {
  /// Returns a new [V3InitiateFileCollectionResponse] instance.
  V3InitiateFileCollectionResponse({
    this.ok,
    this.error,
    this.data,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3InitiateFileCollectionResponseData? data;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateFileCollectionResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + data.hashCode + includes.hashCode;

  factory V3InitiateFileCollectionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateFileCollectionResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateFileCollectionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
