import 'dart:io';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart';
import '../../../domain/event_bus/message/resend_attachment_event.dart';
import '../../../domain/usecase/message/delete_local_attachment_use_case.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class ImagesFullscreenWidgetImpl extends BaseFullscreenWidget {
  ImagesFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    this.onPageImageChange,
    super.key,
    this.initialIndex = 0,
  });

  final int? initialIndex;
  final void Function(int index)? onPageImageChange;

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      return const SizedBox();
    }

    final sortedAttachments = List.from(message.mediaAttachments)
      ..sort((a, b) {
        final aIndex = a.photo?.fileRef ?? '';
        final bIndex = b.photo?.fileRef ?? '';
        return aIndex.compareTo(bIndex);
      });

    final Map<String, Attachment> attachmentMap = {
      for (final attachment in sortedAttachments)
        if (attachment.photo?.attachmentId != null)
          attachment.photo!.attachmentId!: attachment,
    };

    final imageAttachments = sortedAttachments.map((attachment) {
      final photo = attachment.photo ?? MediaObject.nullObject();
      return ImageAttachment(
        attachmentId:
            photo.attachmentId ?? photo.fileId ?? RandomUtils.randomUlId(),
        attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
        width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
        height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
      );
    }).toList();

    final ValueNotifier<bool> isHidePartial = ValueNotifier(false);

    void onHidePartial() {
      isHidePartial.value = !isHidePartial.value;
    }

    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false;

    void discardAttachment(Attachment attachment) {
      Navigator.pop(context);
      final output = GetIt.instance.get<DeleteLocalAttachmentUseCase>().execute(
            DeleteLocalAttachmentInput(
              attachmentRef: attachment.ref,
              attachmentId: attachment.attachmentId,
            ),
          );
      if (output.message != null) {
        AppEventBus.publish(MessageCreatedEvent(message: output.message));
      }
    }

    void resendAttachment(Attachment attachment, String messageId) {
      AppEventBus.publish(PopToChannelViewEvent());
      discardAttachment(attachment);
      AppEventBus.publish(
        ResendAttachmentEvent(attachment: attachment, messageId: messageId),
      );
    }

    Future<bool> isHiddenResend(String fileRef) async {
      final filePath = await FileUtils.getImagePathFromFileRef(
        messageRef: message.ref!,
        fileRef: fileRef,
      );

      if (filePath == null) return true;
      final file = File(filePath);
      return !(await file.exists());
    }

    return ViewFullScreenImages(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      quickReact: quickReact,
      imageAttachments: imageAttachments,
      caption: '',
      onPageImageChange: onPageImageChange,
      totalReactions: ValueNotifier(totalReactions),
      isHidePartial: isHidePartial,
      onHidePartial: onHidePartial,
      initialImageIndex: initialIndex,
      onLongPress: (messageItem, index) async {
        final attachmentId = imageAttachments[index].attachmentId;

        final attachment = attachmentMap[attachmentId];
        if (attachment == null) return;

        if (attachment.attachmentStatus == AttachmentStatusEnum.FAILURE) {
          final hidden = await isHiddenResend(attachment.photo?.fileRef ?? '');
          BottomSheetUtil.showFullScreenMessageOptionsFailed(
            context: context,
            messageItem: messageItem,
            isHiddenResend: hidden,
            onResend: (_) =>
                resendAttachment(attachment, messageItem.messageId),
            onDiscard: (_) => discardAttachment(attachment),
          );
        }
      },
    );
  }
}
