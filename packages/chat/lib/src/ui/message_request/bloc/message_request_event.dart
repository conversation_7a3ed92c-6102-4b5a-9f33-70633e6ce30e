part of 'message_request_bloc.dart';

class MessageRequestEvent extends BaseBlocEvent {
  const MessageRequestEvent();
}

class InitMessageRequestEvent extends MessageRequestEvent {
  const InitMessageRequestEvent();
}

class UpdateListMessageRequestEvent extends MessageRequestEvent {
  const UpdateListMessageRequestEvent({
    required this.channels,
  });

  final List<data.Channel> channels;
}

class AcceptMessageRequestEvent extends MessageRequestEvent {
  const AcceptMessageRequestEvent({
    required this.channelId,
    required this.workspaceId,
  });

  final String channelId;
  final String workspaceId;
}

class RejectMessageRequestEvent extends MessageRequestEvent {
  const RejectMessageRequestEvent({
    required this.channelId,
    required this.workspaceId,
  });

  final String channelId;
  final String workspaceId;
}

class UpdatedClosedWarningStatusEvent extends MessageRequestEvent {
  const UpdatedClosedWarningStatusEvent({
    required this.closedWarningStatus,
  });

  final bool closedWarningStatus;
}

class LoadMessageRequestEvent extends MessageRequestEvent {
  const LoadMessageRequestEvent();
}

class UpdateTranslatedMessagesEventV2 extends MessageRequestEvent {
  const UpdateTranslatedMessagesEventV2({
    required this.translatedMessages,
  });

  final Map<String, String?> translatedMessages;
}

/// Event để load thêm message requests với phân trang
/// Tương tự như LoadMoreChannelsEvent trong ChannelsBloc
class LoadMoreMessageRequestEvent extends MessageRequestEvent {
  const LoadMoreMessageRequestEvent({
    required this.nextPageToken,
  });

  final String nextPageToken;
}
