import 'package:chat_api/chat_api.dart' as chat_api;

import 'data_router_event_data.dart';

/// Wrapper class for DM Channel Created Event Data
/// Extends chat_api V3DMChannelCreatedEventData to avoid direct dependency
class IncomingFriendRequestAcceptedEventData extends chat_api
    .V3IncomingMessageRequestAcceptedEventData implements DataRouterEventData {
  /// Creates a new [IncomingFriendRequestAcceptedEventData] instance
  IncomingFriendRequestAcceptedEventData({
    super.channel,
    super.includes,
  });

  /// Creates instance from JSON data
  factory IncomingFriendRequestAcceptedEventData.fromJson(
    Map<String, dynamic> json,
  ) {
    return IncomingFriendRequestAcceptedEventData(
      channel: json['channel'] != null
          ? chat_api.V3Channel.fromJson(json['channel'] as Map<String, dynamic>)
          : null,
      includes: json['includes'] != null
          ? chat_api.V3DataInclude.fromJson(
              json['includes'] as Map<String, dynamic>,
            )
          : null,
    );
  }

  @override
  bool get isValid {
    return channel?.channelId?.isNotEmpty == true &&
        channel?.workspaceId?.isNotEmpty == true &&
        channel?.type == chat_api.V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM;
  }

  @override
  String toString() {
    return 'IncomingFriendRequestAcceptedEventData{channelId: ${channel?.channelId}, workspaceId: ${channel?.workspaceId}, userId: ${channel?.userId}}';
  }

  /// Get channel ID from the event data
  String? get channelId => channel?.channelId;

  /// Get workspace ID from the event data
  String? get workspaceId => channel?.workspaceId;

  /// Get recipient user ID from the event data (not available in V3Channel)
  String? get recipientId => null;

  /// Get DM channel ID from the event data (not available in V3Channel)
  String? get dmChannelId => null;

  /// Get channel owner user ID from the event data
  String? get channelOwnerUserId => channel?.userId;

  /// Check if this is a DM channel
  bool get isDMChannel =>
      channel?.type == chat_api.V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM;
}
