import 'dart:async';

import 'package:injectable/injectable.dart' hide Order;
import 'package:objectbox/objectbox.dart' as ob show Order;
import 'package:rxdart/rxdart.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../data/database/generated/objectbox.g.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/visited_profile_repository.dart';

@LazySingleton(as: VisitedProfileRepository)
class VisitedProfileRepositoryImpl extends BaseRepository<VisitedProfile>
    implements VisitedProfileRepository {
  final SessionBox<VisitedProfile> _sessionBox;

  VisitedProfileRepositoryImpl(this._sessionBox) : super(_sessionBox);

  @override
  VisitedProfile? getByVisitedUserId(String visitedUserId) {
    try {
      return getFirst(VisitedProfile_.visitedUserIdField.equals(visitedUserId));
    } catch (e) {
      return null;
    }
  }

  @override
  List<VisitedProfile> getAllForCurrentSession() {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: ob.Order.descending)
          .build();

      final results = query.find();
      query.close();

      return results;
    } catch (e) {
      throw Exception(
        'Failed to get all visited profiles for current session: $e',
      );
    }
  }

  @override
  Stream<List<VisitedProfile>> watchVisitedProfiles() {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: ob.Order.descending)
          .watch(triggerImmediately: true)
          .debounceTime(GlobalConfig.dbDebounceTime);

      return query.map((query) => query.find());
    } catch (e) {
      throw Exception('Failed to watch visited profiles: $e');
    }
  }

  @override
  Stream<VisitedProfile?> watchByVisitedUserId(String visitedUserId) {
    try {
      return watch(VisitedProfile_.visitedUserIdField.equals(visitedUserId))
          .map((query) => query.findFirst());
    } catch (e) {
      throw Exception('Failed to watch visited profile[$visitedUserId]: $e');
    }
  }

  @override
  bool deleteByVisitedUserId(String visitedUserId) {
    try {
      final profile = getByVisitedUserId(visitedUserId);
      if (profile != null) {
        _sessionBox.remove(profile.id);
        return true;
      }
      return false;
    } catch (e) {
      throw Exception(
        'Failed to delete visited profile by visited user ID[$visitedUserId]: $e',
      );
    }
  }

  @override
  int markAllAsRead() {
    try {
      final profiles = getAllForCurrentSession();
      int updatedCount = 0;

      for (final profile in profiles) {
        if (!profile.isRead) {
          profile.markAsRead();
          put(profile);
          updatedCount++;
        }
      }
      putMany(profiles);
      return updatedCount;
    } catch (e) {
      throw Exception('Failed to mark all visited profiles as read: $e');
    }
  }

  @override
  bool markAsReadByVisitedUserId(String visitedUserId) {
    try {
      final profile = getByVisitedUserId(visitedUserId);
      if (profile == null) return false;

      if (!profile.isRead) {
        profile.markAsRead();
        put(profile);
      }

      return true;
    } catch (e) {
      throw Exception(
        'Failed to mark visited profile as read[$visitedUserId]: $e',
      );
    }
  }

  @override
  bool markAsUnreadByVisitedUserId(String visitedUserId) {
    try {
      final profile = getByVisitedUserId(visitedUserId);
      if (profile == null) return false;

      if (profile.isRead) {
        profile.markAsUnread();
        put(profile);
      }

      return true;
    } catch (e) {
      throw Exception(
        'Failed to mark visited profile as unread[$visitedUserId]: $e',
      );
    }
  }

  @override
  int getUnreadCount() {
    try {
      return _sessionBox.countWith(VisitedProfile_.isRead.equals(false));
    } catch (e) {
      throw Exception('Failed to get unread visited profiles count: $e');
    }
  }

  @override
  List<VisitedProfile> getRecent({int limit = 50}) {
    try {
      final allProfiles = getAllForCurrentSession();
      return allProfiles.take(limit).toList();
    } catch (e) {
      throw Exception('Failed to get recent visited profiles: $e');
    }
  }

  @override
  List<VisitedProfile> getByDateRange(DateTime startDate, DateTime endDate) {
    try {
      final condition = VisitedProfile_.updateTime.between(
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      );
      return _sessionBox.query(condition);
    } catch (e) {
      throw Exception('Failed to get visited profiles by date range: $e');
    }
  }

  @override
  bool exists(String visitedUserId) {
    try {
      return getByVisitedUserId(visitedUserId) != null;
    } catch (e) {
      throw Exception(
        'Failed to check visited profile existence[$visitedUserId]: $e',
      );
    }
  }

  @override
  void deleteAllBySessionKey(String sessionKey) {
    try {
      final query = _sessionBox.box
          .query(VisitedProfile_.sessionKey.equals(sessionKey))
          .build();

      query.remove();
      query.close();
    } catch (e) {
      throw Exception(
        'Failed to delete visited profiles by session key[$sessionKey]: $e',
      );
    }
  }

  @override
  int deleteAll() {
    try {
      final query = queryBuilder().build();
      final deletedCount = query.remove();
      query.close();

      return deletedCount;
    } catch (e) {
      throw Exception('Failed to delete all visited profiles: $e');
    }
  }

  @override
  VisitedProfile updateVisitTime(String visitedUserId) {
    try {
      final existing = getByVisitedUserId(visitedUserId);

      if (existing != null) {
        existing.updateVisitTime();
        existing.markAsUnread();
        put(existing);
        return existing;
      } else {
        final newProfile = VisitedProfile.create(
          sessionKey: _sessionBox.activeSessionKey,
          visitedUserId: visitedUserId,
          isRead: false,
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
        );
        put(newProfile);
        return newProfile;
      }
    } catch (e) {
      throw Exception('Failed to update visit time[$visitedUserId]: $e');
    }
  }

  @override
  int insertVisitedProfile(VisitedProfile item) {
    return _sessionBox.put(item);
  }

  @override
  Future<void> insertAllVisitedProfile(List<VisitedProfile> items) async {
    await _sessionBox.putMany(items);
  }

  @override
  List<VisitedProfile> getAllVisitedProfiles() {
    try {
      final query = queryBuilder()
          .order(VisitedProfile_.updateTime, flags: ob.Order.descending)
          .build();

      final results = query.find();
      query.close();

      return results;
    } catch (e) {
      throw Exception(
        'Failed to get all visited profiles for current session: $e',
      );
    }
  }

  @override
  Future<void> syncVisitedProfiles(List<VisitedProfile> items) async {
    messageBox.store.runInTransaction(TxMode.write, () {
      final oldProfiles = getAll();
      final oldMap = {
        for (var item in oldProfiles) item.visitedUserIdField: item,
      };

      // Gán id từ oldList cho newList nếu trùng
      for (var newItem in items) {
        final oldItem = oldMap[newItem.visitedUserIdField];
        if (oldItem != null) {
          newItem.id = oldItem.id;
        }
      }

      // Lọc ra các item cần xoá (có trong oldList nhưng không còn trong newList)
      final namesInNewList = items.map((e) => e.visitedUserIdField).toSet();
      final itemsToDelete = oldProfiles
          .where((e) => !namesInNewList.contains(e.visitedUserIdField))
          .toList();

      super.putMany(items);
      super.removeMany(itemsToDelete.map((e) => e.id).toList());
    });
  }
}
