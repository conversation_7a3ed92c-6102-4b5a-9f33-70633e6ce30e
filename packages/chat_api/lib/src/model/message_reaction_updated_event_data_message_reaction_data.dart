//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'message_reaction_updated_event_data_message_reaction_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class MessageReactionUpdatedEventDataMessageReactionData {
  /// Returns a new [MessageReactionUpdatedEventDataMessageReactionData] instance.
  MessageReactionUpdatedEventDataMessageReactionData({
    this.emoji,
    this.total,
  });

  @JsonKey(
    name: r'emoji',
    required: false,
    includeIfNull: false,
  )
  final String? emoji;

  @JsonKey(
    name: r'total',
    required: false,
    includeIfNull: false,
  )
  final int? total;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MessageReactionUpdatedEventDataMessageReactionData &&
          other.emoji == emoji &&
          other.total == total;

  @override
  int get hashCode => emoji.hashCode + total.hashCode;

  factory MessageReactionUpdatedEventDataMessageReactionData.fromJson(
          Map<String, dynamic> json) =>
      _$MessageReactionUpdatedEventDataMessageReactionDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$MessageReactionUpdatedEventDataMessageReactionDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
