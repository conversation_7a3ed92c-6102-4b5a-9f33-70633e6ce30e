import 'dart:async';

import 'package:injectable/injectable.dart' hide Order;
import 'package:shared/shared.dart';

import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/member_repository.dart';
import '../database/entities/member.dart';
import '../database/generated/objectbox.g.dart';
import '../local/session_box.dart';
import '../models/resource.dart';

/// Implementation of MemberRepository using SessionBox
/// Handles Member entity operations with automatic session filtering and proper caching
@LazySingleton(as: MemberRepository)
class MemberRepositoryImpl extends BaseRepository<Member>
    implements MemberRepository {
  MemberRepositoryImpl(SessionBox<Member> sessionBox) : super(sessionBox);

  @override
  Stream<List<Member>> watchMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    // Use SessionBox watch with automatic session filtering
    // This replaces the manual sessionKey filtering from the chat package
    return watch(
      Member_.workspaceId
          .equals(workspaceId)
          .and(Member_.channelIdField.equals(channelId))
          .and(
            Member_.role.notNull().and(Member_.role.notEquals('')),
          ), // Filter out members without roles
    ).map((query) {
      final members = query.find();
      return members;
    });
  }

  @override
  Stream<Member?> watchMember({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) {
    // Use ObjectBox native watch for specific member
    return watch(
      Member_.workspaceId
          .equals(workspaceId)
          .and(Member_.channelIdField.equals(channelId))
          .and(Member_.userIdField.equals(userId)),
    ).map((query) => query.findFirst());
  }

  @override
  Future<List<Member>> getMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      // Use SessionBox for direct query
      final members = query(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId)),
      );

      // Sort by updateTime descending
      members.sort(
        (a, b) => (b.updateTime ?? DateTime.now())
            .compareTo(a.updateTime ?? DateTime.now()),
      );

      return members;
    } catch (e) {
      Log.e('Error getting members for channel $workspaceId:$channelId: $e');
      return [];
    }
  }

  @override
  Future<Member?> getMember({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    try {
      // Use SessionBox for direct query
      return getFirst(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId))
            .and(Member_.userIdField.equals(userId)),
      );
    } catch (e) {
      Log.e('Error getting member $workspaceId:$channelId:$userId: $e');
      return null;
    }
  }

  @override
  Future<bool> memberExists({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    try {
      // Use SessionBox for direct query
      final member = getFirst(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId))
            .and(Member_.userIdField.equals(userId)),
      );
      return member != null;
    } catch (e) {
      Log.e(
        'Error checking member existence $workspaceId:$channelId:$userId: $e',
      );
      return false;
    }
  }

  @override
  Future<Resource<bool>> updateMemberNickname({
    required String workspaceId,
    required String channelId,
    required String userId,
    required String nickname,
  }) async {
    try {
      // Find and update member using SessionBox
      final member = getFirst(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId))
            .and(Member_.userIdField.equals(userId)),
      );

      if (member != null) {
        // Update nickname and save - ObjectBox will automatically notify watchers
        final updatedMember = member.copyWith(nickname: nickname);
        put(updatedMember);

        return Resource.success(true);
      } else {
        return Resource.error('Member not found');
      }
    } catch (e) {
      return Resource.error('Failed to update member nickname: $e');
    }
  }

  @override
  Future<Resource<Member>> assignMemberAsAdmin({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    // TODO: Implement assignMemberAsAdmin with remote API call
    // This requires integration with MemberService for remote operations
    throw UnimplementedError(
      'assignMemberAsAdmin requires remote API integration - use MemberDataOperation instead',
    );
  }

  @override
  Future<Resource<bool>> leaveChannel({
    required String workspaceId,
    required String channelId,
  }) async {
    // TODO: Implement leaveChannel with remote API call
    // This requires integration with MemberService for remote operations
    throw UnimplementedError(
      'leaveChannel requires remote API integration - use MemberService instead',
    );
  }

  @override
  Future<Resource<List<Member>>> loadMembersForChannel({
    required String workspaceId,
    required String channelId,
    bool forceRefresh = false,
  }) async {
    try {
      // For SessionBox pattern, we only handle local data
      // Remote loading should be handled by MemberService
      final members = query(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId)),
      );

      // Sort by updateTime descending
      members.sort(
        (a, b) => (b.updateTime ?? DateTime.now())
            .compareTo(a.updateTime ?? DateTime.now()),
      );

      return Resource.success(members);
    } catch (e) {
      return Resource.error('Failed to load members: $e');
    }
  }

  @override
  Future<Resource<List<Member>>> syncMembers({
    String? updateTimeAfter,
    String? workspaceId,
    String? channelId,
  }) async {
    // TODO: Implement syncMembers with remote API call
    // This requires integration with MemberService for remote sync operations
    throw UnimplementedError(
      'syncMembers requires remote API integration - use MemberService instead',
    );
  }

  @override
  Future<void> insertMember(Member member) async {
    try {
      // Use SessionBox for direct insert - ObjectBox will automatically notify watchers
      put(member);
    } catch (e) {
      Log.e('Error inserting member ${member.memberId}: $e');
      rethrow;
    }
  }

  @override
  Future<void> insertMembers(List<Member> members) async {
    try {
      // Use SessionBox for direct insert - ObjectBox will automatically notify watchers
      putMany(members);
    } catch (e) {
      Log.e('Error inserting ${members.length} members: $e');
      rethrow;
    }
  }

  @override
  Future<bool> deleteMember({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    try {
      // Use SessionBox for direct delete
      final member = getFirst(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId))
            .and(Member_.userIdField.equals(userId)),
      );

      if (member != null) {
        // ObjectBox will automatically notify watchers
        final success = remove(member.id);
        return success;
      }
      return false;
    } catch (e) {
      Log.e('Error deleting member $workspaceId:$channelId:$userId: $e');
      return false;
    }
  }

  @override
  Future<void> clearAllMembers() async {
    try {
      // Use SessionBox for direct delete - ObjectBox will automatically notify watchers
      removeAll();
    } catch (e) {
      Log.e('Error clearing all members: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      // Use SessionBox for direct delete - ObjectBox will automatically notify watchers
      final members = query(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId)),
      );

      final ids = members.map((m) => m.id).toList();
      removeMany(ids);
    } catch (e) {
      Log.e('Error clearing members for channel $workspaceId:$channelId: $e');
      rethrow;
    }
  }

  @override
  bool deleteMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    try {
      final query = messageBox
          .queryWith(
            Member_.workspaceId
                .equals(workspaceId)
                .and(Member_.channelIdField.equals(channelId)),
          )
          .build();
      final numberOfMembers = query.remove();
      Log.e(
        name: 'MemberRepositoryImpl.deleteMembersForChannel',
        'Deleted $numberOfMembers members',
      );
      query.close();
      return numberOfMembers > 0;
    } catch (e) {
      Log.e('Error deleting members for channel $workspaceId:$channelId: $e');
      rethrow;
    }
  }

  @override
  void deleteAllBySessionKey({required String sessionKey}) {
    try {
      final query =
          messageBox.box.query(Member_.sessionKey.equals(sessionKey)).build();
      final numberOfMembers = query.remove();
      Log.e(
        name: 'MemberRepositoryImpl.deleteMembersForChannel',
        'Deleted $numberOfMembers members',
      );
      query.close();
    } catch (e) {
      rethrow;
    }
  }

  @override
  List<Member> getAllMembers({
    required String workspaceId,
    required String channelId,
  }) {
    final members = query(
      Member_.workspaceId
          .equals(workspaceId)
          .and(Member_.channelIdField.equals(channelId)),
    );

    // Sort by updateTime descending
    members.sort(
      (a, b) => (b.updateTime ?? DateTime.now())
          .compareTo(a.updateTime ?? DateTime.now()),
    );

    return members;
  }

  @override
  Future<List<Member>> getMembersWithRole({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      // Use SessionBox for direct query
      final members = query(
        Member_.workspaceId
            .equals(workspaceId)
            .and(Member_.channelIdField.equals(channelId))
            .and(
              Member_.role.notNull().and(Member_.role.notEquals('')),
            ),
      );

      // Sort by updateTime descending
      members.sort(
        (a, b) => (b.updateTime ?? DateTime.now())
            .compareTo(a.updateTime ?? DateTime.now()),
      );

      return members;
    } catch (e) {
      Log.e('Error getting members for channel $workspaceId:$channelId: $e');
      return [];
    }
  }
}
