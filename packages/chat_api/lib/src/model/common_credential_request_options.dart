//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_public_key_credential_request_options.dart';
import 'package:json_annotation/json_annotation.dart';

part 'common_credential_request_options.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonCredentialRequestOptions {
  /// Returns a new [CommonCredentialRequestOptions] instance.
  CommonCredentialRequestOptions({
    this.reqId,
    this.credentialRequestOptions,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'credentialRequestOptions',
    required: false,
    includeIfNull: false,
  )
  final CommonPublicKeyCredentialRequestOptions? credentialRequestOptions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonCredentialRequestOptions &&
          other.reqId == reqId &&
          other.credentialRequestOptions == credentialRequestOptions;

  @override
  int get hashCode => reqId.hashCode + credentialRequestOptions.hashCode;

  factory CommonCredentialRequestOptions.fromJson(Map<String, dynamic> json) =>
      _$CommonCredentialRequestOptionsFromJson(json);

  Map<String, dynamic> toJson() => _$CommonCredentialRequestOptionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
