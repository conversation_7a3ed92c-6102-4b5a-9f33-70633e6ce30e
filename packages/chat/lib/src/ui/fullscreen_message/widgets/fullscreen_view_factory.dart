import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../chat.dart';
import '../../../data/repositories/database/entities/translated_result.dart';
import '../../../data/repositories/extensions/message_extension.dart';
import '../../message_list/message_translate_state.dart';
import '../../message_list/widgets/message_view_factory.dart';
import 'file_fullscreen_widget_impl.dart';
import 'images_fullscreen_widget_impl.dart';
import 'invitation_fullscreen_widget_impl.dart';
import 'link_fullscreen_widget_impl.dart';
import 'location_fullscreen_widget_impl.dart';
import 'sticker_fullscreen_widget_impl.dart';
import 'text_fullscreen_widget_impl.dart';
import 'video_fullscreen_widget_impl.dart';
import 'ziishort_fullscreen_widget_impl.dart';
import 'ziivoice_fullscreen_widget_impl.dart';

class FullscreenViewFactory {
  static Widget buildMessageView({
    required Message message,
    TranslatedResult? translatedResult,
    required Member? member,
    required ChatUser? user,
    required String? recipientId,
    required bool is24HourFormat,
    int? initialAttachmentIndex,
    ui.QuoteMessage? quoteMessageItem,
    void Function()? onQuoteMessageClicked,
    void Function(int index)? onPageImageChange,
    void Function(ui.MessageItem messageItem)? onButtonTranslateClick,
    AppLocalizations? appLocalizations,
    String? appLocale,
  }) {
    final AppLocalizations appLocalizations = GetIt.I.get<AppLocalizations>();
    if (message.content?.contains('@all') ?? false) {
      message = message.copyWith(
        content: message.content?.replaceAll(
          '@all',
          '@${appLocalizations.all}',
        ),
      );
    }

    switch (message.messageViewType) {
      case MessageViewType.text:
      case MessageViewType.link:
        return buildTranslatableMessageWidget(
          translatedResult,
          message,
          member,
          user,
          recipientId,
          appLocale,
          appLocalizations,
          is24HourFormat,
          quoteMessageItem,
          onQuoteMessageClicked,
          onButtonTranslateClick,
        );
      default:
        final widgetBuilder = _messageBuilders[message.messageViewType];
        return widgetBuilder != null
            ? widgetBuilder(
                message,
                member,
                user,
                recipientId,
                initialAttachmentIndex,
                quoteMessageItem,
                onQuoteMessageClicked,
                onPageImageChange,
                appLocalizations,
                appLocale,
                is24HourFormat,
              )
            : Text('unknown');
    }
  }

  static Widget buildTranslatableMessageWidget(
    TranslatedResult? translatedResult,
    Message message,
    Member? member,
    ChatUser? user,
    String? recipientId,
    String? appLocale,
    AppLocalizations appLocalizations,
    bool is24HourFormat,
    ui.QuoteMessage? quoteMessageItem,
    void Function()? onQuoteMessageClicked,
    void Function(ui.MessageItem messageItem)? onButtonTranslateClick,
  ) {
    const defaultTranslatedContent = '';
    final translatedContent =
        translatedResult?.translatedContent ?? defaultTranslatedContent;
    MessagesTranslateState? currentMessageState = null;

    late final ui.MessageItem messageItem;

    if (translatedResult == null) {
      messageItem = message.createMessageItem(
        member: member,
        user: user,
        recipientId: recipientId,
        hasEditedMessage: message.editTime != null,
        appLocale: appLocale,
        appLocalizations: appLocalizations,
        isFullScreenMessage: isFullScreenMessage,
        use24HourFormat: is24HourFormat,
      );
    } else {
      currentMessageState = MessageViewFactory.getTranslateStatuses(
        translatedResult,
        message.messageId,
      );

      messageItem = message.createMessageItem(
        member: member,
        user: user,
        recipientId: recipientId,
        hasEditedMessage: message.editTime != null,
        hasTranslate: currentMessageState.hasTranslate,
        translateStatus: currentMessageState.translateStatus,
        translateButtonStatus: currentMessageState.translateButtonStatus,
        appLocale: appLocale,
        appLocalizations: appLocalizations,
        isFullScreenMessage: isFullScreenMessage,
        use24HourFormat: is24HourFormat,
      );
    }

    final messageContent = translatedContent.isEmpty ||
            currentMessageState?.translateButtonStatus ==
                ui.TranslateButtonStatus.inActive
        ? message.argsContent()!
        : translatedContent;

    final hasShimmerEffect =
        currentMessageState?.translateStatus == ui.TranslateStatus.translating;

    return message.messageViewType == MessageViewType.text
        ? TextFullscreenWidgetImpl(
            key: ValueKey(message.messageId),
            messageContent: messageContent,
            message: message,
            hasShimmerEffect: hasShimmerEffect,
            messageItem: messageItem,
            quoteMessageItem: quoteMessageItem,
            onQuoteMessageClicked: onQuoteMessageClicked,
            onButtonTranslateClick: onButtonTranslateClick,
          )
        : LinkFullscreenWidgetImpl(
            messageContent: messageContent,
            key: ValueKey(message.messageId),
            message: message,
            messageItem: messageItem,
            quoteMessageItem: quoteMessageItem,
            onQuoteMessageClicked: onQuoteMessageClicked,
            hasShimmerEffect: hasShimmerEffect,
            onButtonTranslateClick: onButtonTranslateClick,
          );
  }

  static final isFullScreenMessage = true;

  static final Map<
      MessageViewType,
      Widget Function(
        Message message,
        Member? member,
        ChatUser? user,
        String? recipientId,
        int? initialAttachmentIndex,
        ui.QuoteMessage? quoteMessageItem,
        void Function()? onQuoteMessageClicked,
        void Function(int index)? onPageImageChange,
        AppLocalizations? appLocalizations,
        String? appLocale,
        bool is24HourFormat,
      )> _messageBuilders = {
    MessageViewType.textOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) {
      return TextFullscreenWidgetImpl(
        key: ValueKey(message.messageId),
        messageContent: message.argsContent()!,
        message: message,
        messageItem: message.createMessageItem(
          member: member,
          user: user,
          recipientId: recipientId,
          hasEditedMessage: message.editTime != null,
          appLocale: appLocale,
          appLocalizations: appLocalizations,
          isFullScreenMessage: isFullScreenMessage,
          use24HourFormat: is24HourFormat,
        ),
        quoteMessageItem: quoteMessageItem,
        onQuoteMessageClicked: onQuoteMessageClicked,
      );
    },
    MessageViewType.linkOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        LinkFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            hasEditedMessage: message.editTime != null,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
          quoteMessageItem: quoteMessageItem,
          onQuoteMessageClicked: onQuoteMessageClicked,
        ),
    MessageViewType.invitation: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        InvitationFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            hasEditedMessage: message.editTime != null,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.invitationOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        InvitationFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            hasEditedMessage: message.editTime != null,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.sticker: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        StickerFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.stickerOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        StickerFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.file: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        FileFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.fileOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        FileFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.images: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        ImagesFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
          initialIndex: initialAttachmentIndex,
          onPageImageChange: onPageImageChange,
        ),
    MessageViewType.imagesOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        ImagesFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
          initialIndex: initialAttachmentIndex,
          onPageImageChange: onPageImageChange,
        ),
    MessageViewType.location: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        LocationFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.locationOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        LocationFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.ziiShort: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        ZiiShortFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.ziiShortsOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        ZiiShortFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.ziiVoice: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        ZiiVoiceFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.ziiVoiceOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        ZiiVoiceFullscreenWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.video: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        VideoWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
    MessageViewType.videoOwner: (
      message,
      member,
      user,
      recipientId,
      initialAttachmentIndex,
      quoteMessageItem,
      onQuoteMessageClicked,
      onPageImageChange,
      appLocalizations,
      appLocale,
      is24HourFormat,
    ) =>
        VideoWidgetImpl(
          key: ValueKey(message.messageId),
          message: message,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            appLocale: appLocale,
            appLocalizations: appLocalizations,
            isFullScreenMessage: isFullScreenMessage,
            use24HourFormat: is24HourFormat,
          ),
        ),
  };
}
