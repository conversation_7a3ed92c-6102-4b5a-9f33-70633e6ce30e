//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_creation_failed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelCreationFailedEventData {
  /// Returns a new [V3ChannelCreationFailedEventData] instance.
  V3ChannelCreationFailedEventData({
    this.workspaceId,
    this.channelId,
    this.reason,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @J<PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'reason',
    required: false,
    includeIfNull: false,
  )
  final String? reason;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelCreationFailedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.reason == reason;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + reason.hashCode;

  factory V3ChannelCreationFailedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3ChannelCreationFailedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ChannelCreationFailedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
