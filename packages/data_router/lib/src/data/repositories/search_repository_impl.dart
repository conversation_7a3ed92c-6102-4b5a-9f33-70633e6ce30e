import 'package:injectable/injectable.dart' as di;
import 'package:objectbox/objectbox.dart' as ob;

import '../../../data_router.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/search_repository.dart';
import '../database/generated/objectbox.g.dart';

@di.LazySingleton(as: SearchRepository)
class SearchRepositoryImpl extends BaseRepository<Search>
    implements SearchRepository {
  final SessionBox<SearchHistory> _searchHistorySessionBox;
  final SessionBox<Search> _searchSessionBox;
  final SessionBox<Channel> _channelSessionBox;

  SearchRepositoryImpl(
    this._searchSessionBox,
    this._searchHistorySessionBox,
    this._channelSessionBox,
  ) : super(_searchSessionBox);

  @override
  Box<Search> get searchBox => _searchSessionBox.box;

  @override
  Box<SearchHistory> get searchHistoryBox => _searchHistorySessionBox.box;

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  @override
  Future<List<Search>> getSearchesBySessionKey(String sessionKey) async {
    try {
      _logOperation('Getting searches by session key', sessionKey);
      final query = _searchSessionBox
          .queryWith(
            Search_.sessionKey.equals(sessionKey),
          )
          .order(Search_.timestamp, flags: ob.Order.descending)
          .build();
      final searches = query.find();
      query.close();
      _logOperation(
        'Found searches by session key',
        '$sessionKey: ${searches.length} searches',
      );
      return searches;
    } catch (e) {
      _logOperation('Error getting searches by session key', '$sessionKey: $e');
      return [];
    }
  }

  @override
  Future<List<Search>> getSearchesByUserId(String userId) async {
    try {
      _logOperation('Getting searches by user ID', userId);
      final query = _searchSessionBox
          .queryWith(
            Search_.userFieldId.equals(userId),
          )
          .order(Search_.timestamp, flags: ob.Order.descending)
          .build();
      final searches = query.find();
      query.close();
      _logOperation(
        'Found searches by user ID',
        '$userId: ${searches.length} searches',
      );
      return searches;
    } catch (e) {
      _logOperation('Error getting searches by user ID', '$userId: $e');
      return [];
    }
  }

  @override
  Future<Search?> getSearchByUserId(String userId) async {
    try {
      _logOperation('Getting search by user ID', userId);
      return _searchSessionBox.getFirstWith(Search_.userFieldId.equals(userId));
    } catch (e) {
      _logOperation('Error getting search by user ID', '$userId: $e');
      return null;
    }
  }

  @override
  Future<Search?> getSearchByChannelId(String channelId) async {
    try {
      _logOperation('Getting search by channel ID', channelId);
      return _searchSessionBox
          .getFirstWith(Search_.channelFieldId.equals(channelId));
    } catch (e) {
      _logOperation('Error getting search by channel ID', '$channelId: $e');
      return null;
    }
  }

  @override
  Future<List<Search>> getSearchesByChannelId(String channelId) async {
    try {
      _logOperation('Getting searches by channel ID', channelId);
      final query = _searchSessionBox
          .queryWith(
            Search_.channelFieldId.equals(channelId),
          )
          .order(Search_.timestamp, flags: ob.Order.descending)
          .build();
      final searches = query.find();
      query.close();
      _logOperation(
        'Found searches by channel ID',
        '$channelId: ${searches.length} searches',
      );
      return searches;
    } catch (e) {
      _logOperation('Error getting searches by channel ID', '$channelId: $e');
      return [];
    }
  }

  @override
  Future<List<Search>> getSearchesBySearchType(String searchType) async {
    try {
      _logOperation('Getting searches by search type', searchType);
      final query = _searchSessionBox
          .queryWith(
            Search_.searchType.equals(searchType),
          )
          .order(Search_.timestamp, flags: ob.Order.descending)
          .build();
      final searches = query.find();
      query.close();
      _logOperation(
        'Found searches by search type',
        '$searchType: ${searches.length} searches',
      );
      return searches;
    } catch (e) {
      _logOperation('Error getting searches by search type', '$searchType: $e');
      return [];
    }
  }

  @override
  Future<void> deleteSearchesBySessionKey(String sessionKey) async {
    try {
      _logOperation('Deleting searches by session key', sessionKey);
      final query = _searchSessionBox.box
          .query(Search_.sessionKey.equals(sessionKey))
          .build();
      final count = query.remove();
      query.close();
      _logOperation(
        'Deleted searches by session key',
        '$sessionKey: $count searches',
      );
    } catch (e) {
      _logOperation(
        'Error deleting searches by session key',
        '$sessionKey: $e',
      );
    }
  }

  @override
  Stream<List<Search>> watchSearchesBySessionKey(String sessionKey) {
    _logOperation('Watching searches by session key', sessionKey);
    return _searchSessionBox.watchAll().map((query) {
      final searches = query.find();
      if (sessionKey != _sessionKey) {
        return searches
            .where((search) => search.sessionKey == sessionKey)
            .toList();
      }
      return searches;
    });
  }

  @override
  Future<List<SearchHistory>> getSearchHistoryBySessionKey(
    String sessionKey,
  ) async {
    try {
      _logOperation('Getting search history by session key', sessionKey);
      final query = _searchHistorySessionBox
          .queryWith(
            SearchHistory_.sessionKey.equals(sessionKey),
          )
          .order(SearchHistory_.timestamp, flags: ob.Order.descending)
          .build();
      final history = query.find();
      query.close();
      _logOperation(
        'Found search history by session key',
        '$sessionKey: ${history.length} entries',
      );
      return history;
    } catch (e) {
      _logOperation(
        'Error getting search history by session key',
        '$sessionKey: $e',
      );
      return [];
    }
  }

  @override
  Future<SearchHistory?> addSearchHistory(String keyword) async {
    try {
      _logOperation('Adding search history', keyword);
      final query = _searchHistorySessionBox
          .queryWith(
            SearchHistory_.keyword.equals(keyword),
          )
          .build();
      final existing = query.findFirst();
      query.close();

      if (existing != null) {
        existing.timestamp = DateTime.now();
        existing.updateTime = DateTime.now();
        _searchHistorySessionBox.put(existing);
        _logOperation('Updated existing search history', keyword);
        return existing;
      } else {
        final newHistory = SearchHistory.create(
          sessionKey: _searchHistorySessionBox.activeSessionKey,
          keyword: keyword,
        );
        final id = _searchHistorySessionBox.put(newHistory);
        newHistory.id = id;
        _logOperation('Added new search history', keyword);
        return newHistory;
      }
    } catch (e) {
      _logOperation('Error adding search history', '$keyword: $e');
      return null;
    }
  }

  @override
  Future<bool> deleteSearchHistory(String sessionKey, String keyword) async {
    try {
      _logOperation('Deleting search history', '$sessionKey: $keyword');
      final query = _searchHistorySessionBox
          .queryWith(
            SearchHistory_.sessionKey
                .equals(sessionKey)
                .and(SearchHistory_.keyword.equals(keyword)),
          )
          .build();
      final toDelete = query.find();
      query.close();
      final ids = toDelete.map((e) => e.id).toList();
      final count = _searchHistorySessionBox.removeMany(ids);
      _logOperation(
        'Deleted search history',
        '$sessionKey: $keyword: $count entries',
      );
      return count > 0;
    } catch (e) {
      _logOperation(
        'Error deleting search history',
        '$sessionKey: $keyword: $e',
      );
      return false;
    }
  }

  @override
  Future<void> clearSearchHistory(String sessionKey) async {
    try {
      _logOperation('Clearing search history for session', sessionKey);
      final query = _searchHistorySessionBox
          .queryWith(
            SearchHistory_.sessionKey.equals(sessionKey),
          )
          .build();
      final toDelete = query.find();
      query.close();
      final ids = toDelete.map((e) => e.id).toList();
      final count = _searchHistorySessionBox.removeMany(ids);
      _logOperation(
        'Cleared search history for session',
        '$sessionKey: $count entries',
      );
    } catch (e) {
      _logOperation(
        'Error clearing search history for session',
        '$sessionKey: $e',
      );
    }
  }

  @override
  Stream<List<SearchHistory>> watchSearchHistoryBySessionKey(
    String sessionKey,
  ) {
    _logOperation('Watching search history by session key', sessionKey);
    return _searchHistorySessionBox.watchAll().map((query) {
      final history = query.find();
      if (sessionKey != _sessionKey) {
        return history.where((h) => h.sessionKey == sessionKey).toList();
      }
      return history;
    });
  }

  @override
  List<Search> getAllRecent() {
    _logOperation('Getting all searches');
    final allSearches = _searchSessionBox.getAll();
    final result = allSearches.map((searchData) {
      if (searchData.isUser) {
        final userTarget = searchData.user.target;
        final profile = Profile()
          ..sessionKey = _searchSessionBox.activeSessionKey
          ..displayName = userTarget?.profile.target?.displayName ?? ''
          ..avatar = userTarget?.profile.target?.avatar ?? ''
          ..userBadgeType = userTarget?.profile.target?.userBadgeType ?? 0;

        final user = User()
          ..sessionKey = _searchSessionBox.activeSessionKey
          ..userId = userTarget?.userId ?? ''
          ..username = userTarget?.username ?? ''
          ..profile.target = profile;

        searchData.user.target = user;
        return searchData;
      } else {
        final channel = Channel()
          ..sessionKey = _searchSessionBox.activeSessionKey
          ..channelId = searchData.channelFieldId ?? ''
          ..workspaceId = searchData.workspaceId ?? ''
          ..name = searchData.effectiveChannelName ?? ''
          ..avatar = searchData.effectiveAvatar ?? '';

        searchData.channel.target = channel;
        return searchData;
      }
    }).toList();

    result.sort(
      (a, b) => (b.timestamp ?? DateTime.now())
          .compareTo(a.timestamp ?? DateTime.now()),
    );
    return result;
  }

  @override
  List<SearchHistory> getAllHistory() {
    final history = _searchHistorySessionBox.getAll();
    history.sort(
      (a, b) => (b.timestamp ?? DateTime.now())
          .compareTo(a.timestamp ?? DateTime.now()),
    );
    return history;
  }

  @override
  bool deleteHistory(String keyword) {
    final query = _searchHistorySessionBox
        .queryWith(
          SearchHistory_.keyword.equals(keyword),
        )
        .build();
    final history = query.findFirst();
    query.close();
    if (history != null) {
      return _searchHistorySessionBox.remove(history.id);
    }
    return false;
  }

  void _logOperation(String operation, [String? details]) {
    DataRouterLogger().debug(
      '$operation${details != null ? ' - $details' : ''}',
      component: LogComponent.database,
      methodName: '_logOperation',
    );
  }

  @override
  int saveSearch(Search search) {
    var id = 0;

    if (search.isUser) {
      final query = _searchSessionBox
          .queryWith(Search_.userFieldId.equals(search.userFieldId!))
          .build();
      final oldUser = query.findFirst();
      query.close();
      if (oldUser != null) {
        id = oldUser.id;
      }
    } else {
      final query = _searchSessionBox
          .queryWith(
            Search_.workspaceId
                .equals(search.workspaceId!)
                .and(Search_.channelFieldId.equals(search.channelFieldId!)),
          )
          .build();
      final oldChannel = query.findFirst();
      query.close();
      if (oldChannel != null) {
        id = oldChannel.id;
      }
    }

    search.id = id;

    if (search.isChannel) {
      final channel = search.channel.target;

      if (channel != null) {
        if (channel.id == 0) {
          final existingChannel = _channelSessionBox
              .queryWith(
                Channel_.workspaceId.equals(channel.workspaceId).and(
                      Channel_.channelId.equals(channel.channelId),
                    ),
              )
              .build()
              .findFirst();

          if (existingChannel != null) {
            search.channel.target = existingChannel;
          } else {
            final newId = _channelSessionBox.put(channel);
            assert(newId > 0);
          }
        }
      }
    }

    return _searchSessionBox.put(search);
  }

  @override
  bool deleteSearchById(int id) {
    return _searchSessionBox.remove(id);
  }
}
