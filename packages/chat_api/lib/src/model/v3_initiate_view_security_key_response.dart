//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_credential_request_options.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_view_security_key_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateViewSecurityKeyResponse {
  /// Returns a new [V3InitiateViewSecurityKeyResponse] instance.
  V3InitiateViewSecurityKeyResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final CommonCredentialRequestOptions? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateViewSecurityKeyResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3InitiateViewSecurityKeyResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateViewSecurityKeyResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateViewSecurityKeyResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
