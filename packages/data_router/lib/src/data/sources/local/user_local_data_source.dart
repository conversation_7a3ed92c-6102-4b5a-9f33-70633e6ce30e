import 'dart:async';

import 'package:injectable/injectable.dart';

import '../../../../data_router.dart';
import '../../../domain/repositories/private_data_repository.dart';
import '../../../domain/repositories/user_repository.dart';
import '../../models/deleted_user.dart';
import 'base_local_data_source.dart';

/// User-specific local data source implementation
/// Handles User entity operations using ObjectBox database through UserRepository
///
/// This follows the Single Responsibility Principle by handling only User entities
/// and delegates actual database operations to the existing UserRepository.
///
/// No initialization is required as UserRepository is injected and already
/// initialized through the dependency injection container.
@LazySingleton()
class UserLocalDataSource extends BaseLocalDataSource<User> {
  final UserRepository _userRepository;
  final PrivateDataRepository _privateDataRepository;

  final Map<String, StreamController<User?>> _itemControllers = {};
  final Map<String, StreamController<List<User>>> _listControllers = {};

  UserLocalDataSource(this._userRepository, this._privateDataRepository);

  @override
  EntityEnum get entity => EntityEnum.user;

  @override
  Future<User?> get(String id) async {
    try {
      logOperation('Getting user', id);
      final user = _userRepository.getUser(id);

      if (user != null) {
        logOperation('Found user in local database', id);
        return user;
      } else {
        logOperation('User not found in local database', id);
        return null;
      }
    } catch (e) {
      logOperation('Error getting user', '$id: $e');
      throw Exception('Failed to get user[$id]: $e');
    }
  }

  User? getSync(String id) {
    try {
      logOperation('Getting user', id);
      final user = _userRepository.getUser(id);

      if (user != null) {
        logOperation('Found user in local database', id);
        return user;
      } else {
        logOperation('User not found in local database', id);
        return null;
      }
    } catch (e) {
      logOperation('Error getting user', '$id: $e');
      throw Exception('Failed to get user[$id]: $e');
    }
  }

  @override
  Future<List<User>> getAll() async {
    try {
      logOperation('Getting all users');
      final users = _userRepository.getUsers();
      logOperation(
        'Found users in local database',
        '${users.length} users',
      );
      return users;
    } catch (e) {
      logOperation('Error getting all users', e.toString());
      throw Exception('Failed to get all users: $e');
    }
  }

  @override
  Future<void> insert(User item) async {
    try {
      if (!validate(item)) {
        handleValidationError(item, 'User validation failed');
        return;
      }

      final id = item.userId;
      logOperation('Saving user', id);

      _userRepository.insert(item);
      logOperation('Saved user successfully', id);
    } catch (e) {
      logOperation('Error saving user', '${item.userId}: $e');
      throw Exception('Failed to save user[${item.userId}]: $e');
    }
  }

  @override
  Future<void> insertAll(List<User> items) async {
    logOperation('Saving users', '${items.length} users');

    final validUsers = <User>[];
    for (final item in items) {
      if (validate(item)) {
        validUsers.add(item);
      } else {
        logOperation('Skipping invalid user', item.userId);
      }
    }

    if (validUsers.isNotEmpty) {
      _userRepository.insertAll(validUsers);
      logOperation('Saved users successfully', '${validUsers.length} users');
    }
  }

  @override
  Future<bool> delete(String id) async {
    try {
      logOperation('Deleting user', id);
      final deleted = _userRepository.deleteUser(id);

      if (deleted) {
        logOperation('Deleted user successfully', id);

        // Notify watchers
        _notifyItemWatchers(id, null);
        _notifyListWatchers();
      } else {
        logOperation('User not found for deletion', id);
      }

      return deleted;
    } catch (e) {
      logOperation('Error deleting user', '$id: $e');
      throw Exception('Failed to delete user[$id]: $e');
    }
  }

  @override
  Future<int> deleteAll() async {
    try {
      logOperation('Deleting all users');
      final deletedCount = _userRepository.deleteAllUsers();
      logOperation(
        'Deleted all users successfully',
        '$deletedCount users',
      );

      // Notify watchers
      _notifyListWatchers();

      return deletedCount;
    } catch (e) {
      logOperation('Error deleting all users', e.toString());
      throw Exception('Failed to delete all users: $e');
    }
  }

  @override
  Stream<User?> watch(String id) {
    return _userRepository.getUserStream(id);
  }

  @override
  Stream<List<User>> watchAll() {
    const key = 'users:all';

    if (!_listControllers.containsKey(key)) {
      _listControllers[key] = StreamController<List<User>>.broadcast();

      // Initialize with current value
      getAll().then((users) {
        if (_listControllers.containsKey(key)) {
          _listControllers[key]!.add(users);
        }
      });
    }

    logOperation('Watching all users');
    return _listControllers[key]!.stream;
  }

  @override
  Future<bool> exists(String id) async {
    try {
      final exists = _userRepository.userExists(id);
      logOperation('Checking user existence', '$id: $exists');
      return exists;
    } catch (e) {
      logOperation('Error checking user existence', '$id: $e');
      return false;
    }
  }

  @override
  Future<int> count() async {
    try {
      final count = _userRepository.getUserCount();
      logOperation('Getting user count', count.toString());
      return count;
    } catch (e) {
      logOperation('Error getting user count', e.toString());
      return 0;
    }
  }

  @override
  bool validate(User item) {
    return item.userId.isNotEmpty &&
        item.sessionKey.isNotEmpty &&
        item.username.isNotEmpty;
  }

  /// Get users by session key
  /// This is a User-specific query optimization for session-based filtering
  Future<List<User>> getUsersBySession(String sessionKey) async {
    try {
      logOperation('Getting users by session', sessionKey);
      final users = _userRepository.getUsersBySession(sessionKey);
      logOperation(
        'Found users for session',
        '$sessionKey: ${users.length} users',
      );
      return users;
    } catch (e) {
      logOperation('Error getting users by session', '$sessionKey: $e');
      throw Exception('Failed to get users by session[$sessionKey]: $e');
    }
  }

  /// Watch current user data for the active session (reactive)
  /// Returns stream of current user updates from local storage
  /// This is a specialized method for user entities to get "me" data
  Stream<User?> watchMe() {
    logOperation('Watching current user (me)');
    return _userRepository.getMeStream();
  }

  User? getMe() {
    logOperation('Get current user (me)');
    return _userRepository.getMe();
  }

  /// Save profile entity through repository
  /// This is a specialized method for saving profile data
  Future<void> saveProfile(Profile profile) async {
    try {
      logOperation('Saving profile', profile.displayName);
      await _userRepository.saveProfile(profile);
      logOperation('Saved profile successfully', profile.displayName);
    } catch (e) {
      logOperation('Error saving profile', '${profile.displayName}: $e');
      throw Exception('Failed to save profile[${profile.displayName}]: $e');
    }
  }

  /// OPTIMIZED: Get user by sessionKey and userId using composite index
  /// This method uses the composite index (sessionKey + userId) for faster queries
  /// instead of scanning the entire User table
  Future<User?> getUserBySessionAndUserId(String userId) async {
    try {
      final user = _userRepository.getUserBySessionAndUserId(userId);

      if (user != null) {
        return user;
      } else {
        return null;
      }
    } catch (e) {
      logOperation('Error getting user with optimized query', '$userId: $e');
      throw Exception('Failed to get user[$userId]: $e');
    }
  }

  /// OPTIMIZED: Update user and profile in single transaction
  /// This method performs both User and Profile updates in a single database transaction
  /// reducing the number of database operations and improving performance
  Future<void> updateUserWithProfile(User user, Profile profile) async {
    try {
      logOperation(
        'Updating user and profile in single transaction',
        '${user.userId}:${profile.displayName}',
      );

      await _userRepository.updateUserWithProfile(user, profile);

      logOperation(
        'Updated user and profile successfully in single transaction',
        '${user.userId}:${profile.displayName}',
      );
    } catch (e) {
      logOperation(
        'Error updating user and profile in single transaction',
        '${user.userId}:${profile.displayName}: $e',
      );
      throw Exception(
        'Failed to update user and profile[${user.userId}:${profile.displayName}]: $e',
      );
    }
  }

  /// Get all users by set of user IDs
  Stream<List<User>> getAllUsersBySetUserIdOnChannelStream(
    Set<String> setUserId,
  ) {
    return _userRepository.getAllUsersBySetUserIdOnChannelStream(setUserId);
  }

  //region UserStatus Methods

  /// Get user status by user ID
  /// This method gets the status through the User entity's relationship
  Future<UserStatus?> getUserStatus(String userId) async {
    try {
      logOperation('Getting user status', userId);
      final user = await get(userId);

      if (user?.status.target != null) {
        logOperation('Found user status in local database', userId);
        return user!.status.target;
      } else {
        logOperation('User status not found in local database', userId);
        return null;
      }
    } catch (e) {
      logOperation('Error getting user status', '$userId: $e');
      return null;
    }
  }

  /// Delete user status by user ID
  /// This method clears the status relationship from the user
  Future<bool> deleteUserStatus(String userId) async {
    try {
      logOperation('Deleting user status', userId);

      final user = await get(userId);
      if (user == null) {
        logOperation('User not found for status deletion', userId);
        return false;
      }
      _userRepository.deleteMyStatus(user: user);

      return true;
    } catch (e) {
      logOperation('Error deleting user status', '$userId: $e');
      return false;
    }
  }

  Future<bool> updateUserStatus(String userId, UserStatus userStatus) async {
    try {
      logOperation('Deleting user status', userId);

      final user = await get(userId);
      if (user == null) {
        logOperation('User not found for status deletion', userId);
        return false;
      }
      _userRepository.updateMyStatus(user: user, userStatus: userStatus);

      return true;
    } catch (e) {
      logOperation('Error deleting user status', '$userId: $e');
      return false;
    }
  }

  /// Watch user status for a specific user ID
  /// Returns stream of user status updates through user stream
  Stream<UserStatus?> watchUserStatus(String userId) {
    logOperation('Watching user status', userId);
    return watch(userId).map((user) => user?.status.target);
  }

  /// Watch all user status for a specific user ID
  /// Returns stream of user status updates through user stream
  Stream<List<Friend>> watchAllUserStatus() {
    return _userRepository.watchAllUserStatus();
  }

  //endregion

  /// Notify item watchers of changes
  void _notifyItemWatchers(String id, User? user) {
    final key = 'user:$id';
    if (_itemControllers.containsKey(key)) {
      _itemControllers[key]!.add(user);
    }
  }

  /// Notify list watchers of changes
  void _notifyListWatchers() {
    const key = 'users:all';
    if (_listControllers.containsKey(key)) {
      getAll().then((users) {
        if (_listControllers.containsKey(key)) {
          _listControllers[key]!.add(users);
        }
      });
    }
  }

  /// Dispose all stream controllers
  void dispose() {
    for (final controller in _itemControllers.values) {
      controller.close();
    }
    for (final controller in _listControllers.values) {
      controller.close();
    }
    _itemControllers.clear();
    _listControllers.clear();
  }

  @override
  Future<bool> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting all users with sessionKey', sessionKey);

      // Single bulk delete operation using repository's deleteUsersBySession method
      _userRepository.deleteUsersBySession(sessionKey);

      return true;
    } catch (e) {
      logOperation('Error deleting users by sessionKey', '$sessionKey: $e');
      return false;
    }
  }

  //region UserPresence Methods

  /// Get user presence by user ID
  /// This method gets the presence through the User entity's relationship
  Future<UserPresence?> getUserPresence(String userId) async {
    try {
      logOperation('Getting user presence', userId);
      final user = await get(userId);

      if (user?.presence.target != null) {
        logOperation('Found user presence in local database', userId);
        return user!.presence.target;
      } else {
        logOperation('User presence not found in local database', userId);
        return null;
      }
    } catch (e) {
      logOperation('Error getting user presence', '$userId: $e');
      return null;
    }
  }

  /// Save user presence entity
  /// This method saves the presence and links it to the user through relationship
  Future<void> saveUserPresence(UserPresence userPresence) async {
    try {
      logOperation('Saving user presence', userPresence.userId);

      // Get the user first
      final user = await get(userPresence.userId);
      if (user == null) {
        logOperation('User not found for presence save', userPresence.userId);
        throw Exception(
          'User not found for presence save: ${userPresence.userId}',
        );
      }

      // Set the relationship
      user.presence.target = userPresence;

      // Save the updated user (this will save the presence relationship)
      await insert(user);

      logOperation('Saved user presence successfully', userPresence.userId);

      // Notify watchers
      _notifyItemWatchers(userPresence.userId, user);
    } catch (e) {
      logOperation('Error saving user presence', '${userPresence.userId}: $e');
      throw Exception(
        'Failed to save user presence[${userPresence.userId}]: $e',
      );
    }
  }

  /// Delete user presence by user ID
  /// This method clears the presence relationship from the user
  Future<bool> deleteUserPresence(String userId) async {
    try {
      logOperation('Deleting user presence', userId);

      final user = await get(userId);
      if (user == null) {
        logOperation('User not found for presence deletion', userId);
        return false;
      }

      // Clear the presence relationship
      user.presence.target = null;

      // Save the updated user
      await insert(user);

      logOperation('Deleted user presence successfully', userId);

      // Notify watchers
      _notifyItemWatchers(userId, user);

      return true;
    } catch (e) {
      logOperation('Error deleting user presence', '$userId: $e');
      return false;
    }
  }

  /// Watch user presence for a specific user ID
  /// Returns stream of user presence updates through user stream
  Stream<UserPresence?> watchUserPresence(String userId) {
    logOperation('Watching user presence', userId);
    return watch(userId).map((user) => user?.presence.target);
  }

  /// Update user presence status
  /// This method updates only the presence status and related timestamps
  Future<void> updateUserpresenceState(
    String userId,
    PresenceStateEnum presenceState,
  ) async {
    try {
      logOperation('Updating user presence status', '$userId: $presenceState');

      final user = await get(userId);
      if (user?.presence.target != null) {
        user!.presence.target!.presenceStateEnum = presenceState;
        await insert(user);

        logOperation(
          'Updated user presence status successfully',
          '$userId: $presenceState',
        );

        // Notify watchers
        _notifyItemWatchers(userId, user);
      } else {
        logOperation('User presence not found for status update', userId);
      }
    } catch (e) {
      logOperation('Error updating user presence status', '$userId: $e');
    }
  }

  /// Update user typing status
  /// This method updates the typing status and channel
  Future<void> updateUserTypingStatus(
    String userId,
    bool isTyping,
    String channelId,
  ) async {
    try {
      logOperation(
        'Updating user typing status',
        '$userId: $isTyping in $channelId',
      );

      final user = await get(userId);
      if (user?.presence.target != null) {
        if (isTyping) {
          user!.presence.target!.startTyping(channelId);
        } else {
          user!.presence.target!.stopTyping();
        }

        await insert(user);

        logOperation(
          'Updated user typing status successfully',
          '$userId: $isTyping',
        );

        // Notify watchers
        _notifyItemWatchers(userId, user);
      } else {
        logOperation(
          'User presence not found for typing status update',
          userId,
        );
      }
    } catch (e) {
      logOperation('Error updating user typing status', '$userId: $e');
    }
  }

  //endregion

  //region VisitedProfile Methods
  /// Get visited profile by session key and visited user ID
  /// Returns visited profile or null if not found
  Future<VisitedProfile?> getVisitedProfile(
    String sessionKey,
    String visitedUserId,
  ) async {
    try {
      logOperation('Getting visited profile', '$sessionKey:$visitedUserId');

      final visitedProfile =
          _userRepository.getVisitedProfile(sessionKey, visitedUserId);

      if (visitedProfile != null) {
        logOperation('Found visited profile', visitedUserId);
      } else {
        logOperation('Visited profile not found', visitedUserId);
      }

      return visitedProfile;
    } catch (e) {
      logOperation(
        'Error getting visited profile',
        '$sessionKey:$visitedUserId: $e',
      );
      return null;
    }
  }

  /// Save visited profile entity
  /// This method saves the visited profile and links it to the visited user through relationship
  Future<void> saveVisitedProfile(VisitedProfile visitedProfile) async {
    try {
      logOperation(
        'Saving visited profile',
        '${visitedProfile.sessionKey}:${visitedProfile.visitedUserId}',
      );

      // Get the visited user if exists
      final visitedUser = await get(visitedProfile.visitedUserId);
      if (visitedUser != null) {
        // Set the relationship
        visitedProfile.visitedUser.target = visitedUser;
      }

      // Save the visited profile
      _userRepository.insertVisitedProfile(visitedProfile);

      logOperation(
        'Saved visited profile successfully',
        visitedProfile.visitedUserId,
      );
    } catch (e) {
      logOperation(
        'Error saving visited profile',
        '${visitedProfile.visitedUserId}: $e',
      );
      throw Exception(
        'Failed to save visited profile[${visitedProfile.visitedUserId}]: $e',
      );
    }
  }

  /// Save multiple visited profiles
  /// This method saves multiple visited profiles efficiently
  Future<void> saveVisitedProfiles(List<VisitedProfile> visitedProfiles) async {
    try {
      logOperation(
        'Saving visited profiles',
        '${visitedProfiles.length} profiles',
      );

      for (final visitedProfile in visitedProfiles) {
        // Get the visited user if exists
        final visitedUser = await get(visitedProfile.visitedUserId);
        if (visitedUser != null) {
          // Set the relationship
          visitedProfile.visitedUser.target = visitedUser;
        }
      }

      // Save all visited profiles
      _userRepository.insertVisitedProfiles(visitedProfiles);

      logOperation(
        'Saved visited profiles successfully',
        '${visitedProfiles.length} profiles',
      );
    } catch (e) {
      logOperation(
        'Error saving visited profiles',
        '${visitedProfiles.length}: $e',
      );
      throw Exception('Failed to save visited profiles: $e');
    }
  }

  /// Delete visited profile by user ID and session
  /// Returns true if deletion was successful
  Future<bool> deleteVisitedProfile(
    String sessionKey,
    String visitedUserId,
  ) async {
    try {
      logOperation('Deleting visited profile', '$sessionKey:$visitedUserId');

      final success =
          _userRepository.deleteVisitedProfile(sessionKey, visitedUserId);

      if (success) {
        logOperation('Deleted visited profile successfully', visitedUserId);
      } else {
        logOperation('Visited profile not found for deletion', visitedUserId);
      }

      return success;
    } catch (e) {
      logOperation('Error deleting visited profile', '$visitedUserId: $e');
      return false;
    }
  }

  /// Mark visited profile as read
  /// Returns true if update was successful
  Future<bool> markVisitedProfileAsRead(
    String sessionKey,
    String visitedUserId,
  ) async {
    try {
      logOperation(
        'Marking visited profile as read',
        '$sessionKey:$visitedUserId',
      );

      final visitedProfile =
          _userRepository.getVisitedProfile(sessionKey, visitedUserId);
      if (visitedProfile != null) {
        visitedProfile.markAsRead();
        _userRepository.insertVisitedProfile(visitedProfile);

        logOperation(
          'Marked visited profile as read successfully',
          visitedUserId,
        );
        return true;
      } else {
        logOperation(
          'Visited profile not found for marking as read',
          visitedUserId,
        );
        return false;
      }
    } catch (e) {
      logOperation(
        'Error marking visited profile as read',
        '$visitedUserId: $e',
      );
      return false;
    }
  }

  /// Mark all visited profiles as read for a session
  /// Returns number of updated visited profiles
  Future<int> markAllVisitedProfilesAsRead(String sessionKey) async {
    try {
      logOperation('Marking all visited profiles as read', sessionKey);

      final markedCount =
          _userRepository.markAllVisitedProfilesAsRead(sessionKey);

      logOperation(
        'Marked visited profiles as read successfully',
        '$sessionKey: $markedCount profiles',
      );
      return markedCount;
    } catch (e) {
      logOperation(
        'Error marking all visited profiles as read',
        '$sessionKey: $e',
      );
      return 0;
    }
  }

  /// Watch visited profiles for a specific session
  /// Returns stream of visited profiles updates
  Stream<List<VisitedProfile>> watchVisitedProfiles(String sessionKey) {
    logOperation('Watching visited profiles for session', sessionKey);
    return _userRepository.getVisitedProfilesStream(sessionKey);
  }

  /// Get unread visited profiles count for a session
  /// Returns number of unread visited profiles
  Future<int> getUnreadVisitedProfilesCount(String sessionKey) async {
    try {
      logOperation('Getting unread visited profiles count', sessionKey);

      final count = _userRepository.getUnreadVisitedProfilesCount(sessionKey);

      logOperation(
        'Found unread visited profiles',
        '$sessionKey: $count profiles',
      );
      return count;
    } catch (e) {
      logOperation(
        'Error getting unread visited profiles count',
        '$sessionKey: $e',
      );
      return 0;
    }
  }

  Future<void> updateUserConnectedLink(String userId, String link) async {
    try {
      logOperation('update User Connected Link', userId);

      _userRepository.updateUserConnectLink(userId, link);
    } catch (e) {
      logOperation(
        'Error update User Connected Link',
        '$e',
      );
    }
  }

  Future<String?> getUserConnectedLink(String userId) async {
    try {
      logOperation('update User Connected Link', userId);

      final link = _userRepository.getUserConnectLink(userId);
      if (link != null) {
        return link;
      }
      return null;
    } catch (e) {
      logOperation(
        'Error update User Connected Link',
        '$e',
      );
      return null;
    }
  }

  List<User> getManyUserByUserId(List<String> userIds) {
    return _userRepository.getManyUsers(userIds);
  }

  Stream<List<User>> getUsersStream() {
    return _userRepository.getUsersStream();
  }

  List<UserPrivateData> getPrivateDataByIds(List<String> listUserId) {
    return _privateDataRepository.getPrivateDataByIds(listUserId);
  }

  User? getUserByIdSync(String userId) {
    return _userRepository.getUser(userId);
  }

  void insertUserPrivateData(UserPrivateData dUserPrivateData) {
    _privateDataRepository.insertUserPrivateData(dUserPrivateData);
  }

  Stream<List<UserPrivateData>> watchAllUserPrivateData() {
    return _privateDataRepository.watchAllUserPrivateData();
  }

  ChannelPrivateData? getChannelPrivateData(String channelId) {
    return _privateDataRepository.getChannel(channelId);
  }

  void insertChannelPrivateData(ChannelPrivateData dChannelPrivateData) {
    _privateDataRepository.updateChannel(dChannelPrivateData);
  }

  int? getMaxSortChannel() {
    return _privateDataRepository.getMaxSortChannel();
  }

  /// Get all chat users from local database
  /// Used for sync operations
  Future<List<User>> getChatUsers() async {
    try {
      final chatUsers = _userRepository.getChatUsers();
      return chatUsers;
    } catch (e) {
      logOperation('Error getting chat users', '$e');
      throw Exception('Failed to get chat users: $e');
    }
  }

  /// Get session metadata for sync operations
  Future<SessionLocalMetadata?> getSessionMetadata() async {
    try {
      final metadata = _userRepository.getSessionMetadata();
      return metadata;
    } catch (e) {
      throw Exception('Failed to get session metadata: $e');
    }
  }

  /// Delete chat user by ID
  Future<void> deleteChatUser(String userId) async {
    try {
      await _userRepository.deleteChatUser(userId);
    } catch (e) {
      throw Exception('Failed to delete chat user[$userId]: $e');
    }
  }

  /// Update user update time after for sync
  Future<void> updateUserUpdateTimeAfter(DateTime syncTime) async {
    try {
      await _userRepository.updateUserUpdateTimeAfter(syncTime);
    } catch (e) {
      throw Exception('Failed to update user update time after: $e');
    }
  }

  /// Delete chat friends by user IDs
  Future<void> deleteChatFriends(List<String> userIds) async {
    try {
      await _userRepository.deleteChatFriends(userIds);
    } catch (e) {
      throw Exception('Failed to delete chat friends: $e');
    }
  }

  /// Sync delete user private data
  Future<void> syncDeleteUserPrivateData(List<DeletedUser> deletedUsers) async {
    try {
      await _privateDataRepository.syncDeleteUserPrivateData(deletedUsers);
    } catch (e) {
      throw Exception('Failed to sync delete user private data: $e');
    }
  }

  void syncUserStatus(List<User> user) {
    try {
      _userRepository.insertAll(user);
    } catch (e) {
      logOperation('Error syncing user status', '$e');
      throw Exception('Failed to sync user status: $e');
    }
  }

  User? getUserByIdAndSessionKey({
    required String userId,
    required String sessionKey,
  }) {
    try {
      logOperation('Getting user userId: $userId - sessionKey: $sessionKey');
      final user = _userRepository.getUserByUserIdAndSessionKey(
        userId: userId,
        sessionKey: sessionKey,
      );

      if (user != null) {
        logOperation('Found user in local database', userId);
        return user;
      } else {
        logOperation('User not found in local database', userId);
        return null;
      }
    } catch (e) {
      logOperation('Error getting user', '$userId: $e');
      throw Exception('Failed to get user[$userId]: $e');
    }
  }

//endregion
}
