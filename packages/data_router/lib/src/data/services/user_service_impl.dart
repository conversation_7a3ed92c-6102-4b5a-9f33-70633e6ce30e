import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../domain/services/user_service.dart';
import '../database/entities/entities.dart';
import '../serializers/private_data_serializer.dart';
import '../serializers/user_status_serializer.dart';

/// Implementation of UserService using ChatAPI
/// Provides concrete implementation for user remote operations
///
/// This follows the same pattern as UserRepositoryImpl for local operations,
/// but handles API calls instead of database operations.
@LazySingleton(as: UserService)
class UserServiceImpl implements UserService {
  final ApiClient _apiClient;

  UserServiceImpl(this._apiClient);

  @override
  Future<User?> getUser(String userId) async {
    try {
      final response =
          await _apiClient.api.getUserViewServiceApi().getUser(userId: userId);

      if (response.data?.data != null) {
        return UserSerializer.fromV3UserView(response.data!.data!);
      }

      return null;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return null;
    }
  }

  @override
  Future<User?> getMe({Map<String, dynamic>? headers}) async {
    try {
      final response = await _apiClient.api.getUserViewServiceApi().getMe(
            headers: headers,
          );

      if (response.data?.data != null) {
        return UserSerializer.fromV3Me(response.data!.data!);
      }

      return null;
    } catch (e) {
      Log.e(name: ' error UserServiceImpl.getMe', e);
      return null;
    }
  }

  @override
  Future<List<User>> getUsers() async {
    try {
      // Users list API is not implemented yet in the ChatAPI
      // Return empty list for now, similar to current implementation
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<User?> getUserByUsername(String username) async {
    try {
      final response = await _apiClient.api
          .getUserViewServiceApi()
          .getUserByUsername(username: username);

      if (response.data?.data != null) {
        return UserSerializer.fromV3UserView(response.data!.data!);
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<User>> searchUsers(String searchTerm) async {
    try {
      // Search users API would be implemented here
      // For now, return empty list as API is not fully implemented
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<User>> syncUsers({
    required String? updateTimeAfter,
    required List<String> userIds,
  }) async {
    try {
      final response = await _apiClient.api.getUserViewServiceApi().syncUsers(
            updateTimeAfter: updateTimeAfter,
            userIds: userIds,
          );

      if (response.data?.data != null) {
        return response.data!.data!
            .map((userData) => UserSerializer.fromV3UserView(userData))
            .toList();
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<bool> userExists(String userId) async {
    try {
      final user = await getUser(userId);
      return user != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<int> getUserCount() async {
    try {
      // User count API is not implemented yet
      // Return -1 to indicate not supported
      return -1;
    } catch (e) {
      return -1;
    }
  }

  @override
  Future<bool> updateUserDisplayName(String displayName) async {
    try {
      final request = V3UpdateUserDisplayNameRequest(
        displayName: displayName,
      );

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .updateUserDisplayName(body: request);

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  @override
  Future<bool> updateUserAvatar(String avatarPath) async {
    try {
      final request = V3UpdateUserAvatarRequest(
        avatarPath: avatarPath,
      );

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .updateUserAvatar(body: request);

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  @override
  Future<bool> updateUserCover(String coverPath) async {
    try {
      final request = V3UpdateCoverPhotoRequest(
        coverPath: coverPath,
      );

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .updateCoverPhoto(body: request);

      return response.data?.ok == true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> removeUserAvatar() async {
    try {
      final response =
          await _apiClient.api.getUserProfileServiceApi().deleteUserAvatar();

      return response.data?.ok == true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> addUserCover(String coverPath) async {
    try {
      final request = V3AddCoverPhotoRequest(
        coverPath: coverPath,
      );

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .addCoverPhoto(body: request);

      return response.data?.ok == true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> removeUserCover() async {
    try {
      final response =
          await _apiClient.api.getUserProfileServiceApi().deleteCoverPhoto();

      return response.data?.ok == true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> turnOnOffNotification(bool isTurnOn) async {
    final response;
    if (isTurnOn == true) {
      response = await _apiClient.api
          .getNotificationServiceApi()
          .turnOnGlobalNotification();
    } else {
      response = await _apiClient.api
          .getNotificationServiceApi()
          .turnOffGlobalNotification();
    }

    if (response.data?.ok ?? false) {
      return true;
    }
    return false;
  }

  @override
  Future<bool> turnOnOffSingleNotification(bool isTurnOn) async {
    final response;
    response = await _apiClient.api
        .getNotificationServiceApi()
        .updateChannelCallNotificationSetting(
          body: V3UpdateChannelCallNotificationSettingRequest(
            status: isTurnOn,
          ),
        );

    if (response.data?.ok ?? false) {
      return true;
    }
    return false;
  }

  @override
  Future<bool> turnOnOffGroupNotification(bool isTurnOn) async {
    final response;
    response = await _apiClient.api
        .getNotificationServiceApi()
        .updateChannelCallNotificationSetting(
          body: V3UpdateChannelCallNotificationSettingRequest(
            status: isTurnOn,
          ),
        );

    if (response.data?.ok ?? false) {
      return true;
    }
    return false;
  }

  //region UserStatus Methods Implementation

  @override
  Future<UserStatus?> addUserStatus({
    required String content,
    required String status,
    int? expireAfterTime,
  }) async {
    try {
      final request = V3AddUserStatusRequest(
        content: content,
        status: status.isEmpty ? null : status,
        expireAfterTime: _parseUserStatusExpireAfterTime(expireAfterTime ?? 99),
      );

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .addUserStatus(body: request);

      // Check if the response indicates success and has data
      if (response.data?.ok == true && response.data?.data != null) {
        // Get current user info for userId and sessionKey
        final currentUser = await getMe();
        if (currentUser != null) {
          // Convert V3UserStatus response to UserStatus entity
          return UserStatusSerializer.fromV3UserStatus(
            response.data!.data!,
            sessionKey: currentUser.sessionKey,
            userId: currentUser.userId,
          );
        }
      }

      return null;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return null;
    }
  }

  @override
  Future<bool> updateUserStatus({
    required String content,
    required String status,
    int? expireAfterTime,
  }) async {
    try {
      // Note: V3UpdateUserStatusRequest doesn't support expiration time
      // Only content and status can be updated

      final request = V3UpdateUserStatusRequest(
        content: content,
        status: status,
      );

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .updateUserStatus(body: request);

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      Log.e(name: 'UserServiceImpl.updateUserStatus', e);
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  @override
  Future<bool> deleteUserStatus() async {
    try {
      final response =
          await _apiClient.api.getUserProfileServiceApi().deleteUserStatus();

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  //endregion

  //region Helper Methods

  V3UserStatusExpireAfterTimeEnum _parseUserStatusExpireAfterTime(int hour) {
    return switch (hour) {
      0 => V3UserStatusExpireAfterTimeEnum
          .USER_STATUS_EXPIRES_AFTER_TIME_ENUM_NEVER,
      1 => V3UserStatusExpireAfterTimeEnum
          .USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_1_HOUR,
      4 => V3UserStatusExpireAfterTimeEnum
          .USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_4_HOUR,
      8 => V3UserStatusExpireAfterTimeEnum
          .USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_8_HOUR,
      24 => V3UserStatusExpireAfterTimeEnum
          .USER_STATUS_EXPIRES_AFTER_TIME_ENUM_AFTER_24_HOUR,
      _ => V3UserStatusExpireAfterTimeEnum
          .USER_STATUS_EXPIRES_AFTER_TIME_ENUM_UNSPECIFIED,
    };
  }

  //endregion

  //region VisitedProfile Methods Implementation

  @override
  Future<bool> visitedProfile(String userId) async {
    try {
      final request =
          VisitedProfileSerializer.toV3VisitedProfileRequest(userId);

      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .visitedProfile(body: request);

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  @override
  Future<List<VisitedProfile>> listUserVisitedProfile({int? limit}) async {
    try {
      final response = await _apiClient.api
          .getUserViewServiceApi()
          .listUserVisitedProfile(limit: limit);

      if (response.data?.data != null) {
        // Get current user info for sessionKey
        final currentUser = await getMe();
        if (currentUser != null) {
          return VisitedProfileSerializer.fromV3VisitedProfileDataList(
            response.data!.data!,
            sessionKey: currentUser.sessionKey,
          );
        }
      }

      return [];
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return [];
    }
  }

  @override
  Future<bool> deleteUserVisitedProfile(String userId) async {
    try {
      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .deleteUserVisitedProfile(userId: userId);

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  @override
  Future<bool> clearUserVisitedProfileNotifications() async {
    try {
      final response = await _apiClient.api
          .getUserProfileServiceApi()
          .clearUserVisitedProfileNotifications();

      // Check if the response indicates success
      return response.data?.ok == true;
    } catch (e) {
      // Log error but don't throw to maintain consistency with repository pattern
      return false;
    }
  }

  @override
  Future<String?> generateUserConnectLink() async {
    try {
      final response = await _apiClient.api
          .getUserConnectServiceApi()
          .generateUserConnectLink();

      if (response.data != null) {
        return response.data?.data?.link!;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<PrivateData?> loadPrivateData() async {
    try {
      final response =
          await _apiClient.api.getUserViewServiceApi().getPrivateData();

      if (response.data != null && response.data!.ok == true) {
        return Future.value(PrivateDataSerializer.fromV3(response.data!.data!));
      }
      return null;
    } catch (e) {
      Log.e(name: 'UserServiceImpl.loadPrivateData', e);
      rethrow;
    }
  }

  @override
  Future<bool> onBlockUser(String userId) async {
    try {
      final body = V3BlockUserRequest(targetUserId: userId);

      final response =
          await _apiClient.api.getUserSettingServiceApi().blockUser(body: body);
      if (response.data?.ok ?? false) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> onUnBlockUser(String userId) async {
    try {
      final body = V3UnblockUserRequest(targetUserId: userId);

      final response = await _apiClient.api
          .getUserSettingServiceApi()
          .unblockUser(body: body);
      if (response.data?.ok ?? false) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> onReportUser(
    String userId,
    V3ReportCategory? reportCategory,
    V3PretendingTo? pretendingTo,
    String? reportReason,
  ) async {
    try {
      final body = V3ReportUserRequest(
        userId: userId,
        reportCategory: reportCategory,
        pretendingTo: pretendingTo,
        reportReason: reportReason,
      );

      final response =
          await _apiClient.api.getUserReportServiceApi().reportUser(body: body);
      if (response.data?.ok ?? false) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  //endregion
  @override
  Future<List<User>> listUserStatus() async {
    try {
      final response =
          await _apiClient.api.getUserViewServiceApi().listUserStatus();

      if (response.data?.data != null) {
        final result = response.data!.data!
            .map((userData) => UserSerializer.fromV3User(userData))
            .toList();
        return result;
      }

      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<User>> listBlockedUser() async {
    try {
      final response = await _apiClient.api
          .getUserViewServiceApi()
          .listBlockedUsers(limit: 500);

      if (response.data != null) {
        final usersBLocked = response.data!.data ?? [];

        final users = usersBLocked
            .map((user) => UserSerializer.fromV3User(user))
            .toList();
        return users;
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<String?> decodeUserConnectLink(String userConnectLink) async {
    try {
      final body = V3DecodeUserConnectLinkRequest(link: userConnectLink);
      final response = await _apiClient.api
          .getUserConnectServiceApi()
          .decodeUserConnectLink(body: body);

      if (response.data?.data != null) {
        final user = response.data?.data?.userId;
        return user;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<User>> listInvitableUsers() async {
    try {
      final response = await _apiClient.api
          .getInvitationViewServiceApi()
          .listInvitableUsers();

      if (response.data?.ok == true && response.data?.data != null) {
        final users = response.data!.data!
            .map((userData) => UserSerializer.fromV3User(userData))
            .toList();
        return users;
      }

      return [];
    } catch (e) {
      return [];
    }
  }

//endregion
}
