import 'dart:async';
import 'dart:io';

import 'package:auth/auth.dart';
import 'package:data_router/data_router.dart' as data;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:passkeys/authenticator.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../navigation/routes/app_router.dart';
import '../../ui/widgets/new_login_bottom_sheet.dart';

@LazySingleton()
class LoginQRHandler {
  LoginQRHandler(this.qrLoginBloc, this._sessionDataOperation);

  final BuildContext context =
      GetIt.instance.get<AppRouter>().navigatorKey.currentContext!;
  final QrLoginBloc qrLoginBloc;
  final data.SessionDataOperation _sessionDataOperation;

  bool _bottomSheetShowed = false;
  String _currentQRData = '';
  bool _passkeyMigrated = true;
  StreamSubscription<QrLoginState>? _qrLoginSubscription;
  ValueNotifier<bool> processingNotifier = ValueNotifier(false);

  void init() {
    _qrLoginSubscription?.cancel();
    _qrLoginSubscription = qrLoginBloc.stream.listen(_handleQrLoginState);
  }

  void handleQrLoginDetected(String qrData) {
    if (_currentQRData.isNotEmpty) return;
    _currentQRData = qrData;
    processingNotifier.value = false;
    _showNewLoginFloatingBottomSheet();
  }

  void _handleQrLoginState(QrLoginState state) {
    state.maybeWhen(
      processing: (_) {
        processingNotifier.value = true;
      },
      authQRLoginSuccess: (_) {
        _hideCurrentBottomSheet();
        _showBottomSheetScanQRLoginSuccess();
      },
      authQRLoginFailure: (_) {
        _hideCurrentBottomSheet();
        _showBottomSheetScanQRLoginFailure();
      },
      authQRLoginNoCredentialFound: (_) {
        _hideCurrentBottomSheet();
        _showBottomSheetNoPasskeysAvailable();
      },
      done: (_) {
        _hideCurrentBottomSheet();
        resetState();
        LoadingOverlayHelper.hideLoading(context);
      },
      qrCodeExpired: (_) {
        _hideCurrentBottomSheet();
        _showBottomSheetQRLoginExpired();
      },
      orElse: () {},
    );
  }

  void resetState() {
    qrLoginBloc.add(ResetQrLoginStateEvent());
    LoadingOverlayHelper.hideLoading(context);
    AppEventBus.publish(FinishLoginQREvent(id: UniqueKey().toString()));
    _currentQRData = '';
  }

  void dispose() {
    _qrLoginSubscription?.cancel();
  }

  void _showNewLoginFloatingBottomSheet() {
    LoadingOverlayHelper.hideLoading(context);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.mounted || _bottomSheetShowed) return;
      _bottomSheetShowed = true;
      showModalBottomSheet(
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        enableDrag: true,
        isDismissible: false,
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width - 12.w,
        ),
        context: context,
        builder: (_) {
          return NewLoginBottomSheet(
            parentContext: context,
            onClose: _onTapClose,
            onApprove: _onApproveLoginQR,
            onNotMe: _onTapClose,
            processingNotifier: processingNotifier,
            isDismissible: false,
          );
        },
      ).then((_) {
        _onBottomSheetClose();
      });
    });
  }

  Future<void> _onApproveLoginQR() async {
    _passkeyMigrated =
        _sessionDataOperation.getActiveSession()!.passkeyMigrated;

    if (!(await canAuthenticate()) ||
        !(await canAuthenticateWithWebAPI(_passkeyMigrated))) {
      LoadingOverlayHelper.hideLoading(context);
      ui.DialogUtils.showAuthenticationNotSupportDialog(
        context,
        barrierDismissible: false,
        onOpenSettings: () => PasskeyAuthenticator().goToSettings(),
        onCancel: () => Navigator.of(context).pop(),
        isIos: Platform.isIOS,
        hasFaceId: await hasFaceId(),
      );
      return;
    }

    qrLoginBloc.add(QRLoginDetectedEvent(_currentQRData, _passkeyMigrated));
  }

  void _onRetryAuthQR() {
    qrLoginBloc.add(QRLoginDetectedEvent(_currentQRData, _passkeyMigrated));
    _hideCurrentBottomSheet();
  }

  void _showBottomSheetNoPasskeysAvailable() {
    processingNotifier.value = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.mounted || _bottomSheetShowed) return;
      _bottomSheetShowed = true;
      ui.BottomSheetUtil.showNoPasskeysAvailableFloatingBottomSheet(
        context: context,
        enableDrag: true,
        isDismissible: false,
        onContinue: _onTapClose,
        onLearnMore: _onTapClose,
        onClose: _onTapClose,
        onSheetClose: _onBottomSheetClose,
      );
    });
  }

  void _showBottomSheetScanQRLoginSuccess() {
    processingNotifier.value = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.mounted || _bottomSheetShowed) return;
      _bottomSheetShowed = true;
      ui.BottomSheetUtil.showScanQRLoginSuccessFloatingBottomSheet(
        isDismissible: false,
        enableDrag: true,
        context: context,
        onDone: _onTapClose,
        onClose: _onTapClose,
        onBottomSheetClose: _onBottomSheetClose,
      );
    });
  }

  void _showBottomSheetScanQRLoginFailure() {
    processingNotifier.value = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.mounted || _bottomSheetShowed) return;
      _bottomSheetShowed = true;
      ui.BottomSheetUtil.showScanQRLoginFailedFloatingBottomSheet(
        isDismissible: false,
        enableDrag: true,
        context: context,
        onRetry: _onRetryAuthQR,
        onClose: _onTapClose,
        onSheetClose: _onBottomSheetClose,
      );
    });
  }

  void _showBottomSheetQRLoginExpired() {
    processingNotifier.value = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!context.mounted || _bottomSheetShowed) return;
      _bottomSheetShowed = true;
      ui.BottomSheetUtil.showScanQRLoginExpiredFloatingBottomSheet(
        context: context,
        enableDrag: true,
        isDismissible: false,
        onClickOk: _onTapClose,
        onClose: _onTapClose,
        onSheetClose: _onBottomSheetClose,
      );
    });
  }

  /// Handle when user closes bottom sheet by action on it
  void _onTapClose() {
    if (!_bottomSheetShowed) return;
    Navigator.of(context).pop();
    _bottomSheetShowed = false;
    resetState();
  }

  /// Handle when user closes bottom sheet by system action
  void _onBottomSheetClose() {
    if (!_bottomSheetShowed) return;
    _bottomSheetShowed = false;
    resetState();
  }

  void _hideCurrentBottomSheet() {
    if (!_bottomSheetShowed) return;
    Navigator.of(context).pop();
    _bottomSheetShowed = false;
  }
}
