import 'package:shared/shared.dart';

import '../../../../chat.dart';

class ResendAttachmentEvent extends BaseEvent {
  ResendAttachmentEvent({
    required this.attachment,
    required this.messageId,
    super.source = BaseEvent.LOCAL_SOURCE,
    super.id = 'RESEND_ATTACHMENT',
  });

  final Attachment attachment;
  final String messageId;

  @override
  Map<String, dynamic> toJson() => {'attachment': attachment};
}
