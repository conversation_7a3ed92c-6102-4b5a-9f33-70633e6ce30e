import 'dart:async';
import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data_router.dart';
import 'base_websocket_data_source.dart';

/// User-specific WebSocket data source implementation
/// Simplified architecture: filters User events and broadcasts to DataOperation
///
/// New Responsibilities:
/// - Filter WebSocket events relevant to User entities
/// - Parse and validate User events
/// - Broadcast filtered events to UserDataOperation via callback
/// - No longer manages individual subscriptions (handled by LocalDataSource watch)
@LazySingleton()
class UserWebSocketDataSource extends BaseWebSocketDataSource<User> {
  final DataRouterWebSocketManager _webSocketManager;

  late final StreamSubscription _eventSubscription;
  bool _disposed = false;

  /// Callback to send filtered CloudEvent events to DataOperation
  void Function(CloudEvent event)? _dataOperationCallback;

  UserWebSocketDataSource(WebSocketProvider webSocketProvider)
      : _webSocketManager = webSocketProvider as DataRouterWebSocketManager {
    _eventSubscription = _webSocketManager.rawMessageStream
        .where(_shouldHandleMessage)
        .listen(_handleUserMessage);

    logOperation('UserWebSocketDataSource initialized - filtering User events');
  }

  @override
  String get entityName => 'User';

  @override
  void setDataOperationCallback(void Function(CloudEvent event) callback) {
    _dataOperationCallback = callback;
    logOperation('DataOperation callback set for User events');
  }

  @override
  bool get isConnected => _webSocketManager.isConnected;

  @override
  Stream<bool> get connectionStatus => _webSocketManager.connectionStatusStream;

  /// Check if this message should be handled by User data source
  bool _shouldHandleMessage(dynamic message) {
    if (message is! String) return false;

    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      final dataRouterEvent = DataRouterCloudEvent.fromJsonMap(data);

      // Check if this is a User-related CloudEvent
      return _isUserEvent(dataRouterEvent.type);
    } catch (e) {
      return false;
    }
  }

  /// Handle incoming user-related WebSocket messages
  void _handleUserMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;

      // Create DataRouterCloudEvent for enhanced functionality
      final dataRouterEvent = DataRouterCloudEvent.fromJsonMap(data);

      // Check if this is a User-related event
      if (!_isUserEvent(dataRouterEvent.type)) return;

      logOperation('Handling user CloudEvent', dataRouterEvent.type.toString());

      // Send CloudEvent to DataOperation via callback
      if (_dataOperationCallback != null) {
        _dataOperationCallback!(dataRouterEvent);
        logOperation(
          'User CloudEvent sent to DataOperation',
          '${dataRouterEvent.type} - ${dataRouterEvent.id}',
        );
      } else {
        logOperation(
          'User CloudEvent - no DataOperation callback set',
          '${dataRouterEvent.type} - ${dataRouterEvent.id}',
        );
      }
    } catch (error) {
      handleWebSocketError('CloudEvent parsing', error);
    }
  }

  /// Check if event type is User-related
  bool _isUserEvent(EventType eventType) {
    return _userEventTypes.contains(eventType);
  }

  /// User-related event types
  static final Set<EventType> _userEventTypes = {
    // User Avatar events
    EventType.AVATAR_UPDATED,
    EventType.AVATAR_DELETED,

    // User Status events
    EventType.USER_STATUS_CREATED,
    EventType.USER_STATUS_UPDATED,
    EventType.USER_STATUS_DELETED,

    // User Profile events
    EventType.DISPLAY_NAME_UPDATED,

    // User Cover events
    EventType.USER_COVER_CREATED,
    EventType.USER_COVER_UPDATED,
    EventType.USER_COVER_DELETED,

    // User Action events
    EventType.DELETED_USER,
    EventType.BLOCK_USER,
    EventType.UNBLOCK_USER,

    // Add more user event types as needed
  };

  /// Validate user data
  bool validate(User item) {
    return item.userId.isNotEmpty &&
        item.sessionKey.isNotEmpty &&
        item.username.isNotEmpty;
  }

  /// Dispose resources
  void dispose() {
    if (_disposed) return;

    _disposed = true;
    _eventSubscription.cancel();

    logOperation('UserWebSocketDataSource disposed');
  }
}
