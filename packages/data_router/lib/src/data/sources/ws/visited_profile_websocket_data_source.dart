import 'dart:async';
import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../../data_router.dart';
import 'base_websocket_data_source.dart';

/// VisitedProfile-specific WebSocket data source implementation
/// Simplified architecture: filters VisitedProfile events and broadcasts to DataOperation
///
/// New Responsibilities:
/// - Filter WebSocket events relevant to VisitedProfile entities
/// - Parse and validate VisitedProfile events
/// - Broadcast filtered events to VisitedProfileDataOperation via callback
/// - No longer manages individual subscriptions (handled by LocalDataSource watch)
@LazySingleton()
class VisitedProfileWebSocketDataSource
    extends BaseWebSocketDataSource<VisitedProfile> {
  final DataRouterWebSocketManager _webSocketManager;

  late final StreamSubscription _eventSubscription;
  bool _disposed = false;

  /// Callback to send filtered CloudEvent events to DataOperation
  void Function(CloudEvent event)? _dataOperationCallback;

  VisitedProfileWebSocketDataSource(WebSocketProvider webSocketProvider)
      : _webSocketManager = webSocketProvider as DataRouterWebSocketManager {
    _eventSubscription = _webSocketManager.rawMessageStream
        .where(_shouldHandleMessage)
        .listen(_handleVisitedProfileMessage);

    logOperation(
      'VisitedProfileWebSocketDataSource initialized - filtering VisitedProfile events',
    );
  }

  @override
  String get entityName => 'VisitedProfile';

  @override
  void setDataOperationCallback(void Function(CloudEvent event) callback) {
    _dataOperationCallback = callback;
    logOperation('DataOperation callback set for VisitedProfile events');
  }

  @override
  bool get isConnected => _webSocketManager.isConnected;

  @override
  Stream<bool> get connectionStatus => _webSocketManager.connectionStatusStream;

  /// Check if this message should be handled by VisitedProfile data source
  bool _shouldHandleMessage(dynamic message) {
    if (message is! String) return false;

    try {
      final data = jsonDecode(message) as Map<String, dynamic>;
      final dataRouterEvent = DataRouterCloudEvent.fromJsonMap(data);

      // Check if this is a VisitedProfile-related CloudEvent
      return _isVisitedProfileEvent(dataRouterEvent.type);
    } catch (e) {
      return false;
    }
  }

  /// Check if event type is related to VisitedProfile
  bool _isVisitedProfileEvent(EventType eventType) {
    return _visitedEventTypes.contains(eventType);
  }

  /// User-related event types
  static final Set<EventType> _visitedEventTypes = {
    // User Visited Profile events
    EventType.VISITED_PROFILE,
    EventType.VISITED_PROFILE_DELETE,
    EventType.CLEAR_VISITED_PROFILE_NOTIFICATIONS,

    // Add more user event types as needed
  };

  /// Handle VisitedProfile-related WebSocket messages
  void _handleVisitedProfileMessage(dynamic message) {
    if (_disposed) return;

    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final dataRouterEvent = DataRouterCloudEvent.fromJsonMap(data);
      Log.e(
          name:
              'VisitedProfileWebSocketDataSource._handleVisitedProfileMessage',
          !_isVisitedProfileEvent(dataRouterEvent.type));
      if (!_isVisitedProfileEvent(dataRouterEvent.type)) return;

      if (_dataOperationCallback != null) {
        Log.e(
            name:
                'VisitedProfileWebSocketDataSource._handleVisitedProfileMessage',
            dataRouterEvent.data.toString());
        _dataOperationCallback!(dataRouterEvent);
        logOperation(
          'User CloudEvent sent to DataOperation',
          '${dataRouterEvent.type} - ${dataRouterEvent.id}',
        );
      } else {
        logOperation(
          'User CloudEvent - no DataOperation callback set',
          '${dataRouterEvent.type} - ${dataRouterEvent.id}',
        );
      }
    } catch (e) {
      logOperation(
        'Error processing VisitedProfile WebSocket message',
        e.toString(),
      );
    }
  }

  /// Dispose resources and clean up subscriptions
  void dispose() {
    if (_disposed) return;

    logOperation('Disposing VisitedProfileWebSocketDataSource');
    _disposed = true;
    _eventSubscription.cancel();
    _dataOperationCallback = null;
  }
}
