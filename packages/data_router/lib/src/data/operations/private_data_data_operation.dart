import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../database/entities/private_data.dart';
import '../helpers/base_private_data.dart';
import '../helpers/duplicate_sort_result.dart';
import '../models/sync_key_enum.dart';
import '../sources/local/user_local_data_source.dart';
import '../sources/remote/user_remote_data_source.dart';
import '../sources/ws/events/private_data_sync_event_data.dart';

/// PrivateData Data Operation
/// Handles business logic for PrivateData entity with local operations only
///
/// This class follows the SOLID principles:
/// - Single Responsibility: Manages only PrivateData entity operations
/// - Open/Closed: Extensible for new private data operations
/// - Liskov Substitution: Properly extends BaseDataOperation
/// - Interface Segregation: Uses specific interfaces for each data source
/// - Dependency Inversion: Depends on abstractions, not concrete implementations
///
/// Note: PrivateData entities do NOT implement WebSocket/real-time event handling
/// or remote data source operations according to architectural decisions.
@Singleton()
class PrivateDataDataOperation extends BaseDataOperation<PrivateData> {
  final PrivateDataLocalDataSource _localSource;
  final PrivateDataRemoteDataSource remoteSource;
  final PrivateDataWebSocketDataSource webSocketSource;
  final UserLocalDataSource _userLocalDataSource;
  final UserRemoteDataSource _userRemoteDataSource;

  PrivateDataDataOperation(
    this._localSource,
    this.remoteSource,
    this.webSocketSource,
    this._userLocalDataSource,
    this._userRemoteDataSource,
  ) : super(
          localSource: _localSource,
          remoteSource: remoteSource,
          webSocketSource: webSocketSource,
        );

  //region WebSocket Event Handling

  @override
  Future<void> handleRealtimeEvent(DataRouterCloudEvent event) async {
    try {
      // Only handle PRIVATE_DATA_SYNC events
      if (event.type == EventType.PRIVATE_DATA_SYNC) {
        logOperation('Handling PrivateData sync event', event.type.value);
        await _handlePrivateDataSyncEvent(event);
        return;
      }

      logOperation(
        'PrivateData realtime events not supported for event type',
        event.type.value,
      );
    } catch (e) {
      logOperation(
        'Error handling PrivateData realtime event',
        'Event ignored: $e',
      );
    }
  }

  /// Handle PRIVATE_DATA_SYNC event with business rules for users, channels, callLogs
  /// - For each sync key, handle insert/update/delete logic and publish proper AppEventBus event
  Future<void> _handlePrivateDataSyncEvent(DataRouterCloudEvent event) async {
    try {
      final isOk = event.data['ok'] ?? true;

      // Parse the event data using type-safe parsing
      final syncData = event.parse<PrivateDataSyncEventData>();

      if (syncData == null) {
        return;
      }

      if (!syncData.isValid) {
        return;
      }

      // Handle each key type in sync event
      final dataKey = syncData.key.toString();

      // Case 1: Users sync
      if (dataKey == SyncKeyEnum.users.key) {
        _handleSyncUser(isOk, syncData);
        return;
      }

      // Case 2: Channels sync
      if (dataKey == SyncKeyEnum.channels.key) {
        _handleSyncChannel(isOk, syncData);
        return;
      }

      // Case 3: CallLogs sync
      if (dataKey == SyncKeyEnum.callLogs.key) {
        await _handleSyncCallLog(syncData);
        return;
      }
    } catch (e) {
      logOperation(
        'Error handling PrivateData sync event',
        '${event.id}: $e',
      );
    }
  }

  /// Handles synchronization of channel pin state from remote/local sources.
  /// - If channel exists: update its fields.
  /// - If not: create new, set up relation to parent PrivateData and Channel.
  /// Always updates ToMany<ChannelPrivateData> in PrivateData, ensures ObjectBox relations.
  ///
  /// Throws no error. Emits EventBus events for retry/success.
  ///
  /// @param isOk Indicates sync success.
  /// @param syncData Event data containing channel information.
  Future<void> _handleSyncChannel(
    bool isOk,
    PrivateDataSyncEventData syncData,
  ) async {
    if (!isOk) {
      AppEventBus.publish(
        RetryPinChannelEvent(data: syncData.value),
      );
      return;
    }

    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null) {
      print('Error: activeSessionKey is null in _handleSyncChannel');
      return;
    }

    // Get current PrivateData (entity holding channels ToMany)
    final privateData = await _localSource.get(sessionKey);
    final channels = privateData?.channels;

    if (channels == null) {
      return;
    }

    // Construct new ChannelPrivateData from event data
    final newChannelPrivateData = ChannelPrivateData.create(
      sessionKey: sessionKey,
      channelId: syncData.value['id'],
      version: syncData.value['version'] ?? 0,
      unreadCount: syncData.value['unreadCount'] ?? 0,
      lastSeenMessageId: syncData.value['lastSeenMessageId'] ?? '',
      pinned: syncData.value['pinned'] ?? false,
      sort: syncData.value['sort'] ?? 0,
      source: syncData.value['source'] ?? '',
    );

    // Find if channel already exists (by channelId)
    final index = channels.indexWhere(
      (c) => c.channelId == newChannelPrivateData.channelId,
    );

    if (index != -1) {
      // Exists: update fields, keep id
      final oldChannelData = channels[index];
      final updatedChannelData =
          newChannelPrivateData.copyWith(id: oldChannelData.id);

      // Setup parent relation
      updatedChannelData.privateData.target = privateData;

      // Update in ToMany
      channels.removeAt(index);
      channels.add(updatedChannelData);
    } else {
      // Not exist: create new, set up relations
      newChannelPrivateData.privateData.target = privateData;

      channels.add(newChannelPrivateData);
    }

    await _localSource.insert(privateData!);

    AppEventBus.publish(AddPinChannelSuccessEvent(success: true));
  }

  Future<void> _handleSyncUser(
    bool isOk,
    PrivateDataSyncEventData syncData,
  ) async {
    if (!isOk) {
      AppEventBus.publish(
        RetrySetAliasNameEvent(data: syncData.value),
      );
      return;
    }

    final userId = syncData.value['id'] as String?;
    if (userId == null) {
      logOperation('Error: userId is null in _handleSyncUser');
      return;
    }

    // Get existing UserPrivateData directly
    final existingUserPrivateData = _localSource.getUserPrivateData(userId);

    final newUserPrivateData = UserPrivateData.create(
      userId: userId,
      aliasName: syncData.value['aliasName'],
      version: syncData.value['version'],
      source: syncData.value['source'],
      dmId: syncData.value['dmId'],
      blocked: syncData.value['blocked'],
    );

    if (existingUserPrivateData != null) {
      // Update existing UserPrivateData directly
      final updatedUserPrivateData = newUserPrivateData.copyWith(
        id: existingUserPrivateData.id,
      );

      logOperation('Updating existing UserPrivateData directly', userId);
      _localSource.insertUserPrivateData(updatedUserPrivateData);
    } else {
      // Insert new UserPrivateData directly
      logOperation('Inserting new UserPrivateData directly', userId);
      _localSource.insertUserPrivateData(newUserPrivateData);

      // Update User entity to link with new UserPrivateData if needed
      var user = await _userLocalDataSource.get(userId);
      if (user == null) {
        user = await _userRemoteDataSource.load(userId);
      }

      if (user != null) {
        await _userLocalDataSource.insert(user);
      }
    }

    AppEventBus.publish(SetAliasNameSuccessEvent(success: true));
  }

  /// Handles the synchronization of call logs based on the sync data
  /// - If version < 0: delete call log
  /// - Else: create or update call log
  Future<void> _handleSyncCallLog(PrivateDataSyncEventData syncData) async {
    //Handle after
  }

  //endregion

  //region Child Entity Operations

  /// Create a new private data entity
  PrivateData createPrivateData({
    required String sessionKey,
    required String userId,
  }) {
    logOperation('Creating new private data', userId);
    return PrivateData.create(
      sessionKey: sessionKey,
      userId: userId,
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
    );
  }

  /// Synchronizes local data with remote data, correctly handling 2-way sync versioning.
  Future<void> sync() async {
    try {
      PrivateData? localData = _localSource.getPrivateData();
      final remoteData = await remoteSource.loadPrivateData();

      if (remoteData == null) {
        return;
      }

      if (localData == null) {
        final sessionKey = Config.getInstance().activeSessionKey;
        if (sessionKey == null) {
          print('Error: activeSessionKey is null in sync method');
          return;
        }
        localData = PrivateData.create(
          sessionKey: sessionKey,
          userId: sessionKey,
        );
      } else {}

      // Business logic to resolve data inconsistencies from the server.

      final duplicateResult =
          _checkAndResolveDuplicateSorts(remoteData.channels);
      if (duplicateResult.hasDuplicates) {
        for (final resolvedChannel in duplicateResult.updatedChannels) {
          final index = remoteData.channels
              .indexWhere((c) => c.channelId == resolvedChannel.channelId);
          if (index != -1) {
            remoteData.channels[index] = resolvedChannel;
          }
        }
      } else {}

      // Merge children with the final, corrected logic.

      _mergeChildren<ChannelPrivateData>(
        parent: localData,
        localChildren: localData.channels,
        remoteChildren: remoteData.channels,
        getId: (c) => c.channelId,
        updateItem: (local, remote) {
          local.version = remote.version;
          local.pinned = remote.pinned;
          local.sort = remote.sort;
          local.unreadCount = remote.unreadCount;
          local.lastSeenMessageId = remote.lastSeenMessageId;
        },
        onLocalNewer: (localChannel) {
          final event = UpdatePinChannelEvent(data: localChannel.toSyncData());
          AppEventBus.publish(event);
        },
      );

      _mergeChildren<UserPrivateData>(
        parent: localData,
        localChildren: localData.users,
        remoteChildren: remoteData.users,
        getId: (u) => u.userIdField,
        updateItem: (local, remote) {
          local.version = remote.version;
          local.aliasName = remote.aliasName;
          local.blocked = remote.blocked;
          local.dmId = remote.dmId;
        },
        onLocalNewer: (localUser) {
          final event = RetrySetAliasNameEvent(data: localUser.toSyncData());
          AppEventBus.publish(event);
        },
      );

      // Update parent metadata and save the entire object graph.
      localData.updateTime = remoteData.updateTime;
      await _localSource.insert(localData);
    } catch (e) {
      rethrow;
    }
  }

  /// [REVISED & CORRECTED LOGIC]
  /// Merges remote children into a local children list (ToMany) based on versioning.
  /// This method correctly handles 3 cases:
  /// 1. UPDATE: Item exists in both lists. It's updated if the remote version is newer.
  ///    If the local version is newer, `onLocalNewer` is called.
  /// 2. ADD: Item only exists in the remote list. It's added to the local list.
  /// 3. DELETE: Item only exists in the local list. It's removed from the local list.
  void _mergeChildren<T extends BasePrivateData>({
    required PrivateData parent,
    required List<T> localChildren,
    required List<T> remoteChildren,
    required String Function(T) getId,
    required void Function(T local, T remote) updateItem,
    void Function(T localItem)? onLocalNewer,
  }) {
    final localMap = {for (var item in localChildren) getId(item): item};
    final remoteMap = {for (var item in remoteChildren) getId(item): item};

    final List<T> itemsToAdd = [];
    final List<T> itemsToRemove = [];

    // Case 1: Handle updates and new items from remote.
    for (final remoteChild in remoteChildren) {
      final remoteId = getId(remoteChild);
      final localChild = localMap[remoteId];

      if (localChild != null) {
        // --- UPDATE Case ---
        // Item exists locally. Compare versions.

        if (localChild.objectVersion > remoteChild.objectVersion) {
          // Local data is newer. Do not update.
          // Instead, trigger an event to sync local changes to the server.

          onLocalNewer?.call(localChild);
        } else {
          // Remote data is newer or the same. Update local data.

          updateItem(localChild, remoteChild);
        }
      } else {
        // --- ADD Case ---
        // Item does not exist locally, so add it.
        // The type `T` will be either UserPrivateData or ChannelPrivateData,
        // which have the 'privateData' property.

        (remoteChild as dynamic).privateData.target = parent;
        itemsToAdd.add(remoteChild);
      }
    }

    // Case 2: Handle items that were deleted on the remote.
    for (final localChild in localChildren) {
      final localId = getId(localChild);
      if (!remoteMap.containsKey(localId)) {
        // --- DELETE Case ---
        // Item exists locally but not on remote, so remove it.

        itemsToRemove.add(localChild);
      }
    }

    // Case 3: Apply all changes to the local list (ToMany).
    // Using `removeWhere` is safer than `removeAll` with ToMany lists.
    if (itemsToRemove.isNotEmpty) {
      final idsToRemove = itemsToRemove.map(getId).toSet();
      localChildren.removeWhere((item) => idsToRemove.contains(getId(item)));
    }
    if (itemsToAdd.isNotEmpty) {
      localChildren.addAll(itemsToAdd);
    }
  }

  // The rest of your class methods remain below.
  DuplicateSortResult _checkAndResolveDuplicateSorts(
    List<ChannelPrivateData> channels,
  ) {
    if (channels.isEmpty) {
      return DuplicateSortResult(
        hasDuplicates: false,
        updatedChannels: channels,
      );
    }
    final pinnedChannels =
        channels.where((c) => c.pinned == true && c.sort > 0).toList();
    if (pinnedChannels.isEmpty) {
      return DuplicateSortResult(hasDuplicates: false, updatedChannels: []);
    }
    pinnedChannels.sort((a, b) => (a.sort).compareTo(b.sort));
    bool hasDuplicates = false;
    final sortValues = <int, List<ChannelPrivateData>>{};
    for (final channel in pinnedChannels) {
      sortValues.putIfAbsent(channel.sort, () => []);
      sortValues[channel.sort]!.add(channel);
      if (sortValues[channel.sort]!.length > 1) {
        hasDuplicates = true;
      }
    }
    if (!hasDuplicates) {
      return DuplicateSortResult(hasDuplicates: false, updatedChannels: []);
    }
    final updatedChannels = <ChannelPrivateData>[];
    final usedSorts = <int>{};
    for (final channel in channels) {
      if (channel.pinned == true) {
        usedSorts.add(channel.sort);
      }
    }
    for (final entry in sortValues.entries) {
      final sort = entry.key;
      final channelsWithSameSort = entry.value;
      if (channelsWithSameSort.length <= 1) continue;
      channelsWithSameSort.sort((a, b) => a.channelId.compareTo(b.channelId));
      for (int i = 1; i < channelsWithSameSort.length; i++) {
        final channel = channelsWithSameSort[i];
        int newSort = sort;
        while (usedSorts.contains(newSort)) {
          newSort++;
        }
        channel.sort = newSort;
        channel.version += 1;
        usedSorts.add(newSort);
        updatedChannels.add(channel);
      }
    }
    return DuplicateSortResult(
      hasDuplicates: true,
      updatedChannels: updatedChannels,
    );
  }

  UserPrivateData? getUserPrivateData(String userId) {
    // Validate userId is not empty to prevent ObjectBox crash
    if (userId.trim().isEmpty) {
      print('Error: userId is empty in getUserPrivateData');
      return null;
    }

    return _localSource.getUserPrivateData(userId);
  }

  List<UserPrivateData> getPrivateDataByIds(List<String> listUserId) {
    return _localSource.getPrivateDataByIds(listUserId);
  }

  void insertUserPrivateData(UserPrivateData dUserPrivateData) {
    _localSource.insertUserPrivateData(dUserPrivateData);
  }

  Stream<List<UserPrivateData>> watchAllUserPrivateData() {
    return _localSource.watchAllUserPrivateData();
  }

  ChannelPrivateData? getChannelPrivateData(String channelId) {
    return _localSource.getChannelPrivateData(channelId);
  }

  void insertChannelPrivateData(ChannelPrivateData dChannelPrivateData) {
    _localSource.insertChannelPrivateData(dChannelPrivateData);
  }

  int? getMaxSortChannel() {
    return _localSource.getMaxSortChannel();
  }

  //region CallLogPrivateData Operations

  /// Get call log private data by call ID directly from local source
  CallLogPrivateData? getCallLogPrivateData(String callId) {
    return _localSource.getCallLogPrivateData(callId);
  }

  /// Watch call log private data changes directly from local source
  Stream<CallLogPrivateData?> watchCallLogPrivateData(String callId) {
    return _localSource.watchCallLogPrivateData(callId);
  }

  /// Watch all call log private data changes directly from local source
  Stream<List<CallLogPrivateData>> watchAllCallLogPrivateData() {
    return _localSource.watchAllCallLogPrivateData();
  }

  /// Get all call log private data directly from local source
  List<CallLogPrivateData> getAllCallLogPrivateData() {
    return _localSource.getAllCallLogPrivateData();
  }

  /// Delete call log private data directly through local source
  bool deleteCallLogPrivateData(String callId) {
    return _localSource.deleteCallLogPrivateData(callId);
  }

  //endregion

  //region Additional UserPrivateData Operations

  /// Watch user private data changes directly from local source
  Stream<UserPrivateData?> watchUserPrivateData(String userId) {
    return _localSource.watchUserPrivateData(userId);
  }

  /// Get all user private data directly from local source
  List<UserPrivateData> getAllUserPrivateData() {
    return _localSource.getAllUserPrivateData();
  }

  /// Delete user private data directly through local source
  bool deleteUserPrivateData(String userId) {
    return _localSource.deleteUserPrivateData(userId);
  }

  //endregion

  //region Additional ChannelPrivateData Operations

  /// Watch channel private data changes directly from local source
  Stream<ChannelPrivateData?> watchChannelPrivateData(String channelId) {
    return _localSource.watchChannelPrivateData(channelId);
  }

  /// Watch all channel private data changes directly from local source
  Stream<List<ChannelPrivateData>> watchAllChannelPrivateData() {
    return _localSource.watchAllChannelPrivateData();
  }

  /// Get all channel private data directly from local source
  List<ChannelPrivateData> getAllChannelPrivateData() {
    return _localSource.getAllChannelPrivateData();
  }

  /// Delete channel private data directly through local source
  bool deleteChannelPrivateData(String channelId) {
    return _localSource.deleteChannelPrivateData(channelId);
  }

  //endregion

  /// Delete all private data by session key
  /// This is a PrivateData-specific operation for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting private data by session key', sessionKey);
      await _localSource.deleteAllBySessionKey(sessionKey);
      logOperation(
        '✅ Successfully deleted all private data for session',
        sessionKey,
      );
    } catch (e) {
      logOperation(
        '❌ Error deleting private data by session key',
        '$sessionKey: $e',
      );
      rethrow;
    }
  }
}
