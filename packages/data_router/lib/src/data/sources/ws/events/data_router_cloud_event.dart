import 'dart:convert';

import 'package:shared/shared.dart';

import 'channel_avatar_updated_event_data.dart';
// Channel events
import 'channel_created_event_data.dart';
import 'channel_deleted_event_data.dart';
import 'channel_name_updated_event_data.dart';
import 'channel_updated_event_data.dart';
import 'data_router_event_data.dart';
import 'dm_channel_created_event_data.dart';
// Private Data events
import 'private_data_sync_event_data.dart';
// Session events
import 'friend_removed_event_data.dart';
import 'session_created_event_data.dart';
// User events
import 'user_avatar_deleted_event_data.dart';
import 'user_avatar_updated_event_data.dart';
import 'user_blocked_event_data.dart';
import 'user_cover_created_event_data.dart';
import 'user_cover_deleted_event_data.dart';
import 'user_cover_updated_event_data.dart';
import 'user_display_name_updated_event_data.dart';
import 'user_status_deleted_event_data.dart';
import 'user_status_updated_event_data.dart';
import 'user_unblocked_event_data.dart';
import 'user_visited_profile_deleted_event_data.dart';
import 'user_visited_profile_event_data.dart';

/// Data Router specific CloudEvent extension
/// Provides type-safe access to CloudEvent data with entity-specific parsing
class DataRouterCloudEvent extends CloudEvent {
  /// Creates from CloudEvent properties
  DataRouterCloudEvent(
    super.type,
    super.data,
    super.time, {
    required super.id,
    required super.source,
    super.specVersion = CloudEvent.DEFAULT_SPEC_VERSION,
  });

  /// Creates from raw CloudEvent
  factory DataRouterCloudEvent.fromCloudEvent(CloudEvent cloudEvent) {
    return DataRouterCloudEvent(
      cloudEvent.type,
      cloudEvent.data,
      cloudEvent.time,
      id: cloudEvent.id,
      source: cloudEvent.source,
      specVersion: cloudEvent.specVersion,
    );
  }

  /// Creates from JSON string
  factory DataRouterCloudEvent.fromJson(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    final cloudEvent = CloudEvent.fromJson(json);
    return DataRouterCloudEvent.fromCloudEvent(cloudEvent);
  }

  /// Creates from JSON Map
  factory DataRouterCloudEvent.fromJsonMap(Map<String, dynamic> json) {
    final cloudEvent = CloudEvent.fromJson(json);
    return DataRouterCloudEvent.fromCloudEvent(cloudEvent);
  }

  /// Checks if this is a User-related event
  bool get isUserEvent {
    return _userEventTypes.contains(type);
  }

  /// Checks if this is a Session-related event
  bool get isSessionEvent {
    return _sessionEventTypes.contains(type);
  }

  /// Checks if this is a Channel-related event
  bool get isChannelEvent {
    return _channelEventTypes.contains(type);
  }

  /// Registry of event type to parser function mappings
  /// Each parser function takes JSON data and returns a typed event data instance
  static final Map<EventType,
      DataRouterEventData Function(Map<String, dynamic>)> _parsers = {
    // User Avatar events
    EventType.AVATAR_UPDATED: (json) =>
        UserAvatarUpdatedEventData.fromJson(json),
    EventType.AVATAR_DELETED: (json) =>
        UserAvatarDeletedEventData.fromJson(json),
    // User Status events
    EventType.USER_STATUS_CREATED: (json) =>
        UserStatusUpdatedEventData.fromJson(json),
    EventType.USER_STATUS_UPDATED: (json) =>
        UserStatusUpdatedEventData.fromJson(json),
    EventType.USER_STATUS_DELETED: (json) =>
        UserStatusDeletedEventData.fromJson(json),

    // User Profile events
    EventType.DISPLAY_NAME_UPDATED: (json) =>
        UserDisplayNameUpdatedEventData.fromJson(json),

    // User Cover events
    EventType.USER_COVER_CREATED: (json) =>
        UserCoverCreatedEventData.fromJson(json),
    EventType.USER_COVER_UPDATED: (json) =>
        UserCoverUpdatedEventData.fromJson(json),
    EventType.USER_COVER_DELETED: (json) =>
        UserCoverDeletedEventData.fromJson(json),

    // User Block/Unblock events
    EventType.BLOCK_USER: (json) => UserBlockedEventData.fromJson(json),
    EventType.UNBLOCK_USER: (json) => UserUnblockedEventData.fromJson(json),

    // User Visited Profile events
    EventType.VISITED_PROFILE: (json) =>
        UserVisitedProfileEventData.fromJson(json),
    EventType.VISITED_PROFILE_DELETE: (json) =>
        UserVisitedProfileDeletedEventData.fromJson(json),

    // Session-related events
    // Note: Using DEVICE_UNLINKED as placeholder for session events
    // TODO: Update when proper session event types are available
    EventType.DEVICE_UNLINKED: (json) => SessionCreatedEventData.fromJson(json),

    // Channel-related events
    EventType.CHANNEL_CREATED: (json) => ChannelCreatedEventData.fromJson(json),
    EventType.CHANNEL_UPDATED: (json) => ChannelUpdatedEventData.fromJson(json),
    EventType.CHANNEL_DELETED: (json) => ChannelDeletedEventData.fromJson(json),
    EventType.CHANNEL_NAME_UPDATED: (json) =>
        ChannelNameUpdatedEventData.fromJson(json),
    EventType.CHANNEL_AVATAR_UPDATED: (json) =>
        ChannelAvatarUpdatedEventData.fromJson(json),
    EventType.CHANNEL_DM_CREATE: (json) =>
        DMChannelCreatedEventData.fromJson(json),

    // Private Data events
    EventType.PRIVATE_DATA_SYNC: (json) =>
        PrivateDataSyncEventData.fromJson(json),

    // TODO: Add more event parsers as needed:
    // EventType.SESSION_CREATED: (json) => SessionCreatedEventData.fromJson(json),
    // EventType.SESSION_UPDATED: (json) => SessionUpdatedEventData.fromJson(json),
    // EventType.CLEAR_VISITED_PROFILE_NOTIFICATIONS: (json) => ClearVisitedProfileNotificationsEventData.fromJson(json),

    // Friend data event
    EventType.FRIEND_UNFRIENDED: (json) =>
        FriendRemovedEventData.fromJson(json),
  };

  /// Generic method to parse event data with type safety
  ///
  /// Returns the parsed and validated event data of type [T], or null if:
  /// - No parser is registered for the current event type
  /// - Parsing fails due to invalid JSON structure
  /// - Validation fails (isValid returns false)
  ///
  /// Usage:
  /// ```dart
  /// final avatarData = event.parse<UserAvatarUpdatedEventData>();
  /// if (avatarData != null) {
  ///   // Process valid avatar update event
  /// }
  /// ```
  T? parse<T extends DataRouterEventData>() {
    try {
      // Look up parser for current event type
      final parser = _parsers[type];
      if (parser == null) return null;

      // Parse the JSON data
      final eventData = parser(data);

      // Ensure the result is of the expected type
      if (eventData is! T) return null;

      // Validate the parsed data
      if (!eventData.isValid) return null;

      return eventData;
    } catch (e) {
      // Log error if needed (could add logging here)
      return null;
    }
  }

  /// User-related event types
  static final Set<EventType> _userEventTypes = {
    // User Avatar events
    EventType.AVATAR_UPDATED,
    EventType.AVATAR_DELETED,

    // User Status events
    EventType.USER_STATUS_CREATED,
    EventType.USER_STATUS_UPDATED,
    EventType.USER_STATUS_DELETED,

    // User Profile events
    EventType.DISPLAY_NAME_UPDATED,

    // User Cover events
    EventType.USER_COVER_CREATED,
    EventType.USER_COVER_UPDATED,
    EventType.USER_COVER_DELETED,

    // User Action events
    EventType.DELETED_USER,
    EventType.BLOCK_USER,
    EventType.UNBLOCK_USER,

    // User Visited Profile events
    EventType.VISITED_PROFILE,
    EventType.VISITED_PROFILE_DELETE,
    EventType.CLEAR_VISITED_PROFILE_NOTIFICATIONS,

    // Add more user event types as needed
  };

  /// Session-related event types
  /// Note: Sessions are typically managed through IAM events
  static final Set<EventType> _sessionEventTypes = {
    EventType.DEVICE_UNLINKED,
    // Add more session-related event types as they become available
    // Most session management happens through IAM service
  };

  /// Channel-related event types
  static final Set<EventType> _channelEventTypes = {
    // Channel CRUD events
    EventType.CHANNEL_CREATED,
    EventType.CHANNEL_UPDATED,
    EventType.CHANNEL_DELETED,

    // Channel Property events
    EventType.CHANNEL_NAME_UPDATED,
    EventType.CHANNEL_AVATAR_UPDATED,

    // DM Channel events
    EventType.CHANNEL_DM_CREATE,

    EventType.INCOMING_MESSAGE_REQUEST_CREATED,
    EventType.INCOMING_MESSAGE_REQUEST_ACCEPTED,

    // Add more channel event types as needed
  };

  @override
  String toString() {
    return 'DataRouterCloudEvent{type: $type, id: $id, source: $source, time: $time}';
  }
}
