import 'dart:async';

import 'package:app_core/core.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:qr_scanner/qr_scanner.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart' as user;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class QrScannerPage extends StatefulWidget {
  const QrScannerPage({super.key});

  @override
  State<QrScannerPage> createState() => _QrScannerPageState();
}

class _QrScannerPageState extends BasePageState<QrScannerPage, ScanQrBloc>
    implements ui.QRScannerInterface {
  final GlobalKey<QRScannerPageState> qrScannerKey = GlobalKey();
  bool _pauseDetectCamera = false;

  StreamSubscription? _onCloseInvitationBottomSheetSubscription;
  StreamSubscription? _onFinishLoginQRSubscription;

  @override
  void initState() {
    super.initState();
    _onCloseInvitationBottomSheetSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<OnCloseInvitationBottomSheetEvent>()
        .listen((_) => _resetBlocState());
    _onFinishLoginQRSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<FinishLoginQREvent>()
        .listen((_) => _resetBlocState());
  }

  @override
  void dispose() {
    _onCloseInvitationBottomSheetSubscription?.cancel();
    _onFinishLoginQRSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return ClipRect(
      child: BlocListener<ScanQrBloc, ScanQrState>(
        listener: (context, state) => _handleState(state),
        child: BlocBuilder<ScanQrBloc, ScanQrState>(
          builder: (context, state) {
            return AnimatedSwitcher(
              duration: DurationUtils.ms500,
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                  alwaysIncludeSemantics: true,
                );
              },
              child: state.maybeWhen(
                initial: () {
                  return ui.QRScannerPage(
                    key: qrScannerKey,
                    interface: this,
                  );
                },
                processing: (_) {
                  return ui.QRScannerPage(
                    key: qrScannerKey,
                    interface: this,
                  );
                },
                done: (_) {
                  return ui.QRScannerPage(
                    key: qrScannerKey,
                    interface: this,
                  );
                },
                orElse: () {
                  return Container(
                    color: AppColors.gray80,
                    constraints: BoxConstraints.expand(),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void onException(Exception exception) {
    Log.e(exception);
  }

  @override
  void onReadQR(String qrValue) {
    if (_pauseDetectCamera) return;
    _updateDetectState(true);
    bloc.add(QRDataDetectedEvent(qrValue));
  }

  @override
  void onTapMyQR() {
    _updateDetectState(true);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) {
        return FractionallySizedBox(
          heightFactor: 0.95,
          child: user.MyQrBottomSheet(),
        );
      },
    ).whenComplete(() {
      _updateDetectState(false);
    });
  }

  @override
  void onBack() {
    context.maybePop();
  }

  @override
  bool pauseDetect() {
    return _pauseDetectCamera;
  }

  void _handleState(ScanQrState state) {
    state.maybeWhen(
      initial: () {
        Future.delayed(DurationUtils.ms1000, () {
          _updateDetectState(false);
        });
        LoadingOverlayHelper.hideLoading(context);
      },
      processing: (_) {
        _updateDetectState(true);
      },
      done: (_) {
        _updateDetectState(false);
      },
      authQRLoginDetected: (qrData) {
        _updateDetectState(true);
        GetIt.instance.get<AppEventBus>().fire(
              HandleLoginQREvent(
                id: UniqueKey().toString(),
                qrValue: qrData,
              ),
            );
      },
      scanToConnectLinkDetected: (_) {
        // LoadingOverlayHelper.showLoading(context);
        // Không cần thêm sự kiện mới, chờ trạng thái userConnectLinkDecodeSuccessful
      },
      userConnectLinkDecodeSuccessful: (userId) {
        _navigateToUserProfileOrMe(userId);
      },
      invitationDetected: (qrData) {
        AppEventBus.publish(OnInvitationClickedEvent(invitationLink: qrData));
      },
      invalidQRDetected: (_) {
        _showInvalidQRDialog();
      },
      orElse: () {},
    );
  }

  void _updateDetectState(bool pauseDetect) {
    _pauseDetectCamera = pauseDetect;
    qrScannerKey.currentState
        ?.togglePauseDetect(pauseDetect: _pauseDetectCamera);
  }

  Future<void> _navigateToUserProfileOrMe(String userId) async {
    LoadingOverlayHelper.hideLoading(context);
    if (userId == Config.getInstance().activeSessionKey) {
      AppEventBus.publish(OnGoToHomeEvent(tab: HomeTab.profile));
    } else {
      await context.pushRoute(UserProfileRoute(userId: userId));
      _resetBlocState();
    }
  }

  Future<void> _showInvalidQRDialog() async {
    LoadingOverlayHelper.hideLoading(context);
    await ui.DialogUtils.showAppFutureDialog(
      context: context,
      buildDialogContent: (BuildContext dialogContext) {
        return AppCustomDialog(
          maxWidth: 350.w,
          title: AppLocalizations.of(context)!.invalidQRCode,
          firstAction: AppLocalizations.of(context)!.ok,
          onFirstAction: () => Navigator.of(dialogContext).pop(),
        );
      },
    );
    _resetBlocState();
  }

  void _resetBlocState() {
    bloc.add(ResetScanQrStateEvent());
  }

  @override
  Future<List<ui.UiQrResult>> scanFromFilePath(String filePath) async {
    _updateDetectState(true);

    final result = await QRScannerUtils.detectFromFilePath(filePath: filePath);

    _updateDetectState(false);

    return result
        .map(
          (e) => ui.UiQrResult(boundingBox: e.boundingBox, value: e.rawValue!),
        )
        .toList();
  }

  @override
  Future<List<ui.UiQrResult>> scanOnImageStream(
    ui.UiQrImage imageOnStream,
  ) async {
    if (_pauseDetectCamera) return [];
    _pauseDetectCamera = true;
    final result = await QRScannerUtils.detectFromCameraImage(
      image: QRCameraImage(
        height: imageOnStream.cameraImage.height,
        width: imageOnStream.cameraImage.width,
        planes: imageOnStream.cameraImage.planes
            .map(
              (e) => Plane(
                bytes: e.bytes,
                bytesPerPixel: e.bytesPerPixel,
                bytesPerRow: e.bytesPerRow,
              ),
            )
            .toList(),
        format: imageOnStream.cameraImage.format.raw as int,
        lensDirection: LensDirection.fromName(imageOnStream.lensDirection.name),
        imageRotation: imageOnStream.cameraSensorOrientation,
      ),
      deviceOrientation:
          imageOnStream.recordingOrientation ?? DeviceOrientation.portraitUp,
    );

    _updateDetectState(false);

    return result
        .map(
          (e) => ui.UiQrResult(boundingBox: e.boundingBox, value: e.rawValue!),
        )
        .toList();
  }
}
