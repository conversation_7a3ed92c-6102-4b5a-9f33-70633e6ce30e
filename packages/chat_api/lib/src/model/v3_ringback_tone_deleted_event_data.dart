//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneDeletedEventData {
  /// Returns a new [V3RingbackToneDeletedEventData] instance.
  V3RingbackToneDeletedEventData({
    this.ringbackToneId,
  });

  @JsonKey(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneDeletedEventData &&
          other.ringbackToneId == ringbackToneId;

  @override
  int get hashCode => ringbackToneId.hashCode;

  factory V3RingbackToneDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
