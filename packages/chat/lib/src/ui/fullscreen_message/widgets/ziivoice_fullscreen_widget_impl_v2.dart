import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../chat.dart';
import '../player/ziivoice_fullscreen_player.dart';
import 'base/base_fullscreen_widget.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'package:shared/shared.dart';

class ZiiVoiceFullScreenWidgetDetector extends StatefulWidget {
  const ZiiVoiceFullScreenWidgetDetector({
    super.key,
    required this.url,
    required this.path,
    required this.messageItem,
    this.caption = '',
  });
  final String? url;
  final String? path;
  final String caption;
  final ui.MessageItem messageItem;

  @override
  State<ZiiVoiceFullScreenWidgetDetector> createState() =>
      _ZiiVoiceFullScreenWidgetDetectorState();
}

class _ZiiVoiceFullScreenWidgetDetectorState
    extends State<ZiiVoiceFullScreenWidgetDetector>
    with AutomaticKeepAliveClientMixin {
  bool isActive = false;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Positioned.fill(
      child: VisibilityDetector(
        key: ValueKey('visibility_${widget.messageItem.messageId}'),
        child: ZiiVoiceFullscreenPlayer(
          url: widget.url,
          path: widget.path,
          isActive: isActive,
          messageItem: widget.messageItem,
        ),
        onVisibilityChanged: (info) {
          if (!mounted) return;
          if (info.visibleBounds == Rect.zero) {
            setState(() {
              isActive = false;
            });
          } else if (info.visibleFraction == 1.0) {
            setState(() {
              isActive = true;
            });
          }
        },
      ),
    );
  }
}

class ZiiVoiceFullscreenWidgetImplV2 extends BaseFullscreenWidget {
  ZiiVoiceFullscreenWidgetImplV2({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final voiceMessage =
        message.mediaAttachments.last.voiceMessage ?? MediaObject();

    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);
    Log.e(
        name: 'ZiiVoiceFullscreenWidgetImplV2.build',
        messageItem.messageId.hashCode);

    return ui.ViewFullScreenZiiVoiceWidget(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      key: ValueKey('${messageItem.messageId}'),
      totalReactions: ValueNotifier(totalReactions),
      quickReact: quickReact,
      waveFormWidget: ZiiVoiceFullScreenWidgetDetector(
        url: UrlUtils.parseCDNUrl(voiceMessage.fileUrl),
        path: voiceMessage.filePath,
        messageItem: messageItem,
      ),
    );
  }
}
