//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_cover_photo_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CoverPhotoDeletedEventData {
  /// Returns a new [V3CoverPhotoDeletedEventData] instance.
  V3CoverPhotoDeletedEventData({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CoverPhotoDeletedEventData && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3CoverPhotoDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3CoverPhotoDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3CoverPhotoDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
