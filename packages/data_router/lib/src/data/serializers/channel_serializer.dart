import 'package:chat_api/chat_api.dart' as chat_api;
import 'package:shared/shared.dart';

import '../../../data_router.dart';

/// Serializer for Channel entity
/// Handles conversion between API models and internal Channel entities
class ChannelSerializer {
  /// Convert from ChatAPI V3Channel to internal Channel entity
  /// Optionally processes V3DataInclude to extract and populate channel metadata
  static Channel fromV3Channel(
    chat_api.V3Channel v3Channel, {
    chat_api.V3DataInclude? includes,
  }) {
    final sessionKey = Config.getInstance().activeSessionKey ?? '';
    final channel = Channel.create(
      workspaceId: v3Channel.workspaceId ?? '',
      channelId: v3Channel.channelId ?? '',
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      // V3Channel uses userId for owner
      channelOwnerUserId: v3Channel.userId ?? '',
      recipientId: v3Channel.participantIds
              ?.firstWhere((id) => id != sessionKey, orElse: () => '') ??
          '',
      participantId: v3Channel.participantIds
              ?.firstWhere((id) => id != sessionKey, orElse: () => '') ??
          '',
      dmChannelId: '',
      name: v3Channel.name ?? '',
      avatar: v3Channel.avatar ?? '',
      originalAvatar: v3Channel.originalAvatar ?? '',
      channelTypeRaw: int.parse(v3Channel.type!.value),
      description: '',
      createTime: TimeUtils.parseUTCStringToDateTime(v3Channel.createTime),
      updateTime: TimeUtils.parseUTCStringToDateTime(v3Channel.updateTime),
      // Not available in V3Channel
      isArchived: false,
      invitationLink: v3Channel.invitationLink ?? '',
      isPartial: false,
      totalMembers: v3Channel.totalMembers ?? 0,
      dmStatusRaw: v3Channel.dmStatus?.index ?? 0,
      participantIdsString: v3Channel.participantIds?.join(',') ?? '',
      rejectTime: v3Channel.rejectTime ?? '',
      acceptTime: v3Channel.acceptTime ?? '',
    );

    // Process includes data to populate channel metadata
    if (includes != null) {
      _populateChannelMetadataFromIncludes(channel, includes);
    }

    return channel;
  }

  /// Convert from ChatAPI V3ChannelCreatedEventData to internal Channel entity
  static Channel? fromV3ChannelCreatedEventData(
    chat_api.V3ChannelCreatedEventData eventData,
  ) {
    if (eventData.channel == null) return null;
    return fromV3Channel(eventData.channel!, includes: eventData.includes);
  }

  /// Convert from ChatAPI V3ChannelUpdatedEventData to internal Channel entity
  static Channel? fromV3ChannelUpdatedEventData(
    chat_api.V3ChannelUpdatedEventData eventData,
  ) {
    if (eventData.channel == null) return null;
    return fromV3Channel(eventData.channel!, includes: eventData.includes);
  }

  /// Convert from ChatAPI V3DMChannelCreatedEventData to internal Channel entity
  static Channel? fromV3DMChannelCreatedEventData(
    chat_api.V3DMChannelCreatedEventData eventData,
  ) {
    if (eventData.channel == null) return null;
    return fromV3Channel(eventData.channel!, includes: eventData.includes);
  }

  /// Create Channel entity from JSON data
  static Channel fromJson(Map<String, dynamic> json) {
    return Channel.create(
      workspaceId: json['workspaceId'] as String? ?? '',
      channelId: json['channelId'] as String? ?? '',
      sessionKey: json['sessionKey'] as String? ?? '',
      channelOwnerUserId: json['channelOwnerUserId'] as String? ?? '',
      recipientId: json['recipientId'] as String? ?? '',
      participantId: List<String>.from(json['participantIds'])
          .firstWhere((id) => id != json['sessionKey'], orElse: () => ''),
      dmChannelId: json['dmChannelId'] as String? ?? '',
      name: json['name'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      originalAvatar: json['originalAvatar'] as String? ?? '',
      invitationLink: json['invitationLink'] as String,
      channelTypeRaw: json['channelTypeRaw'] as int? ?? 0,
      topic: json['topic'] as String? ?? '',
      description: json['description'] as String? ?? '',
      createTime: TimeUtils.parseUTCStringToDateTime(json['createTime']),
      updateTime: TimeUtils.parseUTCStringToDateTime(json['updateTime']),
      isArchived: json['isArchived'] as bool? ?? false,
      isPartial: json['isPartial'] as bool? ?? false,
    );
  }

  /// Populate channel metadata from V3DataInclude
  static void _populateChannelMetadataFromIncludes(
    Channel channel,
    chat_api.V3DataInclude includes,
  ) {
    // Find channel metadata for this specific channel
    final v3ChannelMetadata = _findChannelMetadata(
      includes.channelMetadata,
      channel.workspaceId,
      channel.channelId,
    );

    // Find the latest message for this channel
    final latestMessage = _findLatestMessageForChannel(
      includes.messages,
      channel.workspaceId,
      channel.channelId,
    );

    // Create and populate ChannelMetadata entity
    if (v3ChannelMetadata != null || latestMessage != null) {
      final metadata = ChannelMetadata.create(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        sessionKey: channel.sessionKey,
        unreadCount: v3ChannelMetadata?.unreadCount ?? 0,
        lastMessageId:
            v3ChannelMetadata?.lastMessageId ?? latestMessage?.messageId ?? '',
        notificationStatus: v3ChannelMetadata?.notificationStatus ?? true,
        permissions: _mapV3PermissionsToIntList(v3ChannelMetadata?.permissions),
      );

      // Set additional metadata from latest message if available
      if (latestMessage != null) {
        metadata.lastMessageContent = latestMessage.content ?? '';
        metadata.lastMessageContentArguments =
            latestMessage.contentArguments ?? [];
        metadata.lastMessageMentions = latestMessage.mentions ?? [];
        // Map message type to view type if needed
        // TODO: Consider using the view type from the message
        metadata.lastMessageViewTypeRaw =
            _mapMessageTypeToViewType(latestMessage.messageType);
        metadata.createTime =
            TimeUtils.parseUTCStringToDateTime(latestMessage.createTime);
        channel.lastMessageCreateTime = metadata.createTime;
      }

      // Link metadata to channel
      channel.metadata.target = metadata;
    }
  }

  /// Find channel metadata for a specific channel from V3DataInclude
  static chat_api.V3ChannelMetadata? _findChannelMetadata(
    List<chat_api.V3ChannelMetadata>? channelMetadataList,
    String workspaceId,
    String channelId,
  ) {
    if (channelMetadataList == null || channelMetadataList.isEmpty) {
      return null;
    }

    try {
      return channelMetadataList.firstWhere(
        (metadata) =>
            metadata.workspaceId == workspaceId &&
            metadata.channelId == channelId,
        orElse: () => throw StateError('Not found'),
      );
    } catch (e) {
      // No matching metadata found
      return null;
    }
  }

  /// Find the latest message for a specific channel from V3DataInclude
  static chat_api.V3Message? _findLatestMessageForChannel(
    List<chat_api.V3Message>? messages,
    String workspaceId,
    String channelId,
  ) {
    if (messages == null || messages.isEmpty) {
      return null;
    }

    // Filter messages for this channel and find the latest one
    final channelMessages = messages.where(
      (message) =>
          message.workspaceId == workspaceId && message.channelId == channelId,
    );

    if (channelMessages.isEmpty) {
      return null;
    }

    // Sort by createTime and return the latest
    final sortedMessages = channelMessages.toList()
      ..sort((a, b) {
        final aTime = a.createTime;
        final bTime = b.createTime;
        if (aTime == null && bTime == null) return 0;
        if (aTime == null) return -1;
        if (bTime == null) return 1;
        return bTime.compareTo(aTime); // Descending order (latest first)
      });

    return sortedMessages.first;
  }

  /// Map V3 permissions to internal integer list format
  static List<int>? _mapV3PermissionsToIntList(
    List<chat_api.V3ChannelPermissionsEnum>? v3Permissions,
  ) {
    if (v3Permissions == null || v3Permissions.isEmpty) {
      return null;
    }

    return v3Permissions.map((permission) => permission.index).toList();
  }

  /// Map V3 message type to internal view type raw value
  static int _mapMessageTypeToViewType(
    chat_api.V3MessageTypeEnum? messageType,
  ) {
    if (messageType == null) return 3; // Default to text type

    // Map based on V3MessageTypeEnum values to MessageViewType values
    switch (messageType) {
      case chat_api.V3MessageTypeEnum.MESSAGE_TYPE_ENUM_DEFAULT:
        return 3; // text type
      case chat_api.V3MessageTypeEnum.MESSAGE_TYPE_ENUM_AUDIT_LOG:
        return 2; // system type
    }
  }
}
