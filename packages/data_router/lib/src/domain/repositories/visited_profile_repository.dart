import 'dart:async';

import '../../../data_router.dart';

abstract class VisitedProfileRepository {
  VisitedProfile? getByVisitedUserId(String visitedUserId);

  List<VisitedProfile> getAllForCurrentSession();

  Stream<List<VisitedProfile>> watchVisitedProfiles();

  List<VisitedProfile> getAllVisitedProfiles();

  Stream<VisitedProfile?> watchByVisitedUserId(String visitedUserId);

  bool deleteByVisitedUserId(String visitedUserId);

  int markAllAsRead();

  bool markAsReadByVisitedUserId(String visitedUserId);

  bool markAsUnreadByVisitedUserId(String visitedUserId);

  int getUnreadCount();

  int insertVisitedProfile(VisitedProfile item);

  Future<void> insertAllVisitedProfile(List<VisitedProfile> items);

  Future<void> syncVisitedProfiles(List<VisitedProfile> items);

  List<VisitedProfile> getRecent({int limit = 50});

  List<VisitedProfile> getByDateRange(DateTime startDate, DateTime endDate);

  bool exists(String visitedUserId);

  void deleteAllBySessionKey(String sessionKey);

  int deleteAll();

  VisitedProfile updateVisitTime(String visitedUserId);
}
