import 'dart:async';

import 'package:app_core/core.dart';
import 'package:data_router/data_router.dart' as data;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart' as chat;
import '../../common/di/di.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import 'bloc/message_request_bloc.dart';

class MessageRequestListView extends StatefulWidget {
  const MessageRequestListView({
    required this.onAcceptMessage,
    required this.onDeleteMessage,
    required this.onBlockUser,
    required this.onTapMessage,
    required this.onLongTapMessage,
    super.key,
  });

  final void Function(BuildContext context, data.Channel channel)
      onAcceptMessage;
  final void Function(BuildContext context, data.Channel channel)
      onDeleteMessage;
  final void Function(BuildContext context, data.Channel channel) onBlockUser;
  final void Function(data.Channel channel, bool isBlocked) onTapMessage;
  final void Function(data.Channel channel, bool isBlocked) onLongTapMessage;

  @override
  State<MessageRequestListView> createState() => _MessageRequestListViewState();
}

class _MessageRequestListViewState extends State<MessageRequestListView> {
  final PagingController<String, data.Channel> _pagingController =
      PagingController(firstPageKey: '');

  final ValueNotifier<bool> _noMoreItemsNotifier = ValueNotifier(true);

  ValueNotifier<String> _selectedItemNotifier = ValueNotifier('');
  List<UserPrivateData> _listUserPrivateData = [];
  ValueNotifier<bool> _isBlockUser = ValueNotifier(true);
  Map<String, chat.ChatUser> _mapBlockUser = {};
  late StreamSubscription? _listenBlockEventSubscription;

  late bool _is24HourFormat;
  late MessageRequestBloc _messageRequestBloc;

  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;
  String _nextPageToken = '';

  @override
  void initState() {
    super.initState();

    // init bloc
    _messageRequestBloc = getIt<MessageRequestBloc>();

    _is24HourFormat = getIt<AppBloc>().state.is24HourFormat;

    // init paging
    if (_noMoreItemsNotifier.value) {
      _pagingController.appendLastPage([]);
    } else {
      _pagingController.addPageRequestListener(
        (pageKey) {
          if (!_noMoreItemsNotifier.value) {
            _messageRequestBloc.add(
              LoadMoreMessageRequestEvent(
                nextPageToken: _nextPageToken,
              ),
            );
          }
        },
      );
    }

    _messageRequestBloc.add(InitMessageRequestEvent());

    // Setup listen event
    setupListenEventHandler();
  }

  @override
  void dispose() {
    _pagingController.dispose();
    _listenBlockEventSubscription?.cancel();
    super.dispose();
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        final listTemp =
            List<data.Channel>.of(_pagingController.itemList ?? []);
        listTemp.forEach((item) {
          try {
            item.name = getAliasName(item.recipientId) ?? item.name;
          } catch (error) {}
        });
        _pagingController.itemList = listTemp;
      },
    );
  }

  String? getAliasName(String? userId) {
    if (StringUtils.isNullOrEmpty(userId)) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);

      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      blockUser: (userId) {},
      unBlockUser: (userId) {},
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};
        listBlockUser?.forEach((item) {
          _mapBlockUser.putIfAbsent(
            item.userId,
            () => chat.ChatUser.fromJson(item.toJson()),
          );
        });
        setState(() {});
      },
      orElse: () {},
    );
  }

  void _appBlocListener(
    BuildContext context,
    AppState state,
  ) {
    if (_is24HourFormat != state.is24HourFormat) {
      setState(() {
        _is24HourFormat = state.is24HourFormat;
      });
    }
  }

  void setupListenEventHandler() {
    _listenBlockEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ListenBlockUserEvent>()
        .listen(onReceivedListenEvent);
  }

  final meId = chat.Config.getInstance().activeSessionKey;

  void onReceivedListenEvent(event) async {
    if (event is BlockEvent) {
      var user = chat.ChatUser.fromJson(event.user);
      if (event.actorUserId == meId) {
        _mapBlockUser.putIfAbsent(user.userId, () => user);
      }
    }
    if (event is UnBlockEvent) {
      if (event.actorUserId == meId) {
        _mapBlockUser.remove(event.userId);
      }
    }
  }

  void _handleMessageRequestDataLoaded(
    List<data.Channel> channels,
    bool noMoreItems,
  ) {
    _noMoreItemsNotifier.value = noMoreItems;
    if (noMoreItems) {
      _pagingController.appendLastPage([]);
    }

    if (channels.isEmpty && (_pagingController.itemList?.isEmpty ?? true)) {
      _pagingController.itemList = [];
      return;
    }

    if (channels.isNotEmpty) {
      _nextPageToken = channels.last.channelId;
    }

    _pagingController.itemList = channels;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<MessageRequestBloc, MessageRequestState>(
          listenWhen: (prev, current) =>
              prev.channels != current.channels ||
              prev.noMoreItems != current.noMoreItems,
          listener: (context, state) {
            _handleMessageRequestDataLoaded(state.channels, state.noMoreItems);
          },
        ),
        BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocUserPrivateListener,
        ),
        BlocListener<BlockUserBloc, BlockUserState>(
          listener: _blocBlockUserListener,
        ),
        BlocListener<AppBloc, AppState>(
          listenWhen: (prev, state) => prev != state,
          listener: _appBlocListener,
        ),
      ],
      child: BlocBuilder<MessageRequestBloc, MessageRequestState>(
        buildWhen: (prev, current) =>
            current.translatedMessages != prev.translatedMessages,
        builder: (context, state) {
          return _buildPagedListView(state);
        },
      ),
    );
  }

  Widget _buildPagedListView(MessageRequestState state) {
    return PagedListView<String, data.Channel>(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      pagingController: _pagingController,
      builderDelegate: PagedChildBuilderDelegate<data.Channel>(
        itemBuilder: (context, channel, index) {
          return ValueListenableBuilder(
            valueListenable: _selectedItemNotifier,
            builder: (context, selectItem, _) {
              final user = channel.recipient.target;

              final avatarUrl =
                  UrlUtils.parseAvatar(user?.profile.target?.avatar);
              final badgeEnum = user?.profile.target?.userBadgeType ?? 0;
              final userBadgeType =
                  UserBadgeEnumExtension.getEnumByValue(badgeEnum)
                      .toUserBadgeType();

              final _isBlockUserMap = <String, ValueNotifier<bool>>{};
              final userId = user?.userId ?? '';
              if (!_isBlockUserMap.containsKey(userId)) {
                _isBlockUserMap[userId] =
                    ValueNotifier(_mapBlockUser[userId] != null);
              } else {
                final newBlockValue = _mapBlockUser[userId] != null;
                if (_isBlockUserMap[userId]!.value != newBlockValue) {
                  _isBlockUserMap[userId]!.value = newBlockValue;
                }
              }

              final lastMessageTime = channel.metadata.target?.createTime;

              final strLastMessageTime =
                  _formatLastMessageTime(lastMessageTime, context);

              final lastMessageContent = _lastMessageWithUsername(
                channel.metadata.target,
                state,
              );

              return ui.ItemMessageRequestWidget(
                interface: ItemMessageRequestImplementation(
                  onAccept: () => widget.onAcceptMessage(context, channel),
                  onDelete: () => widget.onDeleteMessage(context, channel),
                  onBlockUser: () => widget.onBlockUser(context, channel),
                  onTap: () => widget.onTapMessage(
                    channel,
                    user!.blocked,
                  ),
                ),
                itemMessageRequest: ui.ItemMessageRequest(
                  badgeType: userBadgeType,
                  channelName: channel.getResolvedChannelName(),
                  lastMessageContent: lastMessageContent,
                  avatarUrl: avatarUrl,
                  countNew: channel.metadata.target?.unreadCount ?? 0,
                  lastMessageCreateTime: strLastMessageTime,
                  workspaceId: channel.workspaceId,
                  channelId: channel.channelId,
                  userId: channel.userId,
                  isOnline: true,
                  mentions: channel.metadata.target?.lastMessageMentions ?? [],
                ),
                onItemMessageRequestLongPressed: () {
                  widget.onLongTapMessage(
                    channel,
                    user!.blocked,
                  );
                },
                selectedItem: selectItem,
                updateSelectedItem: (String value) {
                  _selectedItemNotifier.value = value;
                },
                icUserBlocked: _isBlockUserMap[userId]!,
              );
            },
          );
        },
        noItemsFoundIndicatorBuilder: (_) {
          return ValueListenableBuilder(
            valueListenable: _noMoreItemsNotifier,
            builder: (context, noMoreItems, _) {
              if (noMoreItems) {
                return const ui.MessageRequestEmptyWidget();
              }
              return Container();
            },
          );
        },
        newPageProgressIndicatorBuilder: (_) => SizedBox(),
        firstPageProgressIndicatorBuilder: (_) => Container(),
      ),
    );
  }

  /// Formats the last message time based on localization and 24-hour format settings.
  /// If the message is from the same day, it returns the time string.
  /// Otherwise, it returns a shortened time representation in the user's locale.
  String _formatLastMessageTime(
    DateTime? lastMessageTime,
    BuildContext context,
  ) {
    if (lastMessageTime == null) return '';
    final isSameDay = lastMessageTime.isSameDay();
    if (!isSameDay) {
      final localeTag = Localizations.localeOf(context).toLanguageTag();
      return lastMessageTime.toShortTimeLocale(localeTag);
    } else {
      return lastMessageTime.toStringTime(use24Hour: _is24HourFormat);
    }
  }

  /// Retrieves the last message content with the sender's username if applicable.
  /// - If the channel is a DM or the message view type is 2 or lower (system message), it returns the message content.
  /// - Otherwise, it attempts to fetch the sender's username and prefixes it to the message.
  /// - If the username is unavailable, it returns only the message content.
  String _lastMessageWithUsername(
    data.ChannelMetadata? metadata,
    MessageRequestState state,
  ) {
    final translatedMessage = state.translatedMessages[metadata?.lastMessageId];

    final arguments = metadata?.lastMessageContentArguments ?? [];
    final messageContent = metadata?.lastMessageContent ?? '';
    final lastMessageContentOriginal =
        chat.TranslateContentUtils.translateContent(messageContent, arguments);

    return translatedMessage ?? lastMessageContentOriginal;
  }
}

class ItemMessageRequestImplementation extends ui.ItemMessageRequestInterface {
  ItemMessageRequestImplementation({
    required this.onAccept,
    required this.onDelete,
    required this.onBlockUser,
    required this.onTap,
  });

  final VoidCallback onAccept;
  final VoidCallback onDelete;
  final VoidCallback onBlockUser;
  final VoidCallback onTap;

  @override
  void onAcceptClicked(ui.ItemMessageRequest itemMessageRequest) {
    onAccept();
  }

  @override
  void onDeleteClicked(ui.ItemMessageRequest itemMessageRequest) {
    onDelete();
  }

  @override
  void onItemMessageRequestClicked(ui.ItemMessageRequest itemMessageRequest) {
    onTap();
  }

  @override
  void onBlockUserClicked(ui.ItemMessageRequest itemMessageRequest) {
    onBlockUser();
  }

  @override
  void onUnBlockUserClicked(ui.ItemMessageRequest itemMessageRequest) {
    onBlockUser();
  }
}
