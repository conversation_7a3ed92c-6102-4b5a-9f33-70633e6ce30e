import 'package:injectable/injectable.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../domain/repositories/session_repository.dart';
import '../database/generated/objectbox.g.dart';

/// Implementation of SessionRepository using ObjectBox
/// Provides concrete implementation for session data access
@LazySingleton(as: SessionRepository)
class SessionRepositoryImpl implements SessionRepository {
  final Store _store;
  final AppEventBus _eventBus;
  final Map<String, SessionLocalMetadata?> _cachedMetadataMap = {};

  SessionRepositoryImpl(
    this._store,
    this._eventBus,
  ) {
    _eventBus.on<ResumeIdUpdated>().listen((event) {
      updateResumeId(event.id);
    });
    _eventBus.on<OnWebsocketConnected>().listen((event) {
      _handleWebsocketConnected(event.data['resumeId']);
    });
  }

  /// Get ObjectBox session box
  Box<Session> get _sessionBox => _store.box<Session>();

  /// Get ObjectBox session metadata box
  Box<SessionLocalMetadata> get _metadataBox =>
      _store.box<SessionLocalMetadata>();

  /// Get ObjectBox user box
  Box<User> get _userBox => _store.box<User>();

  String? get _activeSessionKey => Config.getInstance().activeSessionKey;

  void _handleWebsocketConnected(String eventId) {
    var metadata =
        _cachedMetadataMap[_activeSessionKey] ?? getSessionMetadata();
    if (metadata == null) return;

    if (StringUtils.isNullOrEmpty(metadata.resumeId)) {
      final newLocalMetadata = metadata.copyWith(resumeId: eventId);
      _metadataBox.put(newLocalMetadata);
      metadata = newLocalMetadata;
    }

    _cachedMetadataMap[_activeSessionKey!] = metadata;

    AppEventBus.publish(ResumeWebsocket(data: metadata.resumeId));
  }

  @override
  int insert(Session session) {
    // Check if session already exists
    final query = _sessionBox
        .query(Session_.sessionKey.equals(session.sessionKey))
        .build();
    final existingSession = query.findFirst();
    query.close();

    if (existingSession != null) {
      // Update existing session, preserve ObjectBox ID
      session.id = existingSession.id;
    }
    if (session.user.target != null &&
        session.user.target?.userId.isNotEmpty == true) {
      final existingUser = _getExistingUser(
        userId: session.user.target!.userId,
      );
      if (existingUser != null) {
        session.user.target!.id = existingUser.id;
      }
    }

    // If the new session is being set as active, deactivate all other sessions
    if (session.active) {
      _deactivateOtherSessions(session.sessionKey);
    }

    // Handle SessionLocalMetadata insertion/management
    _ensureSessionLocalMetadata(session);

    final result = _sessionBox.put(session);

    return result;
  }

  @override
  int insertMetadata(SessionLocalMetadata metadata) {
    final query = _metadataBox
        .query(SessionLocalMetadata_.sessionKey.equals(metadata.sessionKey))
        .build();
    final existingMetadata = query.findFirst();
    query.close();

    if (existingMetadata != null) {
      metadata.id = existingMetadata.id;
    }

    _cachedMetadataMap[_activeSessionKey!] = metadata;

    return _metadataBox.put(metadata);
  }

  @override
  Stream<List<Session>> getSessionsStream() {
    return _sessionBox
        .query()
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  List<Session> getSessions() {
    return _sessionBox.getAll();
  }

  @override
  List<Session> getSessionsSkipQRLogin() {
    final query = _sessionBox.query(Session_.isLoginQR.equals(false)).build();
    final sessions = query.find();
    query.close();
    return sessions;
  }

  @override
  Stream<List<Session>> getSessionsSkipQRLoginStream() {
    return _sessionBox
        .query(Session_.isLoginQR.equals(false))
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  int countSessionsSkipQRLogin() {
    final query = _sessionBox.query(Session_.isLoginQR.equals(false)).build();
    final count = query.count();
    query.close();
    return count;
  }

  @override
  Session? getSession(String sessionKey) {
    final query =
        _sessionBox.query(Session_.sessionKey.equals(sessionKey)).build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  List<Session> getSessionsBySessionKey(String sessionKey) {
    final query =
        _sessionBox.query(Session_.sessionKey.equals(sessionKey)).build();
    final sessions = query.find();
    query.close();
    return sessions;
  }

  @override
  SessionLocalMetadata? getSessionMetadata() {
    final activeSessionKey = getActiveSession()?.sessionKey;
    if (activeSessionKey == null || activeSessionKey.isEmpty) {
      return null;
    }

    final query = _metadataBox
        .query(SessionLocalMetadata_.sessionKey.equals(activeSessionKey))
        .build();
    final metadata = query.findFirst();
    query.close();
    return metadata;
  }

  @override
  void updateResumeId(String resumeId) {
    var metadata =
        _cachedMetadataMap[_activeSessionKey] ?? getSessionMetadata();
    if (metadata == null) return;

    metadata.resumeId = resumeId;
    _cachedMetadataMap[_activeSessionKey!] = metadata;
    _metadataBox.put(metadata);
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _sessionBox.query(Session_.sessionKey.equals(sessionKey)).build();
    query.remove();
    query.close();
    final deleteMetaDataQuery = _metadataBox
        .query(SessionLocalMetadata_.sessionKey.equals(sessionKey))
        .build();
    deleteMetaDataQuery.remove();
    deleteMetaDataQuery.close();

    _cachedMetadataMap[sessionKey] = null;
    return true;
  }

  /// Ensure SessionLocalMetadata exists for the given session
  /// Creates new metadata with default values if it doesn't exist
  /// Preserves existing metadata if it already exists
  /// Establishes proper relationship between Session and SessionLocalMetadata
  void _ensureSessionLocalMetadata(Session session) {
    // Check if SessionLocalMetadata already exists for this session
    final metadataQuery = _metadataBox
        .query(SessionLocalMetadata_.sessionKey.equals(session.sessionKey))
        .build();
    final existingMetadata = metadataQuery.findFirst();
    metadataQuery.close();

    if (existingMetadata == null) {
      // Create new SessionLocalMetadata with default values
      final newMetadata = SessionLocalMetadata.create(
        sessionKey: session.sessionKey,
        // All other fields will use their default values from the constructor
      );

      // Establish relationship between Session and SessionLocalMetadata
      newMetadata.session.target = session;

      // Insert the new metadata
      _metadataBox.put(newMetadata);
    } else {
      // SessionLocalMetadata already exists, ensure relationship is properly set
      if (existingMetadata.session.target == null) {
        existingMetadata.session.target = session;
        _metadataBox.put(existingMetadata);
      }
      // If metadata exists and relationship is already set, do nothing
      // This preserves all existing field values
    }
  }

  /// Deactivate all sessions except the specified one
  /// This ensures only one session can be active at a time
  void _deactivateOtherSessions(String keepActiveSessionKey) {
    // Find all active sessions except the one we want to keep active
    final query = _sessionBox
        .query(
          Session_.active.equals(true) &
              Session_.sessionKey.notEquals(keepActiveSessionKey),
        )
        .build();
    final sessionsToDeactivate = query.find();
    query.close();

    if (sessionsToDeactivate.isNotEmpty) {
      // Deactivate each session found
      for (final session in sessionsToDeactivate) {
        // This sets active = false, isLogin = false, and logoutTime
        session.logout();
      }
      _sessionBox.putMany(sessionsToDeactivate);
    }
  }

  @override
  int setSessionInactive(Session session) {
    session.logout(); // Use the logout method from Session entity
    return _sessionBox.put(session);
  }

  @override
  int deleteAllSessions() {
    // Also delete all metadata
    _metadataBox.removeAll();
    _cachedMetadataMap.clear();
    return _sessionBox.removeAll();
  }

  @override
  Session? getActiveSession() {
    final query = _sessionBox.query(Session_.active.equals(true)).build();
    final session = query.findFirst();
    query.close();
    return session;
  }

  @override
  Future<Session?> getActiveSessionAsync() async {
    final query = _sessionBox.query(Session_.active.equals(true)).build();
    final session = await query.findFirstAsync();
    query.close();
    return session;
  }

  @override
  Future<void> setPasskeyMigrated() async {
    final session = await getActiveSessionAsync();
    if (session == null) return;

    session.passkeyMigrated = true;
    await _sessionBox.putAsync(session);
  }

  @override
  Future<void> updateUserUpdateTimeAfter(String updateTimeAfter) async {
    final metadata = getSessionMetadata();
    if (metadata == null) return;

    // Parse string to DateTime
    final dateTime = TimeUtils.parseUTCStringToDateTime(updateTimeAfter);
    final updatedMetadata = metadata.copyWith(userUpdateTimeAfter: dateTime);
    _metadataBox.put(updatedMetadata);
  }

  @override
  bool updateIntroChannelView(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.introChannelView = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateIntroTranslateEntireChat(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.introTranslateEntireChat = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateIntroChannelList(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.introChannelList = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateIntroCallLog(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.introCallLog = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateIntroCreateChannel(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.introCreateChannel = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateSpeechLang(String value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.speechLang = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateSpeechToTextEnable(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.speechToTextEnable = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateResumeIdGranular(String resumeId) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.resumeId = resumeId;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateUserUpdateTimeAfterGranular(DateTime? userUpdateTimeAfter) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.userUpdateTimeAfter = userUpdateTimeAfter;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateLoadedAllChannels(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.loadedAllChannels = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateLoadedAllFriends(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.loadedAllFriends = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateLoadedAllFriendRequests(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.loadedAllFriendRequests = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateLoadedAllMessageRequests(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.loadedAllMessageRequests = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateClosedMessageRequestWarning(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.closedMessageRequestWarning = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateClosedListBlockUserWarning(bool value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.closedListBlockUserWarning = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool updateUserStatusEmojis(List<String> value) {
    try {
      final metadata = getSessionMetadata();
      if (metadata == null) return false;

      metadata.userStatusEmojis = value;
      _cachedMetadataMap[_activeSessionKey!] = metadata;
      _metadataBox.put(metadata);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  bool? getIntroChannelView() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.introChannelView;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getIntroTranslateEntireChat() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.introTranslateEntireChat;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getIntroChannelList() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.introChannelList;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getIntroCallLog() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.introCallLog;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getIntroCreateChannel() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.introCreateChannel;
    } catch (e) {
      return null;
    }
  }

  @override
  String? getSpeechLang() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.speechLang;
    } catch (e) {
      return null;
    }
  }

  @override
  String? getResumeId() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.resumeId;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getSpeechToTextEnable() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.speechToTextEnable;
    } catch (e) {
      return null;
    }
  }

  @override
  DateTime? getUserUpdateTimeAfter() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.userUpdateTimeAfter;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getClosedListBlockUserWarning() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.closedListBlockUserWarning;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getClosedMessageRequestWarning() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.closedMessageRequestWarning;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getLoadedAllChannels() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.loadedAllChannels;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getLoadedAllFriendRequests() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.loadedAllFriendRequests;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getLoadedAllFriends() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.loadedAllFriends;
    } catch (e) {
      return null;
    }
  }

  @override
  bool? getLoadedAllMessageRequests() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.loadedAllMessageRequests;
    } catch (e) {
      return null;
    }
  }

  @override
  List<String> getUserStatusEmojis() {
    try {
      final metadata = getSessionMetadata();
      return metadata?.userStatusEmojis ?? [];
    } catch (e) {
      throw Exception('Error when get user status emoji');
    }
  }

  @override
  Session createSession({
    required String sessionKey,
    required String sessionId,
    required String sessionToken,
    bool active = true,
    bool isLogin = true,
    bool isLoginQR = false,
    DateTime? loginTime,
  }) {
    final session = Session.create(
      sessionKey: sessionKey,
      sessionId: sessionId,
      sessionToken: sessionToken,
      loginTime: loginTime,
    );

    session.active = active;
    session.isLogin = isLogin;
    session.isLoginQR = isLoginQR;

    return session;
  }

  @override
  Future<void> activateSession(String sessionKey) async {
    // Get the session to activate
    final session = getSession(sessionKey);
    if (session == null) return;

    // Deactivate all other sessions first
    _deactivateOtherSessions(sessionKey);

    // Then activate the specified session
    session.login();
    _sessionBox.put(session);
  }

  @override
  Future<void> deactivateAllSessions() async {
    final sessions = getSessions();
    for (final session in sessions) {
      if (session.active) {
        session.logout();
      }
    }
    _sessionBox.putMany(sessions);
  }

  @override
  bool sessionExists(String sessionKey) {
    return getSession(sessionKey) != null;
  }

  @override
  int getSessionCount() {
    return _sessionBox.count();
  }

  @override
  int getActiveSessionCount() {
    final query = _sessionBox.query(Session_.active.equals(true)).build();
    final count = query.count();
    query.close();
    return count;
  }

  @override
  Stream<Session?> getActiveSessionStream() {
    // Create a query for active sessions and watch for changes
    return _sessionBox
        .query(Session_.active.equals(true))
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) {
      return query.findFirst();
    });
  }

  @override
  Session createSessionWithUser({
    required String sessionKey,
    required String sessionId,
    required String sessionToken,
    required User user,
    bool active = true,
    bool isLogin = true,
    bool isLoginQR = false,
    DateTime? loginTime,
  }) {
    final session = Session.createWithUser(
      sessionKey: sessionKey,
      sessionId: sessionId,
      sessionToken: sessionToken,
      sessionUser: user,
      loginTime: loginTime,
    );

    session.active = active;
    session.isLogin = isLogin;
    session.isLoginQR = isLoginQR;

    return session;
  }

  @override
  bool associateUserWithSession(String sessionKey, User user) {
    final session = getSession(sessionKey);
    if (session == null) return false;

    // Save both entities
    _sessionBox.put(session);
    _userBox.put(user);

    return true;
  }

  @override
  Map<String, User?> getSessionsWithUsers() {
    final sessions = getSessions();
    final result = <String, User?>{};

    for (final session in sessions) {
      result[session.sessionKey] = session.associatedUser;
    }

    return result;
  }

  @override
  bool syncSessionWithUser(String sessionKey) {
    final session = getSession(sessionKey);
    if (session == null) return false;

    session.syncWithUser();
    _sessionBox.put(session);

    // Also save the user if it exists
    final user = session.associatedUser;
    if (user != null) {
      _userBox.put(user);
    }

    return true;
  }

  /// Check if user exists for a specific session
  User? _getExistingUser({
    required String userId,
  }) {
    try {
      if (userId.isEmpty) return null;
      final userQuery = _userBox
          .query(
            User_.userId.equals(userId),
          )
          .build();
      final userExists = userQuery.findFirst();
      userQuery.close();

      return userExists;
    } catch (e) {
      Log.e('Error checking if user $userId exists: $e');
      return null;
    }
  }
}
