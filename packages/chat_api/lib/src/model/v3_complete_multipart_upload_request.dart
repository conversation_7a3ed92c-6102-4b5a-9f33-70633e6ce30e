//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_audio_metadata.dart';
import 'package:chat_api/src/model/v3_completed_part.dart';
import 'package:chat_api/src/model/v3_file_metadata.dart';
import 'package:chat_api/src/model/v3_upload_metadata.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_complete_multipart_upload_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3CompleteMultipartUploadRequest {
  /// Returns a new [V3CompleteMultipartUploadRequest] instance.
  V3CompleteMultipartUploadRequest({
    this.uploadId,
    this.completedParts,
    this.uploadMetadata,
    this.fileMetadata,
    this.audioMetadata,
  });

  /// The identify of the multipart upload to complete.
  @JsonKey(
    name: r'uploadId',
    required: false,
    includeIfNull: false,
  )
  final String? uploadId;

  /// The individual parts that have been uploaded and need to be combined to form the completed object.
  @JsonKey(
    name: r'completedParts',
    required: false,
    includeIfNull: false,
  )
  final List<V3CompletedPart>? completedParts;

  @JsonKey(
    name: r'uploadMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3UploadMetadata? uploadMetadata;

  @JsonKey(
    name: r'fileMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3FileMetadata? fileMetadata;

  @JsonKey(
    name: r'audioMetadata',
    required: false,
    includeIfNull: false,
  )
  final V3AudioMetadata? audioMetadata;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3CompleteMultipartUploadRequest &&
          other.uploadId == uploadId &&
          other.completedParts == completedParts &&
          other.uploadMetadata == uploadMetadata &&
          other.fileMetadata == fileMetadata &&
          other.audioMetadata == audioMetadata;

  @override
  int get hashCode =>
      uploadId.hashCode +
      completedParts.hashCode +
      uploadMetadata.hashCode +
      fileMetadata.hashCode +
      audioMetadata.hashCode;

  factory V3CompleteMultipartUploadRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3CompleteMultipartUploadRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3CompleteMultipartUploadRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
