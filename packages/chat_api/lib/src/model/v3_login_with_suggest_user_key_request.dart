//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_assertion_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_login_with_suggest_user_key_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3LoginWithSuggestUserKeyRequest {
  /// Returns a new [V3LoginWithSuggestUserKeyRequest] instance.
  V3LoginWithSuggestUserKeyRequest({
    this.reqId,
    this.reqVerifier,
    this.assertion,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  /// The randomly generated value created by the client.
  @<PERSON>son<PERSON>ey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @JsonKey(
    name: r'assertion',
    required: false,
    includeIfNull: false,
  )
  final CommonAssertionResult? assertion;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3LoginWithSuggestUserKeyRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.assertion == assertion;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + assertion.hashCode;

  factory V3LoginWithSuggestUserKeyRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3LoginWithSuggestUserKeyRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3LoginWithSuggestUserKeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
