import 'package:data_router/data_router.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@Injectable()
class SyncPrivateDataUseCase
    extends BaseFutureUseCase<SyncPrivateDataInput, SyncPrivateDataOutput> {
  SyncPrivateDataUseCase(this._privateDataOperation);

  final PrivateDataDataOperation _privateDataOperation;

  @override
  Future<SyncPrivateDataOutput> buildUseCase(
    SyncPrivateDataInput input,
  ) async {
    await _privateDataOperation.sync();
    return SyncPrivateDataOutput(ok: true);
  }
}

class SyncPrivateDataInput extends BaseInput {
  SyncPrivateDataInput();
}

class SyncPrivateDataOutput extends BaseOutput {
  SyncPrivateDataOutput({this.ok});

  final bool? ok;
}
