//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_initiate_multipart_upload_response_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_multipart_upload_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateMultipartUploadResponse {
  /// Returns a new [V3InitiateMultipartUploadResponse] instance.
  V3InitiateMultipartUploadResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3InitiateMultipartUploadResponseData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateMultipartUploadResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3InitiateMultipartUploadResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateMultipartUploadResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateMultipartUploadResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
