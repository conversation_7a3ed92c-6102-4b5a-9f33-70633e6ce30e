import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../domain/services/session_service.dart';

/// Implementation of SessionService using ChatAPI
/// Provides concrete implementation for session remote operations
///
/// This follows the same pattern as SessionRepositoryImpl for local operations,
/// but handles API calls instead of database operations.
///
/// Note: Session APIs are not yet implemented in the ChatAPI,
/// so this provides placeholder functionality for future implementation.
@LazySingleton(as: SessionService)
class SessionServiceImpl implements SessionService {
  SessionServiceImpl(this._apiClient);

  final ApiClient _apiClient;

  AuthServiceApi get _authServiceApi => _apiClient.api.getAuthServiceApi();

  HashcashServiceApi get _hashCashServiceApi =>
      _apiClient.api.getHashcashServiceApi();

  @override
  Future<Session?> getSession(String sessionKey) async {
    try {
      // Session API is not implemented yet in the ChatAPI
      // Return null for now, similar to current implementation
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Session>> getSessions() async {
    try {
      // Sessions list API is not implemented yet
      // Return empty list for now
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<Session>> getActiveSessions() async {
    try {
      // Active sessions API is not implemented yet
      // Return empty list for now
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<Session?> createSession({
    required String sessionKey,
    required String sessionId,
    required String sessionToken,
    bool active = true,
    bool isLogin = true,
    bool isLoginQR = false,
    DateTime? loginTime,
  }) async {
    try {
      // Session creation API is not implemented yet
      // Return null for now
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> activateSession(String sessionKey) async {
    try {
      // Session activation API is not implemented yet
      // Return false for now
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deactivateSession(String sessionKey) async {
    try {
      // Session deactivation API is not implemented yet
      // Return false for now
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> deleteSession(String sessionKey) async {
    try {
      // Session deletion API is not implemented yet
      // Return false for now
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> sessionExists(String sessionKey) async {
    try {
      final session = await getSession(sessionKey);
      return session != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<int> getSessionCount() async {
    try {
      // Session count API is not implemented yet
      // Return -1 to indicate not supported
      return -1;
    } catch (e) {
      return -1;
    }
  }

  @override
  Future<List<Session>> syncSessions({
    String? updateTimeAfter,
    int? limit,
  }) async {
    try {
      // Session sync API is not implemented yet
      // Return empty list for now
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<GetPowChallengeResponse> getPowChallenge(
    GetPowChallengeRequest request,
  ) async {
    final response = await _hashCashServiceApi.getPowChallenge();
    return GetPowChallengeResponse(
      level: response.data?.data?.level,
      challenge: response.data?.data?.challenge,
    );
  }

  @override
  Future<InitiateUserKeyAuthFlowResponse> initiateUserKeyAuthFlow(
    InitiateUserKeyAuthFlowRequest request,
  ) async {
    final body = V3InitiateUserKeyAuthFlowRequest(
      userKey: request.userKey,
      deviceId: request.deviceId,
      reqChallenge: request.reqChallenge,
    );
    final response = await _authServiceApi.initiateUserKeyAuthFlow(
      body: body,
      headers: request.headers,
    );
    return InitiateUserKeyAuthFlowResponse(
      loginRequest: response.data?.loginRequest != null
          ? createLoginRequestUserKey(response.data?.loginRequest)
          : null,
      registerRequest: response.data?.registerRequest?.toJson(),
    );
  }

  @override
  Future<RegisterResponse> registerWithUserKey(
    RegisterWithUserKeyRequest request,
  ) async {
    final response = await _authServiceApi.registerWithUserKey(
      body: request,
      headers: request.headers,
    );

    return RegisterResponse(
      userId: response.data?.data?.userId ?? '',
      sessionId: response.data?.data?.sessionId ?? '',
      sessionToken: response.data?.data?.sessionToken ?? '',
      user: response.data?.data?.user != null
          ? UserSerializer.fromV3User(
              response.data!.data!.user!,
              initSessionKey: response.data!.data!.userId!,
            )
          : null,
    );
  }

  @override
  Future<LoginWithUserKeyResponse> loginWithUserKey(
    LoginWithUserKeyRequest request,
  ) async {
    final response = await _authServiceApi.loginWithUserKey(
      body: request,
    );

    return LoginWithUserKeyResponse(
      userId: response.data?.data?.userId ?? '',
      sessionId: response.data?.data?.sessionId ?? '',
      sessionToken: response.data?.data?.sessionToken ?? '',
      user: response.data?.data?.user != null
          ? UserSerializer.fromV3User(
              response.data!.data!.user!,
              initSessionKey: response.data!.data!.userId!,
            )
          : null,
    );
  }

  @override
  Future<bool> cancelRegistration(CancelRegistrationRequest request) async {
    final response = await _authServiceApi.cancelRegistration(
      body: V3CancelRegistrationRequest(
        reqId: request.registrationId,
        userKey: request.userKey,
      ),
    );

    return response.data?.ok ?? false;
  }

  @override
  Future<InitiateSuggestUserKeyAuthFlowResponse> initiateSuggestUserKeyAuthFlow(
    InitiateSuggestUserKeyAuthFlowRequest request,
  ) async {
    final body = V3InitiateSuggestUserKeyAuthFlowRequest(
      deviceId: request.deviceId,
      reqChallenge: request.reqChallenge,
    );
    final response = await _authServiceApi.initiateSuggestUserKeyAuthFlow(
      body: body,
      headers: request.headers,
    );
    return InitiateSuggestUserKeyAuthFlowResponse(
      loginRequest: response.data?.loginRequest?.toJson(),
    );
  }

  @override
  Future<LoginWithSuggestUserKeyResponse> loginWithSuggestUserKey(
    LoginWithSuggestUserKeyRequest request,
  ) async {
    final response = await _authServiceApi.loginWithSuggestUserKey(
      body: request,
    );

    return LoginWithSuggestUserKeyResponse(
      userId: response.data?.data?.userId ?? '',
      sessionId: response.data?.data?.sessionId ?? '',
      sessionToken: response.data?.data?.sessionToken ?? '',
      username: response.data?.data?.user?.username ?? '',
      user: response.data?.data?.user != null
          ? UserSerializer.fromV3User(
              response.data!.data!.user!,
              initSessionKey: response.data!.data!.userId!,
            )
          : null,
    );
  }

  @override
  Future<InitiateQRAuthFlowResponse> initiateQRAuthFlow(
    InitiateQRAuthFlowRequest request,
  ) async {
    final response = await _authServiceApi.initiateQRAuthFlow(
      body: V3InitiateQRAuthFlowRequest(reqId: request.requestId),
      headers: request.headers,
    );

    return InitiateQRAuthFlowResponse(
      reqId: response.data?.data?.reqId ?? '',
      qrAuthCode:
          response.data?.data?.qrAuthCodeRequestOptions?.qrAuthCode ?? '',
      timeout: response.data?.data?.qrAuthCodeRequestOptions?.timeout ?? 0,
    );
  }

  @override
  Future<GetQRAuthStateResponse> getQRAuthState(
    GetQRAuthStateRequest request,
  ) async {
    final response = await _authServiceApi.getQRAuthState(
      reqId: request.requestId,
    );

    return GetQRAuthStateResponse(state: response.data?.data?.value ?? '');
  }

  @override
  Future<LoginWithQRAuthResponse> loginWithQRCode(
    LoginWithQRAuthRequest request,
  ) async {
    final response = await _authServiceApi.loginWithQRAuthCode(
      body: request,
    );

    return LoginWithQRAuthResponse(
      userId: response.data?.data?.userId ?? '',
      sessionId: response.data?.data?.sessionId ?? '',
      sessionToken: response.data?.data?.sessionToken ?? '',
      user: response.data?.data?.user != null
          ? UserSerializer.fromV3User(
              response.data!.data!.user!,
              initSessionKey: response.data!.data!.userId!,
            )
          : null,
    );
  }

  @override
  Future<VerifyQRAuthResponse> verifyQRAuth(VerifyQRAuthRequest request) async {
    final response = await _authServiceApi.verifyQRAuth(
      body: request,
      headers: request.headers,
    );

    return VerifyQRAuthResponse(
      ok: response.data?.ok ?? false,
      data: response.data?.data != null
          ? createVerifyQRAuthData(response.data?.data)
          : null,
    );
  }

  @override
  Future<AcceptQRAuthResponse> acceptQRAuth(AcceptQRAuthRequest request) async {
    final response = await _authServiceApi.acceptQRAuth(
      body: request,
    );

    return AcceptQRAuthResponse(success: response.data?.ok ?? false);
  }

  @override
  Future<bool> logout() async {
    final response = await _authServiceApi.logout();
    return response.data?.ok ?? false;
  }

  @override
  Future<InitiateGenerateSecurityKeyResponse> initiateGenerateSecurityKeyFlow({
    required String reqChallengeHash,
  }) async {
    try {
      final body = V3InitiateGenerateSecurityKeyFlowRequest(
        reqChallenge: reqChallengeHash,
      );

      final response = await _apiClient.api
          .getAuthServiceApi()
          .initiateGenerateSecurityKeyFlow(body: body);

      if (response.data != null) {
        return InitiateGenerateSecurityKeyResponse(
          ok: response.data?.ok ?? false,
          data: response.data?.data != null
              ? CommonCredentialRequestOptionsData(
                  reqId: response.data?.data?.reqId ?? '',
                  credentialRequestOptions: PublicKeyCredentialRequestOptions(
                    challenge: response
                            .data?.data?.credentialRequestOptions?.challenge ??
                        '',
                    timeout: response
                            .data?.data?.credentialRequestOptions?.timeout ??
                        0,
                    rpId: response.data?.data?.credentialRequestOptions?.rpId ??
                        '',
                    attestation: response.data?.data?.credentialRequestOptions
                            ?.attestation ??
                        '',
                    userVerification: response.data?.data
                            ?.credentialRequestOptions?.userVerification ??
                        '',
                    allowCredentials: response.data?.data
                            ?.credentialRequestOptions?.allowCredentials
                            ?.map(
                              (e) => PublicKeyCredentialDescriptor(
                                id: e.id,
                                transports: e.transports,
                                type: e.type,
                              ),
                            )
                            .toList() ??
                        [],
                  ),
                )
              : null,
        );
      } else {
        throw Exception('Response data is null');
      }
    } catch (e) {
      throw Exception('Failed to initiate security key flow: $e');
    }
  }

  @override
  Future<InitiateViewSecurityKeyResponse> initiateViewSecurityKey({
    required String reqChallengeHash,
  }) async {
    try {
      final body = V3InitiateViewSecurityKeyRequest(
        reqChallenge: reqChallengeHash,
      );

      final response = await _apiClient.api
          .getAuthServiceApi()
          .initiateViewSecurityKey(body: body);

      if (response.data != null) {
        return InitiateViewSecurityKeyResponse(
          ok: response.data?.ok ?? false,
          data: response.data?.data != null
              ? CommonCredentialRequestOptionsData(
                  reqId: response.data?.data?.reqId ?? '',
                  credentialRequestOptions: PublicKeyCredentialRequestOptions(
                    challenge: response
                            .data?.data?.credentialRequestOptions?.challenge ??
                        '',
                    timeout: response
                            .data?.data?.credentialRequestOptions?.timeout ??
                        0,
                    rpId: response.data?.data?.credentialRequestOptions?.rpId ??
                        '',
                    attestation: response.data?.data?.credentialRequestOptions
                            ?.attestation ??
                        '',
                    userVerification: response.data?.data
                            ?.credentialRequestOptions?.userVerification ??
                        '',
                    allowCredentials: response.data?.data
                            ?.credentialRequestOptions?.allowCredentials
                            ?.map(
                              (e) => PublicKeyCredentialDescriptor(
                                id: e.id,
                                transports: e.transports,
                                type: e.type,
                              ),
                            )
                            .toList() ??
                        [],
                  ),
                )
              : null,
        );
      } else {
        throw Exception('Response data is null');
      }
    } catch (e) {
      throw Exception('Failed to initiate view security: $e');
    }
  }

  @override
  Future<bool> checkMigratePasskeyStatus() async {
    try {
      final response =
          await _apiClient.api.getAuthServiceApi().checkMigratePasskeyStatus();

      if (response.data != null) {
        return response.data!.data ?? false;
      }

      return false;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<InitiateAccountDeletionFlowResponse>
      initiateAccountDeletionFlowResponse({
    required String reqChallengeHash,
  }) async {
    try {
      final body = V3InitiateAccountDeletionFlowRequest(
        reqChallenge: reqChallengeHash,
      );

      final response = await _apiClient.api
          .getAuthServiceApi()
          .initiateAccountDeletionFlow(body: body);

      if (response.data != null) {
        return InitiateAccountDeletionFlowResponse(
          ok: response.data?.ok ?? false,
          confirmationPayload: response.data?.confirmationPayload != null
              ? DeletionConfirmationPayload(
                  reqId: response.data?.confirmationPayload?.reqId ?? '',
                  credentialRequestOptions: PublicKeyCredentialRequestOptions(
                    challenge: response.data?.confirmationPayload
                            ?.credentialRequestOptions?.challenge ??
                        '',
                    timeout: response.data?.confirmationPayload
                            ?.credentialRequestOptions?.timeout ??
                        0,
                    rpId: response.data?.confirmationPayload
                            ?.credentialRequestOptions?.rpId ??
                        '',
                    attestation: response.data?.confirmationPayload
                            ?.credentialRequestOptions?.attestation ??
                        '',
                    userVerification: response.data?.confirmationPayload
                            ?.credentialRequestOptions?.userVerification ??
                        '',
                    allowCredentials: response.data?.confirmationPayload
                            ?.credentialRequestOptions?.allowCredentials
                            ?.map(
                              (e) => PublicKeyCredentialDescriptor(
                                id: e.id,
                                transports: e.transports,
                                type: e.type,
                              ),
                            )
                            .toList() ??
                        [],
                  ),
                )
              : null,
        );
      } else {
        throw Exception('Response data is null');
      }
    } catch (e) {
      throw Exception('Failed to initiate view security: $e');
    }
  }

  @override
  Future<bool> confirmAccountDeletion(
    ConfirmAccountDeletionRequest body,
  ) async {
    try {
      final response = await _authServiceApi.confirmAccountDeletion(body: body);

      if (response.data?.ok == true) {
        return true;
      } else {
        return false;
      }
    } catch (e, stackTrace) {
      Log.e('Error confirming account deletion: $e\n$stackTrace');

      return false;
    }
  }

  @override
  Future<ConfirmViewSecurityKeyResponse> confirmViewSecurityKey(
    V3ConfirmViewSecurityKeyRequest body,
  ) async {
    try {
      final response = await _authServiceApi.confirmViewSecurityKey(body: body);

      if (response.data != null) {
        return ConfirmViewSecurityKeyResponse(
          ok: response.data?.ok,
          data: response.data?.data != null
              ? SecurityKey(securityKey: response.data?.data?.securityKey)
              : SecurityKey(),
        );
      } else {
        throw Exception('Response data is null');
      }
    } catch (e) {
      throw Exception('Failed to confirm view security: $e');
    }
  }

  @override
  Future<GenerateSecurityKeyResponse> generateSecurityKey(
    GenerateSecurityKeyRequestData body,
  ) async {
    try {
      final response = await _authServiceApi.generateSecurityKey(body: body);

      if (response.data != null) {
        return GenerateSecurityKeyResponse(
          ok: response.data?.ok,
          data: response.data?.data != null
              ? SecurityKey(
                  securityKey: response.data?.data?.securityKey,
                )
              : SecurityKey(),
        );
      } else {
        throw Exception('Response data is null');
      }
    } catch (e) {
      throw Exception('Failed to confirm view security: $e');
    }
  }

  @override
  Future<MigratePasskeyResponse> migratePasskey(
    MigratePasskeyRequest request,
  ) async {
    final body = V3MigratePasskeyRequest(
      reqChallenge: request.reqChallenge,
    );

    final response = await _authServiceApi.migratePasskey(
      body: body,
      headers: request.headers,
    );
    return MigratePasskeyResponse(
      ok: response.data?.ok ?? false,
      registerRequestUserKeyJson: response.data?.data?.toJson(),
    );
  }

  @override
  Future<VerifyMigratePasskeyResponse> verifyMigratePasskey(
    VerifyMigratePasskeyRequest request,
  ) async {
    final response = await _authServiceApi.verifyMigratePasskey(
      body: request,
    );
    return VerifyMigratePasskeyResponse(ok: response.data?.ok ?? false);
  }

  //========== Helper methods to convert between V3 and domain models ==========
  /// Helper method to create LoginRequestUserKey from V3LoginRequestUserKey
  LoginRequestUserKey? createLoginRequestUserKey(
    V3LoginRequestUserKey? v3LoginRequestUserKey,
  ) {
    if (v3LoginRequestUserKey == null) {
      return null;
    }

    final credentialRequestOptions =
        v3LoginRequestUserKey.credentialRequestOptions;

    return LoginRequestUserKey(
      reqId: v3LoginRequestUserKey.reqId,
      credentialRequestOptions: credentialRequestOptions != null
          ? PublicKeyCredentialRequestOptions(
              challenge: credentialRequestOptions.challenge,
              timeout: credentialRequestOptions.timeout,
              rpId: credentialRequestOptions.rpId,
              attestation: credentialRequestOptions.attestation,
              userVerification: credentialRequestOptions.userVerification,
              allowCredentials: credentialRequestOptions.allowCredentials
                  ?.map(
                    (e) => PublicKeyCredentialDescriptor(
                      id: e.id,
                      transports: e.transports,
                      type: e.type,
                    ),
                  )
                  .toList(),
            )
          : null,
      passkeyMigrated: v3LoginRequestUserKey.passkeyMigrated,
    );
  }

  /// Helper method to create LoginRequestUserKey from V3LoginRequestUserKey
  VerifyQRAuthData? createVerifyQRAuthData(V3VerifyQRAuthData? v3Data) {
    if (v3Data == null) {
      return null;
    }
    final credentialRequestOptions = v3Data.credentialRequestOptions;
    return VerifyQRAuthData(
      reqId: v3Data.reqId ?? '',
      credentialRequestOptions: credentialRequestOptions != null
          ? PublicKeyCredentialRequestOptions(
              challenge: credentialRequestOptions.challenge,
              timeout: credentialRequestOptions.timeout,
              rpId: credentialRequestOptions.rpId,
              attestation: credentialRequestOptions.attestation,
              userVerification: credentialRequestOptions.userVerification,
              allowCredentials: credentialRequestOptions.allowCredentials
                  ?.map(
                    (e) => PublicKeyCredentialDescriptor(
                      id: e.id,
                      transports: e.transports,
                      type: e.type,
                    ),
                  )
                  .toList(),
            )
          : null,
    );
  }

  /// Helper method to create RegisterRequestUserKey from V3RegisterRequestUserKey
  RegisterRequestUserKey? createRegisterRequestUserKey(
    V3RegisterRequestUserKey? v3RegisterRequestUserKey,
  ) {
    if (v3RegisterRequestUserKey == null) {
      return null;
    }

    final credentialCreationOptions =
        v3RegisterRequestUserKey.credentialCreationOptions;
    final relayParty = credentialCreationOptions?.rp;
    final authUser = credentialCreationOptions?.user;
    final authenticatorSelection =
        credentialCreationOptions?.authenticatorSelection;

    return RegisterRequestUserKey(
      reqId: v3RegisterRequestUserKey.reqId,
      credentialCreationOptions: credentialCreationOptions != null
          ? PublicKeyCredentialCreationOptions(
              challenge: credentialCreationOptions.challenge,
              rp: relayParty != null
                  ? RelayParty(
                      id: relayParty.id,
                      name: relayParty.name,
                    )
                  : null,
              user: authUser != null
                  ? AuthUser(
                      id: authUser.id,
                      name: authUser.name,
                      displayName: authUser.displayName,
                    )
                  : null,
              pubKeyCredParams: credentialCreationOptions.pubKeyCredParams
                  ?.map(
                    (e) => PublicKeyCredParam(
                      type: e.type,
                      alg: e.alg,
                    ),
                  )
                  .toList(),
              timeout: credentialCreationOptions.timeout,
              attestation: credentialCreationOptions.attestation,
              authenticatorSelection: authenticatorSelection != null
                  ? AuthenticatorSelectionCriteria(
                      authenticatorAttachment:
                          authenticatorSelection.authenticatorAttachment,
                      requireResidentKey:
                          authenticatorSelection.requireResidentKey,
                      userVerification: authenticatorSelection.userVerification,
                    )
                  : null,
            )
          : null,
    );
  }

  @override
  Future<String> getTokenExChange(V3TokenType tokenType) async {
    try {
      final request = V3TokenExchangeRequest(tokenType: tokenType);
      final response = await _authServiceApi.tokenExchange(body: request);

      // Assuming response has a property `token` of type String
      if (response.data != null && response.data!.data!.isNotEmpty) {
        final token = response.data!.data!;
        return token;
      } else {
        throw Exception("Empty or null token received");
      }
    } catch (e) {
      throw Exception("Token exchange failed: $e");
    }
  }
}
