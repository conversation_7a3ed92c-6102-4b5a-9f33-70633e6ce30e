import 'dart:async';
import 'dart:io';
import 'dart:isolate';

import 'package:chat/chat.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;

import '../../common/isolate/handlers/image_compress_handler.dart';

class SendImageMessageHandler {
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final MessagesBloc? messagesBloc;
  final int uploadConcurrency;

  SendImageMessageHandler({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.messagesBloc,
    this.uploadConcurrency = 1,
  });

  bool isDm() => !StringUtils.isNullOrEmpty(userId);

  Future<Message> createTemporaryPhotoMessageForResend({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required Attachment attachment,
    String? ref,
  }) async {
    final message = TempMessageFactory.createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_PHOTO,
      messageViewType: MessageViewType.imagesOwner,
    );

    message
      ..ref = ref ?? RandomUtils.randomUlId()
      ..attachmentTypeRaw = AttachmentType.PHOTO.rawValue();

    final oldFilePath = attachment.photo?.filePath ?? '';

    final fileRef = attachment.photo?.fileRef ?? '${UUIDUtils.random()}';
    var fileName =
        attachment.photo?.fileMetadata?.filename ?? '${UUIDUtils.random()}.jpg';

    // create directory for this image
    final fileDirPath = '${message.ref}/$fileRef';
    final fileDir = Directory('${FileUtils.cacheDirPath}/$fileDirPath');
    if (!fileDir.existsSync()) {
      await fileDir.create(recursive: true);
    }

    // copy file
    final newFilePath = '$fileDirPath/$fileName';
    await File('${FileUtils.cacheDirPath}/$oldFilePath')
        .copy('${FileUtils.cacheDirPath}/$newFilePath');

    // add paths to cache to avoid file system operations later
    FileUtils.addImagePathCache(
      messageRef: message.ref!,
      fileRef: fileRef,
      path: '${FileUtils.cacheDirPath}/${newFilePath}',
    );

    final newAttachment = Attachment(
      attachmentId: attachment.attachmentId,
      ref: attachment.ref,
      isTemp: true,
      attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
    )..photo = MediaObject(
        attachmentId: attachment.attachmentId,
        fileId: attachment.photo?.fileId ?? '',
        fileRef: fileRef,
        attachmentType: AttachmentType.PHOTO.rawValue(),
        filePath: newFilePath,
        fileStatus: AttachmentStatusEnum.UPLOADING.rawValue(),
        fileMetadata: attachment.photo!.fileMetadata,
        compressed: attachment.photo?.compressed ?? false,
      );
    newAttachment.message.target = message;
    message.mediaAttachments.add(newAttachment);
    return message;
  }

  // Original-structure method with parallel dimension extraction on main thread
  Future<Message> createTemporaryPhotoMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required List<UploadFile> imageList,
    String? ref,
  }) async {
    final message = TempMessageFactory.createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_PHOTO,
      messageViewType: MessageViewType.imagesOwner,
    );

    message
      ..ref = ref ?? RandomUtils.randomUlId()
      ..attachmentTypeRaw = AttachmentType.PHOTO.rawValue();

    final baseDir = Directory('${FileUtils.cacheDirPath}/${message.ref}');
    if (!baseDir.existsSync()) {
      await baseDir.create(recursive: true);
    }

    //region Isolate for image copy and  compress
    // Create ReceivePort to receive messages from background isolate
    final receivePort = ReceivePort();
    final sendPort = receivePort.sendPort;
    final rootIsolateToken = RootIsolateToken.instance!;

    // offload file copy to isolate
    final params = _ComputeParams(
      imageList,
      baseDir.path,
      message.ref!,
      sendPort,
      rootIsolateToken,
    );

    await Isolate.spawn(_computeCopyInfos, params);

    final copyInfos = (await receivePort.first) as List<_AttachmentCopyInfo>;

    receivePort.close();
    //endregion Isolate for image copy and dimension extraction
    // add paths to cache to avoid file system operations later
    unawaited(_precacheImage(copyInfos, message));

    // on main thread: get dimensions in parallel
    final futures = copyInfos.map((info) async {
      final filePath = '${FileUtils.cacheDirPath}/${info.filePath}';
      final dims = await getImageDimensions(
        filePath,
      );

      return AttachmentData(
        fileRef: info.fileRef,
        fileName: info.fileName,
        filePath: info.filePath,
        width: dims.width.toInt(),
        height: dims.height.toInt(),
        compressed: info.compressed,
      );
    }).toList();
    final attachmentsData = await Future.wait(futures);

    // recreate attachments
    for (final data in attachmentsData) {
      final attachment = Attachment(
        attachmentId: data.fileRef,
        ref: data.fileRef,
        isTemp: true,
        attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
      )..photo = MediaObject(
          attachmentId: data.fileRef,
          fileId: data.fileRef,
          fileRef: data.fileRef,
          attachmentType: AttachmentType.PHOTO.rawValue(),
          filePath: data.filePath,
          fileStatus: AttachmentStatusEnum.UPLOADING.rawValue(),
          fileMetadata: FileMetadata(
            dimensions: Dimensions(
              width: data.width,
              height: data.height,
            ),
            filename: data.fileName,
          ),
          compressed: data.compressed,
        );

      attachment.message.target = message;
      message.mediaAttachments.add(attachment);
    }

    return message;
  }

  Future<void> _precacheImage(
    List<_AttachmentCopyInfo> copyInfos,
    Message message,
  ) async {
    for (final info in copyInfos) {
      final fullPath = '${FileUtils.cacheDirPath}/${info.filePath}';
      FileUtils.addImagePathCache(
        messageRef: message.ref!,
        fileRef: info.fileRef,
        path: fullPath,
      );
    }
  }
}

// Top-level function run in isolate to handle heavy file I/O
@pragma('vm:entry-point')
Future<List<_AttachmentCopyInfo>> _computeCopyInfos(
  _ComputeParams params,
) async {
  // Initialize BackgroundIsolateBinaryMessenger to call platform channel
  BackgroundIsolateBinaryMessenger.ensureInitialized(params.rootIsolateToken);

  final infos = <_AttachmentCopyInfo>[];

  for (final image in params.images) {
    // ensure fileRef
    final fileRef = image.fileRef ?? UUIDUtils.random();

    // create directory for this image
    final fileDir = Directory('${params.baseDirPath}/$fileRef');
    await fileDir.create(recursive: true);

    // copy file
    var fileName = image.name;
    final cachedPath = '${fileDir.path}/$fileName';

    var compressed = false;
    final imageLength = await File(image.path).length();
    // If the image is larger than the max size, compress it
    if (imageLength > GlobalConfig.maxImageFileSize) {
      fileName = path.basenameWithoutExtension(cachedPath);
      fileName += FileUtils.imageCompressedExtensions;
      await ImageCompressHandler().compressImageForMessage(
        filePath: image.path,
        outputPath: '${fileDir.path}/$fileName',
      );
      compressed = true;
    } else {
      // If the image is small enough, just copy it
      await File(image.path).copy(cachedPath);
    }

    infos.add(
      _AttachmentCopyInfo(
        fileRef: fileRef,
        fileName: fileName,
        filePath: '${params.messageRef}/$fileRef/$fileName',
        compressed: compressed,
      ),
    );
  }
  params.sendSort.send(infos);
  return infos;
}

Future<Size> getImageDimensions(String path) async {
  return await ImageCompressHandler().getImageDimensionsFormFile(File(path));
}

// Parameter object for isolate processing
class _ComputeParams {
  final List<UploadFile> images;
  final String baseDirPath;
  final String messageRef;
  final SendPort sendSort;
  final RootIsolateToken rootIsolateToken;

  _ComputeParams(
    this.images,
    this.baseDirPath,
    this.messageRef,
    this.sendSort,
    this.rootIsolateToken,
  );
}

// Data returned from isolate: only file copy info
class _AttachmentCopyInfo {
  final String fileRef;
  final String fileName;
  final String filePath;
  final bool compressed;

  _AttachmentCopyInfo({
    required this.fileRef,
    required this.fileName,
    required this.filePath,
    this.compressed = false,
  });
}

class AttachmentData {
  final String fileRef;
  final String fileName;
  final String filePath;
  final int width;
  final int height;
  final bool compressed;

  AttachmentData({
    required this.fileRef,
    required this.fileName,
    required this.filePath,
    required this.width,
    required this.height,
    this.compressed = false,
  });
}
