//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_channel_creation_completed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ChannelCreationCompletedEventData {
  /// Returns a new [V3ChannelCreationCompletedEventData] instance.
  V3ChannelCreationCompletedEventData({
    this.workspaceId,
    this.channelId,
  });

  @Json<PERSON>ey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ChannelCreationCompletedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId;

  @override
  int get hashCode => workspaceId.hashCode + channelId.hashCode;

  factory V3ChannelCreationCompletedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3ChannelCreationCompletedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ChannelCreationCompletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
