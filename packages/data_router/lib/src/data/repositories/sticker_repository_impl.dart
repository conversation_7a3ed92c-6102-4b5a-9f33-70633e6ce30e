import 'dart:async';
import 'dart:math' show Random;

import 'package:injectable/injectable.dart' hide Order;

import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/sticker_repository.dart';
import '../database/entities/collection.dart';
import '../database/entities/sticker.dart';
import '../database/entities/sticker_frame_count.dart';
import '../database/generated/objectbox.g.dart';
import '../local/session_box.dart';

/// Unified Sticker Repository Implementation
/// Handles all sticker-related entities (Collection, Sticker, StickerFrameCount) using SessionBox
///
/// This follows the data_router pattern where one repository implementation
/// manages all related entities with automatic session filtering.
@LazySingleton(as: StickerRepository)
class StickerRepositoryImpl extends BaseRepository<Sticker>
    implements StickerRepository {
  final SessionBox<Collection> _collectionSessionBox;
  final SessionBox<StickerFrameCount> _frameCountSessionBox;
  final SessionBox<Sticker> stickerSessionBox;

  StickerRepositoryImpl(
    this._collectionSessionBox,
    this._frameCountSessionBox,
    this.stickerSessionBox,
  ) : super(stickerSessionBox) {
    // Custom value for save sticker before login
    _frameCountSessionBox.customActiveSessionKey = "_StickerFrameSessionKey";
  }

  //region Sticker Operations

  @override
  Future<Sticker?> getSticker(String stickerId) async {
    try {
      return stickerSessionBox
          .getFirstWith(Sticker_.stickerId.equals(stickerId));
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Sticker>> getAllStickers() async {
    try {
      return stickerSessionBox.getAll();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> saveSticker(Sticker sticker) async {
    try {
      stickerSessionBox.put(sticker);
      // Attempt to link with an existing frame count after saving
      await _linkStickerAndFrameCount(
        stickerUrl: sticker.url,
        stickerId: sticker.stickerId,
      );
    } catch (e) {
      throw Exception('Failed to save sticker: $e');
    }
  }

  @override
  Future<void> saveStickers(List<Sticker> stickers) async {
    if (stickers.isEmpty) {
      return;
    }
    try {
      stickerSessionBox.putMany(stickers);
      // Attempt to link each sticker after saving
      for (final sticker in stickers) {
        await _linkStickerAndFrameCount(
          stickerUrl: sticker.url,
          stickerId: sticker.stickerId,
        );
      }
    } catch (e) {
      throw Exception('Failed to save stickers: $e');
    }
  }

  @override
  Future<bool> deleteSticker(String stickerId) async {
    try {
      final sticker = await getSticker(stickerId);
      if (sticker == null) {
        return false;
      }
      // Optionally, also delete the associated StickerFrameCount
      final frameCount = getFrameCount(sticker.url);
      if (frameCount != null) {
        _frameCountSessionBox.remove(frameCount.id);
      }
      return stickerSessionBox.remove(sticker.id);
    } catch (e) {
      return false;
    }
  }

  @override
  Stream<Sticker?> watchSticker(String stickerId) {
    return stickerSessionBox
        .watch(Sticker_.stickerId.equals(stickerId))
        .map((query) => query.findFirst());
  }

  @override
  Stream<List<Sticker>> watchAllStickers() {
    return stickerSessionBox.watchAll().map((query) => query.find());
  }

  @override
  Future<List<Sticker>> searchStickers(String query) async {
    // Try local search first
    final localStickers = stickerSessionBox.getAll();
    final localResults = localStickers
        .where(
          (s) =>
              s.name.toLowerCase().contains(query.toLowerCase()) ||
              s.tags.any(
                (tag) => tag.toLowerCase().contains(query.toLowerCase()),
              ),
        )
        .toList();

    // If no local results, try remote search
    if (localResults.isEmpty) {
      // For now, return empty list if no local results
      // Remote search should be handled by DataOperation layer
      return [];
    }

    return localResults;
  }

  @override
  Future<List<Sticker>> getStickersForCollection(String collectionId) async {
    final allStickers = stickerSessionBox.getAll();
    return allStickers.where((s) => s.collectionId == collectionId).toList()
      ..sort((a, b) => a.weight.compareTo(b.weight));
  }

  @override
  Future<List<Sticker>> syncStickersForCollection(String collectionId) async {
    // Sync should be handled by DataOperation layer
    // For now, return local stickers for the collection
    return getStickersForCollection(collectionId);
  }

  @override
  Future<void> markStickerAsSent(String stickerId) async {
    try {
      final sticker =
          stickerSessionBox.getFirstWith(Sticker_.stickerId.equals(stickerId));
      if (sticker != null) {
        final updatedSticker = sticker.copyWith(
          sentTime: DateTime.now(),
          updateTime: DateTime.now(),
        );
        stickerSessionBox.put(updatedSticker);
      }
    } catch (e) {
      throw Exception('Failed to mark sticker as sent: $e');
    }
  }

  @override
  Future<List<Sticker>> getRecentStickers({int limit = 20}) async {
    try {
      final allStickers = stickerSessionBox.getAll();
      final recentStickers =
          allStickers.where((s) => s.sentTime != null).toList()
            ..sort(
              (a, b) => (b.sentTime ?? DateTime.now()).compareTo(
                a.sentTime ?? DateTime.now(),
              ),
            );

      return recentStickers.take(limit).toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<Sticker?> getWaveSticker() async {
    try {
      final allStickers = stickerSessionBox.getAll();
      final allHelloStickers = allStickers
          .where(
            (s) =>
                s.name.toLowerCase().contains('hello') ||
                s.tags.any((tag) => tag.toLowerCase().contains('hello')),
          )
          .toList();

      if (allHelloStickers.isEmpty) {
        return null;
      }

      return allHelloStickers[Random().nextInt(allHelloStickers.length)];
    } catch (e) {
      return null;
    }
  }

  //endregion

  //region Collection Operations

  @override
  Future<Collection?> getCollection(String collectionId) async {
    try {
      return _collectionSessionBox.getFirstWith(
        Collection_.collectionId.equals(collectionId),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<List<Collection>> getAllCollections() async {
    try {
      return _collectionSessionBox.getAll();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> saveCollection(Collection collection) async {
    try {
      _collectionSessionBox.put(collection);
    } catch (e) {
      throw Exception('Failed to save collection: $e');
    }
  }

  @override
  Future<void> saveCollections(List<Collection> collections) async {
    try {
      _collectionSessionBox.putMany(collections);
    } catch (e) {
      throw Exception('Failed to save collections: $e');
    }
  }

  @override
  Future<bool> deleteCollection(String collectionId) async {
    try {
      final collection = await getCollection(collectionId);
      if (collection != null) {
        return _collectionSessionBox.remove(collection.id);
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<List<Collection>> syncCollections() async {
    // Sync should be handled by DataOperation layer
    // For now, return local collections
    return getAllCollections();
  }

  @override
  Future<List<Collection>> getCollectionsByType(String type) async {
    final allCollections = await getAllCollections();
    return allCollections
        .where((c) => c.type.toLowerCase() == type.toLowerCase())
        .toList()
      ..sort((a, b) => a.weight.compareTo(b.weight));
  }

  @override
  Future<List<Collection>> getPremiumCollections() async {
    return getCollectionsByType('premium');
  }

  @override
  Future<List<Collection>> getFreeCollections() async {
    return getCollectionsByType('free');
  }

  /// [OPTIMIZATION] Reactive streams instead of polling for better performance
  @override
  Stream<List<Collection>> watchAllCollections() {
    return _collectionSessionBox.watchAll().map((query) => query.find());
  }

  @override
  Stream<List<Collection>> watchCollectionsByType(String type) {
    return _collectionSessionBox
        .watch(Collection_.type.equals(type))
        .map((query) => query.find());
  }

  //endregion

  //region StickerFrameCount Operations

  @override
  StickerFrameCount? getFrameCount(String stickerUrl) {
    if (stickerUrl.isEmpty) {
      return null;
    }
    try {
      return _frameCountSessionBox.getFirstWith(
        StickerFrameCount_.stickerUrl.equals(stickerUrl),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> saveFrameCount(StickerFrameCount frameCount) async {
    if (frameCount.stickerUrl.isEmpty) {
      return;
    }
    try {
      await _frameCountSessionBox.put(frameCount);
      // Link with sticker after saving frame count
      await _linkStickerAndFrameCount(stickerUrl: frameCount.stickerUrl);
    } catch (e) {
      throw Exception('Failed to save frame count: $e');
    }
  }

  Future<bool> deleteFrameCount(String stickerUrl) async {
    if (stickerUrl.isEmpty) {
      return false;
    }
    try {
      final frameCount = getFrameCount(stickerUrl);
      if (frameCount == null) {
        return false;
      }

      // Find and unlink from the sticker before deleting
      final sticker =
          await stickerSessionBox.getFirstWith(Sticker_.url.equals(stickerUrl));

      if (sticker != null) {
        sticker.frameCountData.target = null;
        sticker.syncFrameCount(); // Resets frameCount and isAnimated
        await stickerSessionBox.put(sticker);
      }

      return await _frameCountSessionBox.remove(frameCount.id);
    } catch (e) {
      return false;
    }
  }

  /// Centralized method to link Sticker and StickerFrameCount.
  /// Can be triggered by saving either a Sticker or a StickerFrameCount.
  Future<void> _linkStickerAndFrameCount({
    String? stickerUrl,
    String? stickerId,
  }) async {
    if (stickerUrl == null && stickerId == null) {
      return;
    }
    try {
      // Find both entities. Prioritize finding by URL as it's the primary link key.
      final sticker = stickerUrl != null
          ? await stickerSessionBox
              .getFirstWith(Sticker_.url.equals(stickerUrl))
          : await getSticker(stickerId!);

      if (sticker == null || sticker.url.isEmpty) {
        return; // No sticker or sticker URL to link with
      }

      final frameCount = getFrameCount(sticker.url);

      // If one exists and the other doesn't, we can't link.
      // If both exist, we link them.
      if (frameCount != null) {
        final isAlreadyLinked =
            sticker.frameCountData.target?.id == frameCount.id;
        final needsSync = sticker.effectiveFrameCount != frameCount.frameCount;

        if (!isAlreadyLinked || needsSync) {
          sticker.frameCountData.target = frameCount;
          sticker.syncFrameCount();
          await stickerSessionBox.put(sticker);
        }
      }
    } catch (e) {
      // Silently fail to avoid crashing the app on background sync issues.
      // Consider logging this error to your monitoring service.
    }
  }

  @override
  Future<void> updateFrameCount({
    required String stickerUrl,
    required String sessionKey,
    required int frameCount,
    String? firstFrameData,
  }) async {
    final existing = getFrameCount(stickerUrl);

    if (existing != null) {
      final updated = existing.copyWith(
        frameCount: frameCount,
        firstFrameData: firstFrameData,
        updateTime: DateTime.now(),
      );
      await saveFrameCount(updated);
    } else {
      final newFrameCount = StickerFrameCount.createNew(
        stickerUrl: stickerUrl,
        sessionKey: sessionKey,
        frameCount: frameCount,
        firstFrameData: firstFrameData,
      );
      await saveFrameCount(newFrameCount);
    }
  }

  @override
  Future<void> createAnimatedStickerFrameCount({
    required String stickerUrl,
    required String sessionKey,
    required int frameCount,
    String? firstFrameData,
  }) async {
    if (frameCount > 1) {
      await updateFrameCount(
        stickerUrl: stickerUrl,
        sessionKey: sessionKey,
        frameCount: frameCount,
        firstFrameData: firstFrameData,
      );
    }
  }

  @override
  Future<void> createStaticStickerFrameCount({
    required String stickerUrl,
    required String sessionKey,
    String? firstFrameData,
  }) async {
    await updateFrameCount(
      stickerUrl: stickerUrl,
      sessionKey: sessionKey,
      frameCount: 1,
      firstFrameData: firstFrameData,
    );
  }

  /// [OPTIMIZATION] Reactive stream instead of polling for frame count watching
  @override
  Stream<StickerFrameCount?> watchFrameCount(String stickerUrl) {
    return _frameCountSessionBox
        .watch(StickerFrameCount_.stickerUrl.equals(stickerUrl))
        .map((query) => query.findFirst())
        .distinct();
  }

  //endregion

  //region Session Operations

  @override
  Future<void> initializeForSession(String sessionKey) async {
    // SessionBox automatically handles session initialization
    // No additional action needed
  }

  @override
  Future<void> clearSessionData(String sessionKey) async {
    // For cross-session operations, use underlying box
    try {
      // Clear stickers
      final stickerQuery = stickerSessionBox.box
          .query(Sticker_.sessionKey.equals(sessionKey))
          .build();
      stickerQuery.remove();
      stickerQuery.close();

      // Clear collections
      final collectionQuery = _collectionSessionBox.box
          .query(Collection_.sessionKey.equals(sessionKey))
          .build();
      collectionQuery.remove();
      collectionQuery.close();

      // Clear frame counts (No need to remove due to shared use)
      // final frameCountQuery = _frameCountSessionBox.box
      //     .query(StickerFrameCount_.sessionKey.equals(sessionKey))
      //     .build();
      // frameCountQuery.remove();
      // frameCountQuery.close();
    } catch (e) {
      throw Exception('Failed to clear session data: $e');
    }
  }

  @override
  Future<List<Sticker>> getStickersBySession(String sessionKey) async {
    try {
      // For cross-session queries, use underlying box
      final query = stickerSessionBox
          .queryWith(Sticker_.sessionKey.equals(sessionKey))
          .build();
      final stickers = query.find();
      query.close();
      return stickers;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<Collection>> getCollectionsBySession(String sessionKey) async {
    try {
      // For cross-session queries, use underlying box
      final query = _collectionSessionBox
          .queryWith(Collection_.sessionKey.equals(sessionKey))
          .build();
      final collections = query.find();
      query.close();
      return collections;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<List<StickerFrameCount>> getFrameCountsBySession(
    String sessionKey,
  ) async {
    try {
      // For cross-session queries, use underlying box
      final query = _frameCountSessionBox
          .queryWith(StickerFrameCount_.sessionKey.equals(sessionKey))
          .build();
      final frameCounts = query.find();
      query.close();
      return frameCounts;
    } catch (e) {
      return [];
    }
  }

  @override
  Stream<List<Sticker>> watchStickersBySession(String sessionKey) {
    // For cross-session watching, use underlying box
    final box = stickerSessionBox;
    return box
        .watch(Sticker_.sessionKey.equals(sessionKey))
        .map((query) => query.find());
  }

  @override
  Stream<List<Collection>> watchCollectionsBySession(String sessionKey) {
    // For cross-session watching, use underlying box
    final box = _collectionSessionBox;
    return box
        .watch(Collection_.sessionKey.equals(sessionKey))
        .map((query) => query.find());
  }

  //endregion

  //region Utility Operations

  @override
  Future<Map<String, int>> getStatistics() async {
    try {
      final collections = await getAllCollections();
      final stickers = await getAllStickers();

      return {
        'collections': collections.length,
        'stickers': stickers.length,
        'animatedStickers': stickers.where((s) => s.isAnimated).length,
        'premiumCollections': collections.where((c) => c.isPremium).length,
      };
    } catch (e) {
      return {
        'collections': 0,
        'stickers': 0,
        'animatedStickers': 0,
        'premiumCollections': 0,
      };
    }
  }

  @override
  Future<void> refreshAllData() async {
    // Data refresh should be handled by DataOperation layer
    // Repository layer only handles local storage operations
  }

  @override
  Future<bool> stickerExists(String stickerId) async {
    try {
      final sticker = await getSticker(stickerId);
      return sticker != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> collectionExists(String collectionId) async {
    try {
      final collection = await getCollection(collectionId);
      return collection != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<int> getTotalStickerCount() async {
    try {
      final stickers = await getAllStickers();
      return stickers.length;
    } catch (e) {
      return 0;
    }
  }

  @override
  Future<int> getTotalCollectionCount() async {
    try {
      final collections = await getAllCollections();
      return collections.length;
    } catch (e) {
      return 0;
    }
  }

//endregion
}
