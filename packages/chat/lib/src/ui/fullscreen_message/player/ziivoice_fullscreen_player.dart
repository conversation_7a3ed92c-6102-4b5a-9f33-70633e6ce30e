import 'dart:async';
import 'dart:core';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../utils/player_controller_manager.dart';

class ZiiVoiceFullscreenPlayer extends StatefulWidget {
  const ZiiVoiceFullscreenPlayer({
    super.key,
    required this.url,
    required this.path,
    required this.messageItem,
    this.caption = '',
    this.isActive = false,
  });

  final String? url;
  final String? path;
  final String caption;
  final ui.MessageItem messageItem;
  final bool isActive;

  @override
  State<ZiiVoiceFullscreenPlayer> createState() =>
      _ZiiVoiceFullscreenPlayerState();
}

class _ZiiVoiceFullscreenPlayerState extends State<ZiiVoiceFullscreenPlayer>
    with WidgetsBindingObserver {
  PlayerController? playerController;
  final playerControllerManager = PlayerControllerManager();
  Timer? _timer;

  bool showMoreAvailable = false;
  bool _isLoading = false;
  int? initialPosition;

  String _currentControllerKey = '';
  String _currentPath = '';
  late String newPath;
  late String messageId;
  final ScrollController scrollController = ScrollController();
  late List<double> waveFormData;
  final isPlaying = ValueNotifier(false);

  final ValueNotifier<Widget> playButton = ValueNotifier(
    SizedBox(
      width: 60.w,
      height: 60.w,
    ),
  );

  @override
  void initState() {
    super.initState();
    messageId = widget.messageItem.messageId;
    _initializeHolderIfAvailable();
    newPath = widget.path ?? '';
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkShowMoreAvailability();
    });
    _initializeZiiVoice();
  }

  void _initializeHolderIfAvailable() {
    final holder =
        playerControllerManager.getStateHolder(widget.messageItem.messageId);
    if (holder != null) {
      _currentControllerKey = holder.key;
      playerController = holder.controller;
      waveFormData = holder.waveFormData;
      _currentPath = holder.path;
      playerControllerManager.markControllerAsRecentlyUsed(
        _currentPath,
        widget.messageItem.messageId,
        waveFormData,
      );
      playerController?.addListener(_listener);

      if (holder.isPlaying && widget.isActive) {
        playerController?.seekTo(holder.currentPosition);
        _onPlayZiiVoice();
      }
      playerController?.onCurrentDurationChanged.listen((event) {
        initialPosition = event;
      });

      Log.e(
        name: '_initializeHolderIfAvailable: loaded existing holder',
        waveFormData.length,
      );
    } else {
      waveFormData = [];
      initialPosition = 0;
    }
  }

  Future<void> _initializeZiiVoice() async {
    final key = playerControllerManager.getKey(messageId);

    if (key == _currentControllerKey &&
        playerController != null &&
        ui.StringUtil.stringIsNotEmpty(_currentPath)) {
      setState(() {});
      playerController?.addListener(_listener);
      playerController!.waveformExtraction.onCurrentExtractedWaveformData
          .listen((onData) {
        waveFormData = onData;
      });
      playerController?.onCompletion.listen((_) async {
        await playerController?.seekTo(0);
        _onPlayZiiVoice();
      });
      if (widget.isActive && playerController != null && mounted) {
        _onPlayZiiVoice();
      }
      return;
    }

    playerController?.removeListener(_listener);

    setState(() {
      _isLoading = true;
    });

    try {
      if (!ui.StringUtil.stringIsNotEmpty(_currentPath) &&
          !ui.StringUtil.stringIsNotEmpty(newPath)) {
        newPath = (await _downloadFileAndCache()).path;
      }
      playerController = await playerControllerManager.getController(
        path: newPath,
        messageId: messageId,
        waveFormData: waveFormData,
      );
      _currentPath = newPath;

      playerController?.addListener(_listener);

      playerController?.onCompletion.listen((_) async {
        await playerController?.seekTo(0);
        _onPlayZiiVoice();
      });
      _currentControllerKey = key;

      playerController!.waveformExtraction.onCurrentExtractedWaveformData
          .listen((onData) {
        waveFormData = onData;
      });

      initialPosition =
          await playerController?.getDuration(DurationType.current);
      if (widget.isActive &&
          playerController != null &&
          mounted &&
          playerController!.playerState.isInitialised) {
        _onPlayZiiVoice();
      }
    } catch (e) {
      Log.e(name: 'Error initializing ZiiVoice:', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _listener() {
    final newState = playerController?.playerState.isPlaying ?? false;
    Log.e(
        name: '_ZiiVoiceFullscreenPlayerState._listener',
        [isPlaying, newState]);
    if (isPlaying.value != newState) {
      if (mounted) isPlaying.value = newState;
    }
    playerController?.onCurrentDurationChanged.listen((event) {
      initialPosition = event;
    });
  }

  Future<File> _downloadFileAndCache() async =>
      await AppCacheManager().getFile(UrlUtils.parseCDNUrl(widget.url));

  void _onPauseZiiVoice() {
    if (playerController != null) {
      playButton.value = _buildPlayIcon();
      playerController?.pausePlayer();
    }
  }

  void _onReset() {
    if (playerController != null) {
      Log.e(name: '_ZiiVoiceFullscreenPlayerState._onReset', playerController);
      playerController?.pausePlayer();
      playerController?.seekTo(0);
    }
  }

  void _onPlayZiiVoice() {
    if (playerController != null) {
      Log.e('_ZiiVoiceFullscreenPlayerState._onPlayZiiVoice');
      playButton.value = const SizedBox(width: 60, height: 60);
      playerController?.startPlayer();
    }
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer(const Duration(seconds: 2), () {
      if (playerController?.playerState.isPlaying ?? false) {
        playButton.value = const SizedBox(width: 60, height: 60);
      }
    });
  }

  void _onPlayAfterPauseZiiVoice() {
    playButton.value = _buildPauseIcon();

    playerController?.setFinishMode(finishMode: FinishMode.pause);
    playerController?.startPlayer();
  }

  @override
  void dispose() {
    Log.e('_ZiiVoiceFullscreenPlayerState.dispose');
    WidgetsBinding.instance.removeObserver(this);

    _savePlaybackStateBeforeDispose();
    playerController?.removeListener(_listener);

    scrollController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _savePlaybackStateBeforeDispose() async {
    if (playerController != null) {
      final isPlaying = playerController!.playerState.isPlaying;

      playerControllerManager.saveState(
        messageId,
        isPlaying: isPlaying,
        position: initialPosition!,
      );
    }
  }

  @override
  void didUpdateWidget(covariant ZiiVoiceFullscreenPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);

    Log.e(
      name: '_ZiiVoiceFullscreenPlayerState.didUpdateWidget',
      widget.isActive,
    );

    final controller = playerController;

    if (widget.isActive && controller != null) {
      controller.getDuration(DurationType.current).then((durationMs) {
        final isAtZero = durationMs == 0;

        if (!controller.playerState.isPlaying && isAtZero) {
          _onPlayZiiVoice();
        }
      });

      playerControllerManager.markControllerAsRecentlyUsed(
        _currentPath,
        messageId,
        waveFormData,
      );
    } else if (!widget.isActive) {
      _onReset();
    }
  }

  Widget _buildPauseIcon() {
    return ClipOval(
      child: SizedBox(
        width: 60.w,
        height: 60.w,
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: Color.fromRGBO(255, 255, 255, 0.2),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: ui.AppAssets.pngIconAsset(
              key: const ValueKey(true),
              ui.AppAssets.icPauseNew,
              color: Colors.white,
              boxFit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlayIcon() {
    return ClipOval(
      child: SizedBox(
        width: 60.w,
        height: 60.w,
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: Color.fromRGBO(255, 255, 255, 0.2),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: ui.AppAssets.pngIconAsset(
              key: const ValueKey(false),
              ui.AppAssets.icPlayNew,
              color: Colors.white,
              boxFit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      _onPauseZiiVoice();
    } else if (state == AppLifecycleState.resumed) {
      _onPlayZiiVoice();
    }
  }

  void _checkShowMoreAvailability() {
    final textStyle = Theme.of(context)
        .textTheme
        .headlineSmall
        ?.copyWith(color: Colors.white);

    final textPainter = TextPainter(
      text: TextSpan(text: widget.caption, style: textStyle),
      maxLines: 9,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 40.w);

    setState(() {
      showMoreAvailable = textPainter.didExceedMaxLines;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isPlaying,
      builder: (context, isPlayingValue, child) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            if (isPlayingValue) {
              _onPauseZiiVoice();
            } else {
              _onPlayAfterPauseZiiVoice();
              _startTimer();
            }
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCaption(),
              SizedBox(height: 36.h),
              _buildAudioWaveForm(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingWidget() {
    return SizedBox(
      width: 60.w,
      height: 60.w,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: const CircularProgressIndicator(
            backgroundColor: ui.AppColors.lightBlue,
            color: ui.AppColors.primaryBlue,
            strokeWidth: 3,
            trackGap: 3,
          ),
        ),
      ),
    );
  }

  Widget _buildAudioWaveForm() {
    final waveWidth = MediaQuery.of(context).size.width - 160.w;
    if (_isLoading) {
      return _buildLoadingWidget();
    }
    Log.e(
        name: '_ZiiVoiceFullscreenPlayerState._buildAudioWaveForm',
        [waveFormData.length, initialPosition]);

    return Column(
      children: [
        AudioFileWaveforms(
          key: ValueKey('${messageId}'),
          playerController: playerController,
          size: Size(waveWidth, 100.h),
          waveformData: waveFormData,
          waveformType: WaveformType.long,
          continuousWaveform: true,
          animationDuration: const Duration(milliseconds: 10),
          playerWaveStyle: PlayerWaveStyle(
            showSeekLine: false,
            fixedWaveColor: Colors.white,
            liveWaveColor: ui.AppColors.primaryBlue,
            scaleFactor: 100,
            waveThickness: 2.8,
          ),
        ),
        SizedBox(height: 36.h),
        _buildButton(),
      ],
    );
  }

  Widget _buildButton() {
    return ValueListenableBuilder<Widget>(
      valueListenable: playButton,
      builder: (_, value, __) => Align(
        child: ui.AppSizeTransition(child: value),
      ),
    );
  }

  Widget _buildCaption() {
    final textStyle = !showMoreAvailable
        ? Theme.of(context)
            .textTheme
            .titleLarge
            ?.copyWith(color: Colors.white, fontSize: 20.sp)
        : Theme.of(context)
            .textTheme
            .titleMedium
            ?.copyWith(color: Colors.white, fontSize: 18.sp);

    final localization = AppLocalizations.of(context)!;

    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: 1.sh - 450.h),
      child: RawScrollbar(
        thickness: 2.w,
        thumbVisibility: true,
        thumbColor: Colors.white,
        controller: scrollController,
        radius: Radius.circular(8.r),
        padding: EdgeInsets.only(right: 8.w),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: SingleChildScrollView(
            controller: scrollController,
            child: ui.AppExpandableText(
              widget.caption,
              maxLines: 10,
              textAlign: TextAlign.center,
              expandText: localization.showMore,
              linkColor: ui.AppColors.seeMoreFullScreenColor,
              style: textStyle,
            ),
          ),
        ),
      ),
    );
  }
}
