//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_created_event_data_geo_location.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserCreatedEventDataGeoLocation {
  /// Returns a new [V3UserCreatedEventDataGeoLocation] instance.
  V3UserCreatedEventDataGeoLocation({
    this.countryCode,
  });

  @JsonKey(
    name: r'countryCode',
    required: false,
    includeIfNull: false,
  )
  final String? countryCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserCreatedEventDataGeoLocation &&
          other.countryCode == countryCode;

  @override
  int get hashCode => countryCode.hashCode;

  factory V3UserCreatedEventDataGeoLocation.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserCreatedEventDataGeoLocationFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserCreatedEventDataGeoLocationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
