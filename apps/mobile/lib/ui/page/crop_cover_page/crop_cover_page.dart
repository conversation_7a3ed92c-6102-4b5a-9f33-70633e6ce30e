import 'package:auto_route/auto_route.dart';
import 'package:cross_file/cross_file.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

@RoutePage()
class CropCoverPage extends StatelessWidget {
  const CropCoverPage({
    required this.photo,
    required this.avatarType,
    super.key,
    required this.user,
  });

  final XFile photo;
  final AvatarType avatarType;
  final User user;

  @override
  Widget build(BuildContext context) {
    return PopScope<Object?>(
      canPop: true,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          return;
        }
        if (context.mounted) {
          Navigator.pop(
            context,
            CropCoverPopResult(
              action: CropCoverPopResult.onSavedCover,
              result: false,
            ),
          );
        }
      },
      child: ui.CropCoverPhotoPage(
        onSave: (imageBytes) async {
          final output = XFile.fromData(imageBytes);
          final tempDir = await getTemporaryDirectory();
          final outputPath = '${tempDir.path}/cover_temp.png';
          await output.saveTo(outputPath);
          AppEventBus.publish(
            ChooseCoverEvent(
              id: 'ChooseCoverID',
              filePath: outputPath,
              avatarType: avatarType,
            ),
          );
          Navigator.pop(
            context,
            CropCoverPopResult(
              action: CropCoverPopResult.onSavedCover,
              result: true,
            ),
          );
        },
        username: user.username ?? '',
        displayname: user.profile?.displayName ?? '',
        imageAvatarPath: UrlUtils.parseAvatar(user.profile?.avatar),
        coverProfileData: photo,
        onBack: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

class CropCoverPopResult {
  final String action;
  final bool result;
  static final onSavedCover = "onSavedCover";

  CropCoverPopResult({
    required this.action,
    required this.result,
  });
}
