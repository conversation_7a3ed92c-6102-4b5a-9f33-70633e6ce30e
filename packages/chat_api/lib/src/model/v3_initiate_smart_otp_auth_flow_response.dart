//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/login_request_smart_otp.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_smart_otp_auth_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateSmartOtpAuthFlowResponse {
  /// Returns a new [V3InitiateSmartOtpAuthFlowResponse] instance.
  V3InitiateSmartOtpAuthFlowResponse({
    this.ok,
    this.error,
    this.loginRequest,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON><PERSON>(
    name: r'loginRequest',
    required: false,
    includeIfNull: false,
  )
  final LoginRequestSmartOtp? loginRequest;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateSmartOtpAuthFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.loginRequest == loginRequest;

  @override
  int get hashCode => ok.hashCode + error.hashCode + loginRequest.hashCode;

  factory V3InitiateSmartOtpAuthFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateSmartOtpAuthFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateSmartOtpAuthFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
