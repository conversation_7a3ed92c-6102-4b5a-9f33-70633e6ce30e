//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:chat_api/src/model/v3_attachment_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_dm_message_media_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendDmMessageMediaRequest {
  /// Returns a new [V3SendDmMessageMediaRequest] instance.
  V3SendDmMessageMediaRequest({
    this.userId,
    this.attachmentType,
    this.mediaObjects,
    this.ref,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'attachmentType',
    required: false,
    includeIfNull: false,
  )
  final V3AttachmentTypeEnum? attachmentType;

  @Json<PERSON>ey(
    name: r'mediaObjects',
    required: false,
    includeIfNull: false,
  )
  final List<V3MediaObject>? mediaObjects;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendDmMessageMediaRequest &&
          other.userId == userId &&
          other.attachmentType == attachmentType &&
          other.mediaObjects == mediaObjects &&
          other.ref == ref;

  @override
  int get hashCode =>
      userId.hashCode +
      attachmentType.hashCode +
      mediaObjects.hashCode +
      ref.hashCode;

  factory V3SendDmMessageMediaRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendDmMessageMediaRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendDmMessageMediaRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
