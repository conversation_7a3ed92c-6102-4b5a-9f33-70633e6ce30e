//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - SESSION_EXPIRATION_SETTING_ENUM_180_DAYS: expirations 180 days  - SESSION_EXPIRATION_SETTING_ENUM_7_DAYS: expirations 70 days  - SESSION_EXPIRATION_SETTING_ENUM_30_DAYS: expirations 30 days  - SESSION_EXPIRATION_SETTING_ENUM_90_DAYS: expirations 90 days
enum V3SessionExpirationSettingEnum {
  /// - SESSION_EXPIRATION_SETTING_ENUM_180_DAYS: expirations 180 days  - SESSION_EXPIRATION_SETTING_ENUM_7_DAYS: expirations 70 days  - SESSION_EXPIRATION_SETTING_ENUM_30_DAYS: expirations 30 days  - SESSION_EXPIRATION_SETTING_ENUM_90_DAYS: expirations 90 days
  @JsonValue(0)
  SESSION_EXPIRATION_SETTING_ENUM_180_DAYS('0'),

  /// - SESSION_EXPIRATION_SETTING_ENUM_180_DAYS: expirations 180 days  - SESSION_EXPIRATION_SETTING_ENUM_7_DAYS: expirations 70 days  - SESSION_EXPIRATION_SETTING_ENUM_30_DAYS: expirations 30 days  - SESSION_EXPIRATION_SETTING_ENUM_90_DAYS: expirations 90 days
  @JsonValue(1)
  SESSION_EXPIRATION_SETTING_ENUM_7_DAYS('1'),

  /// - SESSION_EXPIRATION_SETTING_ENUM_180_DAYS: expirations 180 days  - SESSION_EXPIRATION_SETTING_ENUM_7_DAYS: expirations 70 days  - SESSION_EXPIRATION_SETTING_ENUM_30_DAYS: expirations 30 days  - SESSION_EXPIRATION_SETTING_ENUM_90_DAYS: expirations 90 days
  @JsonValue(2)
  SESSION_EXPIRATION_SETTING_ENUM_30_DAYS('2'),

  /// - SESSION_EXPIRATION_SETTING_ENUM_180_DAYS: expirations 180 days  - SESSION_EXPIRATION_SETTING_ENUM_7_DAYS: expirations 70 days  - SESSION_EXPIRATION_SETTING_ENUM_30_DAYS: expirations 30 days  - SESSION_EXPIRATION_SETTING_ENUM_90_DAYS: expirations 90 days
  @JsonValue(3)
  SESSION_EXPIRATION_SETTING_ENUM_90_DAYS('3');

  const V3SessionExpirationSettingEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
