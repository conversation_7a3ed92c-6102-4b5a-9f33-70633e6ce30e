//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_device_info.dart';
import 'package:chat_api/src/model/common_attestation_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_register_with_user_key_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RegisterWithUserKeyRequest {
  /// Returns a new [V3RegisterWithUserKeyRequest] instance.
  V3RegisterWithUserKeyRequest({
    this.reqId,
    this.reqVerifier,
    this.credential,
    this.deviceInfo,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  /// The randomly generated value created by the client.
  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @J<PERSON><PERSON>ey(
    name: r'credential',
    required: false,
    includeIfNull: false,
  )
  final CommonAttestationResult? credential;

  @JsonKey(
    name: r'deviceInfo',
    required: false,
    includeIfNull: false,
  )
  final V3DeviceInfo? deviceInfo;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RegisterWithUserKeyRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.credential == credential &&
          other.deviceInfo == deviceInfo;

  @override
  int get hashCode =>
      reqId.hashCode +
      reqVerifier.hashCode +
      credential.hashCode +
      deviceInfo.hashCode;

  factory V3RegisterWithUserKeyRequest.fromJson(Map<String, dynamic> json) =>
      _$V3RegisterWithUserKeyRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3RegisterWithUserKeyRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
