//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_share_to_incoming_result.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_share_to_incoming_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListShareToIncomingResponse {
  /// Returns a new [V3ListShareToIncomingResponse] instance.
  V3ListShareToIncomingResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3ShareToIncomingResult>? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListShareToIncomingResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3ListShareToIncomingResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListShareToIncomingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListShareToIncomingResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
