//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'privacy_settings_restrict_saving_content.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class PrivacySettingsRestrictSavingContent {
  /// Returns a new [PrivacySettingsRestrictSavingContent] instance.
  PrivacySettingsRestrictSavingContent({
    this.enable,
  });

  @JsonKey(
    name: r'enable',
    required: false,
    includeIfNull: false,
  )
  final bool? enable;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PrivacySettingsRestrictSavingContent && other.enable == enable;

  @override
  int get hashCode => enable.hashCode;

  factory PrivacySettingsRestrictSavingContent.fromJson(
          Map<String, dynamic> json) =>
      _$PrivacySettingsRestrictSavingContentFromJson(json);

  Map<String, dynamic> toJson() =>
      _$PrivacySettingsRestrictSavingContentToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
