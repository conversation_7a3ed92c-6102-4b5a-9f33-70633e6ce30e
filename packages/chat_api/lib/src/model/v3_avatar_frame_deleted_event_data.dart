//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_avatar_frame_deleted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AvatarFrameDeletedEventData {
  /// Returns a new [V3AvatarFrameDeletedEventData] instance.
  V3AvatarFrameDeletedEventData({
    this.actorId,
    this.avatarFrame,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'avatarFrame',
    required: false,
    includeIfNull: false,
  )
  final String? avatarFrame;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AvatarFrameDeletedEventData &&
          other.actorId == actorId &&
          other.avatarFrame == avatarFrame;

  @override
  int get hashCode => actorId.hashCode + avatarFrame.hashCode;

  factory V3AvatarFrameDeletedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3AvatarFrameDeletedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3AvatarFrameDeletedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
