import 'dart:async';

import 'package:injectable/injectable.dart';

import '../../../core/enums/entity_enum.dart';
import '../../../domain/repositories/member_repository.dart';
import '../../database/entities/member.dart';
import 'base_local_data_source.dart';

/// Member-specific local data source implementation
/// Handles Member entity operations with ObjectBox database
///
/// This follows the Single Responsibility Principle by handling only Member entities
/// and provides specific query methods for member-related operations.
@LazySingleton()
class MemberLocalDataSource extends BaseLocalDataSource<Member> {
  MemberLocalDataSource(this._memberRepository);

  final MemberRepository _memberRepository;

  @override
  EntityEnum get entity => EntityEnum.member;

  @override
  Future<Member?> get(String id) async {
    throw UnimplementedError();
  }

  @override
  Future<List<Member>> getAll() async {
    throw UnimplementedError();
  }

  List<Member> getAllMember({
    required String channelId,
    required String workspaceId,
  }) {
    return _memberRepository.getAllMembers(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  @override
  Future<void> insert(Member item) async {
    try {
      if (!validate(item)) {
        handleValidationError(item, 'Invalid member data');
        return;
      }
      logOperation('Inserting member', item.memberId);
      _memberRepository.insertMember(item);
    } catch (e) {
      logOperation('Error inserting member', '${item.memberId}: $e');
      rethrow;
    }
  }

  @override
  Future<void> insertAll(List<Member> items) async {
    try {
      logOperation('Inserting multiple members', '${items.length} members');

      final validItems = items.where(validate).toList();
      if (validItems.length != items.length) {
        logOperation(
          'Some members failed validation',
          '${items.length - validItems.length} invalid',
        );
      }

      _memberRepository.insertMembers(validItems);

      logOperation(
        'Inserted multiple members successfully',
        '${validItems.length} members',
      );
    } catch (e) {
      logOperation('Error inserting multiple members', e.toString());
      rethrow;
    }
  }

  @override
  Future<bool> delete(String id) async {
    throw UnimplementedError();
  }

  @override
  Future<int> deleteAll() async {
    throw UnimplementedError();
  }

  @override
  Stream<Member?> watch(String id) {
    throw UnimplementedError();
  }

  @override
  Stream<List<Member>> watchAll() {
    throw UnimplementedError();
  }

  @override
  Future<bool> exists(String id) async {
    try {
      logOperation('Checking member existence', id);
      final member = await get(id);
      final exists = member != null;
      logOperation('Member existence check result', '$id = $exists');
      return exists;
    } catch (e) {
      logOperation('Error checking member existence', '$id: $e');
      return false;
    }
  }

  @override
  Future<int> count() async {
    throw UnimplementedError();
  }

  @override
  bool validate(Member item) {
    return item.isValid;
  }

  //endregion

  //region Specific Query Methods

  /// Get members for a specific channel
  Future<List<Member>> getMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      logOperation('Getting members for channel', '$workspaceId:$channelId');

      final members = await _memberRepository.getMembersWithRole(
        workspaceId: workspaceId,
        channelId: channelId,
      );

      // Sort by updateTime descending
      members.sort(
        (a, b) => (b.updateTime ?? DateTime.now())
            .compareTo(a.updateTime ?? DateTime.now()),
      );

      logOperation(
        'Found members for channel',
        '$workspaceId:$channelId: ${members.length} members',
      );
      return members;
    } catch (e) {
      logOperation(
        'Error getting members for channel',
        '$workspaceId:$channelId: $e',
      );
      return [];
    }
  }

  /// Watch members for a specific channel
  Stream<List<Member>> watchMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    logOperation('Watching members for channel', '$workspaceId:$channelId');
    return _memberRepository.watchMembersForChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  /// Watch specific member by workspace, channel and user ID
  Stream<Member?> watchMemberByIds({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) {
    logOperation('Watching member by IDs', '$workspaceId:$channelId:$userId');
    return _memberRepository.watchMember(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
    );
  }

  /// Get specific member by workspace, channel and user ID
  Future<Member?> getMemberByIds({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    try {
      logOperation('Getting member by IDs', '$workspaceId:$channelId:$userId');

      final member = await _memberRepository.getMember(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: userId,
      );

      if (member != null) {
        logOperation('Found member by IDs', member.memberId);
      } else {
        logOperation(
          'Member not found by IDs',
          '$workspaceId:$channelId:$userId',
        );
      }

      return member;
    } catch (e) {
      logOperation(
        'Error getting member by IDs',
        '$workspaceId:$channelId:$userId: $e',
      );
      return null;
    }
  }

  /// Check if member exists by IDs
  Future<bool> memberExistsByIds({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    try {
      logOperation(
        'Checking member existence by IDs',
        '$workspaceId:$channelId:$userId',
      );

      final member = await _memberRepository.getMember(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: userId,
      );
      final exists = member != null;

      logOperation(
        'Member existence check result',
        '$workspaceId:$channelId:$userId = $exists',
      );
      return exists;
    } catch (e) {
      logOperation(
        'Error checking member existence',
        '$workspaceId:$channelId:$userId: $e',
      );
      return false;
    }
  }

  /// Delete member by IDs
  Future<bool> deleteMemberByIds({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) async {
    try {
      logOperation('Deleting member by IDs', '$workspaceId:$channelId:$userId');

      final member = await getMemberByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: userId,
      );

      if (member != null) {
        final result = await _memberRepository.deleteMember(
          workspaceId: workspaceId,
          channelId: channelId,
          userId: userId,
        );
        if (result) {
          logOperation('Deleted member by IDs', member.memberId);
        }
        return result;
      }

      return false;
    } catch (e) {
      logOperation(
        'Error deleting member by IDs',
        '$workspaceId:$channelId:$userId: $e',
      );
      return false;
    }
  }

  //endregion

  @override
  Future<bool> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting all members by session key', sessionKey);
      _memberRepository.deleteAllBySessionKey(sessionKey: sessionKey);
      logOperation('Deleted all members by session key', '$count members');
      return true;
    } catch (e) {
      logOperation(
        'Error deleting all members by session key',
        '$sessionKey: $e',
      );
      return false;
    }
  }

  /// Delete all members for a specific channel
  bool deleteMembersForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    logOperation('Deleting members for channel', '$workspaceId:$channelId');
    return _memberRepository.deleteMembersForChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

//endregion
}
