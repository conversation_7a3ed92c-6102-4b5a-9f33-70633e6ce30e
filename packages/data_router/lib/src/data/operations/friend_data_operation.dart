import 'dart:convert';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data_router.dart';
import '../sources/local/channel_local_data_source.dart';
import '../sources/local/friend_local_data_source.dart';
import '../sources/local/user_local_data_source.dart';
import '../sources/remote/friend_remote_data_source.dart';
import '../sources/remote/user_remote_data_source.dart';
import '../sources/ws/events/friend_removed_event_data.dart';
import '../sources/ws/events/friend_request_event_data.dart';

/// Friend data operation handling business logic
/// Coordinates between local, remote, and WebSocket data sources
/// Implements optimistic updates for better UX
@Singleton()
class FriendDataOperation extends BaseDataOperation<Friend> {
  FriendDataOperation(
    this.webSocketSource,
    this.localSource,
    this.remoteSource,
    this.userLocalDataSource,
    this.userRemoteDataSource,
    this.channelLocalDataSource,
  ) : super(
          localSource: localSource,
          remoteSource: remoteSource,
          webSocketSource: webSocketSource,
        );

  @override
  final FriendLocalDataSource localSource;

  @override
  final FriendRemoteDataSource remoteSource;

  @override
  final FriendWebSocketDataSource webSocketSource;

  final UserLocalDataSource userLocalDataSource;
  final UserRemoteDataSource userRemoteDataSource;
  final ChannelLocalDataSource channelLocalDataSource;

  //region WebSocket Event Handling

  @override
  Future<void> handleRealtimeEvent(DataRouterCloudEvent event) async {
    switch (event.type) {
      case EventType.INCOMING_FRIEND_REQUEST_CREATED ||
            EventType.INCOMING_FRIEND_REQUEST_DELETED ||
            EventType.OUTGOING_FRIEND_REQUEST_CREATED ||
            EventType.OUTGOING_FRIEND_REQUEST_CANCELED:
        await _handleFriendRequestEvent(event);
        break;
      case EventType.INCOMING_FRIEND_REQUEST_ACCEPTED:
        _handleFriendRequestEvent(event);
        AppEventBus.publish(FriendRequestAcceptEvent());
        break;
      case EventType.OUTGOING_FRIEND_REQUEST_ACCEPTED:
        await _handleOutgoingFriendRequestAcceptedEvent(event);
        break;
      case EventType.OUTGOING_FRIEND_REQUEST_DELETED:
        await _handleOutgoingFriendRequestDeletedEvent(event);
        break;
      case EventType.INCOMING_FRIEND_REQUEST_CANCELED:
        AppEventBus.publish(FriendRequestCancelEvent());
        await _handleIncomingFriendRequestCanceledEvent(event);
        break;

      case EventType.FRIEND_UNFRIENDED:
        await _handleOutgoingUnfriendedEvent(event);
        break;

      default:
        logOperation('⚠️ Unhandled friend event type', event.type.toString());
    }
  }

  /// Handle OUTGOING_FRIEND_REQUEST_CREATED event
  Future<void> _handleFriendRequestEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      final eventData = FriendRequestEventData.fromJson(event.data);

      if (eventData.isValid != true) {
        return;
      }

      final sessionKey = Config.getInstance().activeSessionKey ?? '';
      Log.e(
        name: 'FriendDataOperation._handleFriendRequestEvent',
        eventData.friendRequest!.toJson(),
      );
      final newFriend = FriendSerializer.fromV3Friend(
        eventData.friendRequest!,
        sessionKey: sessionKey,
      );

      final users = eventData.includes!.users;
      final user = users?.firstWhere(
        (userInclude) => userInclude.userId != sessionKey,
      );

      if (newFriend != null) {
        await localSource.insert(newFriend);
        logOperation(
          '✅ Friend request created via WebSocket',
          newFriend.friendId,
        );
        if (event == EventType.INCOMING_FRIEND_REQUEST_ACCEPTED) {
          _updateDMStatus(user!.userId!);
        }
      }
    } catch (e) {
      logOperation(
        '❌ Error handling OUTGOING_FRIEND_REQUEST_CREATED event',
        '${event.id}: $e',
      );
    }
  }

  /// Update DM status for the user
  void _updateDMStatus(String userId) {
    final dmChannel = channelLocalDataSource.getDMChannel(recipientId: userId);
    if (dmChannel != null) {
      dmChannel.dmStatus = DirectMessageStatusEnum.contacted;
      channelLocalDataSource.insert(dmChannel);
    }
  }

  Future<void> _handleOutgoingFriendRequestAcceptedEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation(
        'Processing OUTGOING_FRIEND_REQUEST_ACCEPTED event',
        event.id,
      );

      final eventData = FriendRequestEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation(
          'Invalid OUTGOING_FRIEND_REQUEST_ACCEPTED event data',
          event.id,
        );
        return;
      }

      final sessionKey = Config.getInstance().activeSessionKey ?? '';

      final updatedFriend = FriendSerializer.fromV3Friend(
        eventData.friendRequest!,
        sessionKey: sessionKey,
      );

      final String? targetUserId = eventData.friendRequest!.requestedToUserId;
      final users = eventData.includes!.users;
      final user = users?.firstWhere(
        (userInclude) => userInclude.userId == targetUserId,
      );
      if (updatedFriend != null) {
        final existingFriend =
            await localSource.getFriendRequestById(targetUserId!);

        if (existingFriend != null) {
          final mergedFriend =
              FriendSerializer.merge(existingFriend, updatedFriend);
          await localSource.insert(mergedFriend);
          logOperation(
            '✅ Friend request accepted via WebSocket (merged)',
            updatedFriend.requestedToUserId!,
          );
        } else {
          await localSource.insert(updatedFriend);
          logOperation(
            '✅ New friend inserted via WebSocket',
            updatedFriend.friendId,
          );
        }
      }

      final localUser = await userLocalDataSource.get(targetUserId!);
      if (localUser != null) {
        userLocalDataSource.insert(UserSerializer.fromV3User(user!));
      } else {
        final remoteUser = await userRemoteDataSource.load(targetUserId);
        if (remoteUser != null) {
          userLocalDataSource.insert(remoteUser);
        }
      }
    } catch (e) {
      logOperation(
        '❌ Error handling OUTGOING_FRIEND_REQUEST_ACCEPTED event',
        '${event.id}: $e',
      );
    }
  }

  Future<void> _handleIncomingFriendRequestCanceledEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation(
        'Processing INCOMING_FRIEND_REQUEST_CANCELED event',
        event.id,
      );

      final eventData = FriendRequestEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation(
          'Invalid INCOMING_FRIEND_REQUEST_CANCELED event data',
          event.id,
        );
        return;
      }

      final sessionKey = Config.getInstance().activeSessionKey ?? '';

      final updatedFriend = FriendSerializer.fromV3Friend(
        eventData.friendRequest!,
        sessionKey: sessionKey,
      );

      if (updatedFriend != null) {
        final existingFriend = await localSource.get(updatedFriend.friendId);
        if (existingFriend != null) {
          final mergedFriend =
              FriendSerializer.merge(existingFriend, updatedFriend);
          await localSource.insert(mergedFriend);
          logOperation(
            '✅ Friend request updated (canceled)',
            updatedFriend.friendId,
          );
        } else {
          await localSource.insert(updatedFriend);
          logOperation(
            '✅ New friend inserted (canceled)',
            updatedFriend.friendId,
          );
        }

        if (updatedFriend.status == FriendStatusEnum.notFriend) {
          await localSource.delete(updatedFriend.friendId);
        }
      }
    } catch (e) {
      logOperation(
        '❌ Error handling INCOMING_FRIEND_REQUEST_CANCELED event',
        '${event.id}: $e',
      );
    }
  }

  /// Handle OUTGOING_Unfriend event

  Future<void> _handleOutgoingUnfriendedEvent(
    DataRouterCloudEvent event,
  ) async {
    logOperation(
      'Processing OUTGOING_Unfriend event',
      event.id,
    );

    final eventData = FriendRemovedEventData.fromJson(event.data);
    if (eventData.isValid != true) {
      logOperation(
        'Invalid OUTGOING_Unfriend event data',
        event.id,
      );
      return;
    }
    final actorId = eventData.actorId;
    final targetUserId = eventData.targetUserId;
    final sessionKey = Config.getInstance().activeSessionKey ?? '';

    final userId = actorId != sessionKey ? actorId! : targetUserId!;
    AppEventBus.publish(
      UnFriendEvent(userId: userId),
    );

    try {
      User? user = await userLocalDataSource.get(userId);
      if (user == null) {
        user = await userRemoteDataSource.load(userId);
      }
      user?.chatFriendDataRaw = jsonEncode(
        FriendSerializer.createOptimisticFriendRequest(
          sessionKey: sessionKey,
          requestedFromUserId: actorId!,
          requestedToUserId: targetUserId!,
          status: FriendStatusEnum.notFriend,
        ),
      );
      user?.updateTime = DateTime.now();

      if (user != null) {
        await userLocalDataSource.insert(user);
      }
      localSource.insert(
        FriendSerializer.createOptimisticFriendRequest(
          sessionKey: Config.getInstance().activeSessionKey ?? '',
          requestedFromUserId: actorId!,
          requestedToUserId: targetUserId!,
          status: FriendStatusEnum.notFriend,
        ),
      );
    } catch (e) {
      logOperation(
        '❌ Error handling OUTGOING_FRIEND_REQUEST_CANCELED event',
        '${event.id}: $e',
      );
    }
  }

  /// Handle OUTGOING_FRIEND_REQUEST_DELETED event
  Future<void> _handleOutgoingFriendRequestDeletedEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation(
        'Processing OUTGOING_FRIEND_REQUEST_DELETED event',
        event.id,
      );

      final eventData = FriendRequestEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation(
          'Invalid OUTGOING_FRIEND_REQUEST_DELETED event data',
          event.id,
        );
        return;
      }

      final friendRequest = eventData.friendRequest;
      if (friendRequest?.friendId != null) {
        await localSource.delete(friendRequest!.friendId!);
        logOperation(
          '✅ Friend request deleted via WebSocket',
          friendRequest.friendId!,
        );
      }
    } catch (e) {
      logOperation(
        '❌ Error handling OUTGOING_FRIEND_REQUEST_DELETED event',
        '${event.id}: $e',
      );
    }
  }

  //endregion

  //region Local Data Source Operations

  Stream<List<Friend>> watchAllFriend() {
    logOperation('Watching all items');
    return localSource.watchAll();
  }

  Stream<List<Friend>> watchFriendsRequest() {
    return localSource.watchFriendRequestsStream();
  }

  List<Friend> getListFriendRequest() {
    logOperation('Getting all friend requests');
    return localSource.getAllFriendRequests();
  }

  Resource<List<Friend>> getAllFriends() {
    try {
      logOperation('Getting all friends');
      return Resource.success(localSource.getActiveFriendsForSession());
    } catch (e) {
      logOperation('Error getting all friends', e.toString());
      return Resource.error('Error getting all friends: $e', e);
    }
  }

  Future<void> insertAll(List<Friend> friends) async {
    await localSource.insertAll(friends);
  }

  //endregion

  //region Remote Data Source Operations

  /// Add friend with optimistic update
  Future<Resource<Friend>> addFriend({
    required String userId,
  }) async {
    try {
      final sessionKey = Config.getInstance().activeSessionKey ?? '';

      logOperation('Adding friend', 'userId: $userId');

      // Step 1: Optimistic update - create temporary friend locally
      final tempFriend = FriendSerializer.createOptimisticFriendRequest(
        sessionKey: sessionKey,
        requestedFromUserId: sessionKey,
        requestedToUserId: userId,
        status: FriendStatusEnum.requestSent,
      );

      await localSource.insert(tempFriend);
      logOperation('✅ Optimistic friend request created', tempFriend.friendId);

      // Step 2: Make API call
      final apiResult = await remoteSource.addFriend(userId: userId);
      if (apiResult != null) {
        // Step 3: Replace optimistic update with real data
        await localSource.delete(tempFriend.friendId);
        await localSource.insert(apiResult);

        // Refresh current user data
        final currentUser = await userRemoteDataSource.load(userId);
        await userLocalDataSource.insert(currentUser!);

        logOperation(
          '✅ Friend request created successfully',
          apiResult.friendId,
        );
        return Resource.success(apiResult);
      } else {
        // Step 4: Rollback optimistic update on failure
        await localSource.delete(tempFriend.friendId);
        logOperation('❌ Failed to create friend request, rolled back', userId);
        return Resource.error('Failed to send friend request');
      }
    } catch (e) {
      logOperation('❌ Error adding friend', 'userId: $userId, error: $e');
      return Resource.error('Error sending friend request: $e', e);
    }
  }

  /// Accept friend request with optimistic update
  Future<Resource<Friend>> acceptFriendRequest({
    required String userId,
  }) async {
    try {
      logOperation('Accepting friend request', 'userId: $userId');
      final sessionKey = Config.getInstance().activeSessionKey ?? '';
      // Step 1: Find existing friend request
      final existingFriend = await localSource.getFriendByParticipants(
        sessionKey,
        sessionKey,
        userId,
      );

      if (existingFriend != null) {
        // Step 2: Optimistic update - update status locally
        final optimisticFriend = existingFriend.copyWith(
          status: FriendStatusEnum.friend,
          acceptTime: DateTime.now(),
          updateTime: DateTime.now(),
        );

        await localSource.insert(optimisticFriend);
        logOperation(
          '✅ Optimistic friend request accepted',
          userId,
        );

        // Step 3: Make API call
        final apiResult =
            await remoteSource.acceptFriendRequest(userId: userId);
        if (apiResult != null) {
          // Step 4: Replace optimistic update with real data
          await localSource.insert(apiResult);

          // Refresh current user data
          final currentUser = await userRemoteDataSource.load(userId);
          await userLocalDataSource.insert(currentUser!);

          // Update DM status
          _updateDMStatus(currentUser.userId);

          logOperation(
            '✅ Friend request accepted successfully',
            userId,
          );
          return Resource.success(apiResult);
        } else {
          // Step 5: Rollback optimistic update on failure
          await localSource.insert(existingFriend);
          logOperation(
            '❌ Failed to accept friend request, rolled back',
            userId,
          );
          return Resource.error('Failed to accept friend request');
        }
      } else {
        logOperation('❌ No friend request found to accept', userId);
        return Resource.error('No friend request found');
      }
    } catch (e) {
      logOperation(
        '❌ Error accepting friend request',
        'userId: $userId, error: $e',
      );
      return Resource.error('Error accepting friend request: $e', e);
    }
  }

  /// Cancel friend request with optimistic update
  Future<Resource<bool>> cancelFriendRequest({
    required String userId,
  }) async {
    try {
      final sessionKey = Config.getInstance().activeSessionKey ?? '';

      logOperation('Canceling friend request', 'userId: $userId');

      // Step 1: Find existing friend request
      final existingFriend = await localSource.getFriendByParticipants(
        sessionKey,
        sessionKey,
        userId,
      );

      if (existingFriend != null) {
        // Step 2: Optimistic update - remove friend request locally
        await localSource.delete(existingFriend.friendId);
        logOperation(
          '✅ Optimistic friend request canceled',
          existingFriend.friendId,
        );

        // Step 3: Make API call
        final success = await remoteSource.cancelFriendRequest(userId: userId);

        if (success) {
          // Refresh current user data
          final currentUser = await userRemoteDataSource.load(userId);
          await userLocalDataSource.insert(currentUser!);

          logOperation('✅ Friend request canceled successfully', userId);
          return Resource.success(true);
        } else {
          // Step 4: Rollback optimistic update on failure
          await localSource.insert(existingFriend);
          logOperation(
            '❌ Failed to cancel friend request, rolled back',
            userId,
          );
          return Resource.error('Failed to cancel friend request');
        }
      } else {
        logOperation('❌ No friend request found to cancel', userId);
        return Resource.error('No friend request found');
      }
    } catch (e) {
      logOperation(
        '❌ Error canceling friend request',
        'userId: $userId, error: $e',
      );
      return Resource.error('Error canceling friend request: $e', e);
    }
  }

  Future<Resource<bool>> unFriend({
    required String userId,
  }) async {
    try {
      final sessionKey = Config.getInstance().activeSessionKey ?? '';

      logOperation('UnFriend user', 'userId: $userId');

      // Step 1: Find existing friend request
      final existingFriend = await localSource.getFriendByParticipants(
        sessionKey,
        sessionKey,
        userId,
      );

      if (existingFriend != null) {
        // Step 2: Optimistic update - remove friend request locally
        await localSource.delete(existingFriend.friendId);
        logOperation(
          '✅ Optimistic Un friend',
          existingFriend.friendId,
        );

        // Step 3: Make API call
        final success = await remoteSource.unFriend(userId: userId);

        if (success) {
          // Refresh current user data
          final currentUser = await userRemoteDataSource.load(userId);
          await userLocalDataSource.insert(currentUser!);

          logOperation('✅ unFriend  successfully', userId);
          return Resource.success(true);
        } else {
          // Step 4: Rollback optimistic update on failure
          await localSource.insert(existingFriend);
          logOperation(
            '❌ Failed to unFriend, rolled back',
            userId,
          );
          return Resource.error('Failed to unFriend');
        }
      } else {
        logOperation('❌ No friend request found to unfriend', userId);
        return Resource.error('No friend request found to unfriend');
      }
    } catch (e) {
      logOperation(
        '❌ Error unFriend',
        'userId: $userId, error: $e',
      );
      return Resource.error('Error unFriend: $e', e);
    }
  }

  /// Sync all friends from remote
  Future<Resource<List<Friend>>> syncAllFriends() async {
    try {
      logOperation('Syncing all friends from remote');

      final friends = await remoteSource.loadAll();

      if (friends.isNotEmpty) {
        await localSource.insertAll(friends);
        logOperation(
          '✅ Synced friends successfully',
          '${friends.length} friends',
        );
        return Resource.success(friends);
      } else {
        logOperation('✅ No friends to sync');
        return Resource.success([]);
      }
    } catch (e) {
      logOperation('❌ Error syncing friends', e.toString());
      return Resource.error('Error syncing friends: $e', e);
    }
  }

  /// Sync incoming friend requests from remote
  Future<Resource<List<Friend>>> syncIncomingFriendRequests() async {
    try {
      logOperation('Syncing incoming friend requests from remote');

      final requests = await remoteSource.listIncomingFriendRequests();

      if (requests.isNotEmpty) {
        await localSource.insertAll(requests);
        logOperation(
          '✅ Synced incoming friend requests successfully',
          '${requests.length} requests',
        );
        return Resource.success(requests);
      } else {
        logOperation('✅ No incoming friend requests to sync');
        return Resource.success([]);
      }
    } catch (e) {
      logOperation('❌ Error syncing incoming friend requests', e.toString());
      return Resource.error('Error syncing incoming friend requests: $e', e);
    }
  }

  Future<Resource<bool>> deleteFriendRequest({
    required String userId,
  }) async {
    try {
      logOperation('Deleting friend request', 'userId: $userId');

      final success = await remoteSource.deleteFriendRequestById(userId);

      if (success) {
        logOperation('✅ Friend request deleted successfully', userId);
        return Resource.success(true);
      } else {
        // Step 4: Rollback optimistic update on failure

        return Resource.error('Failed to delete friend request');
      }
    } catch (e) {
      logOperation(
        '❌ Error deleting friend request',
        'userId: $userId, error: $e',
      );
      return Resource.error('Error deleting friend request: $e', e);
    }
  }

  Future<List<Friend>> getIncomingRequestsForSession(String sessionKey) async {
    try {
      logOperation('Getting incoming requests for session', sessionKey);
      final friends =
          await localSource.getIncomingRequestsForSession(sessionKey);
      logOperation(
        'Found incoming requests for session',
        '$sessionKey: ${friends.length} requests',
      );
      return friends;
    } catch (e) {
      logOperation(
        'Error getting incoming requests for session',
        '$sessionKey: $e',
      );
      return [];
    }
  }

  Future<void> insertAllFriend(List<Friend> friends) async {
    try {
      if (friends.isNotEmpty) {
        await localSource.insertAll(friends);
        logOperation(
          '✅ insert friends successfully',
          '${friends.length} friends',
        );
      } else {
        logOperation('✅ No friends to insert');
      }
    } catch (e) {
      logOperation('❌ Error insert friends', e.toString());
    }
  }

  /// Delete all friends by session key
  /// This is a Friend-specific operation for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting friends by session key', sessionKey);
      await localSource.deleteAllBySessionKey(sessionKey);
      logOperation(
        '✅ Successfully deleted all friends for session',
        sessionKey,
      );
    } catch (e) {
      logOperation(
        '❌ Error deleting friends by session key',
        '$sessionKey: $e',
      );
      rethrow;
    }
  }

  /// Load incoming friend requests with raw JSON response for migration compatibility
  /// This method returns the raw API response to maintain compatibility with existing use cases
  /// that need to parse the response manually
  Future<Map<String, dynamic>?> loadIncomingFriendRequestsRaw() async {
    try {
      logOperation('Loading incoming friend requests (raw response)');

      final rawResponse = await remoteSource.getRawIncomingFriendRequests();

      if (rawResponse != null) {
        logOperation(
          '✅ Successfully loaded incoming friend requests (raw)',
          'ok: ${rawResponse['ok']}, data available: ${rawResponse['data'] != null}',
        );
        return rawResponse;
      } else {
        logOperation(
          '❌ Failed to load incoming friend requests (raw)',
          'null response from service',
        );
        return null;
      }
    } catch (e) {
      logOperation(
        '❌ Error loading incoming friend requests (raw)',
        e.toString(),
      );
      return null;
    }
  }

//endregion
}
