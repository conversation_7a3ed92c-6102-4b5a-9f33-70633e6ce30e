//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_dm_call_notification_setting_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateDmCallNotificationSettingRequest {
  /// Returns a new [V3UpdateDmCallNotificationSettingRequest] instance.
  V3UpdateDmCallNotificationSettingRequest({
    this.status,
  });

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final bool? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateDmCallNotificationSettingRequest &&
          other.status == status;

  @override
  int get hashCode => status.hashCode;

  factory V3UpdateDmCallNotificationSettingRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateDmCallNotificationSettingRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateDmCallNotificationSettingRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
