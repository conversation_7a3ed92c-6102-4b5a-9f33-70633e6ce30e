//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel_destination_cloud_event.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_unread_messages_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserUnreadMessagesUpdatedEventData {
  /// Returns a new [V3UserUnreadMessagesUpdatedEventData] instance.
  V3UserUnreadMessagesUpdatedEventData({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.unreadCount,
    this.lastSeenMessageId,
    this.destination,
  });

  @JsonKey(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @J<PERSON><PERSON><PERSON>(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'unreadCount',
    required: false,
    includeIfNull: false,
  )
  final int? unreadCount;

  @JsonKey(
    name: r'lastSeenMessageId',
    required: false,
    includeIfNull: false,
  )
  final String? lastSeenMessageId;

  @JsonKey(
    name: r'destination',
    required: false,
    includeIfNull: false,
  )
  final V3ChannelDestinationCloudEvent? destination;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserUnreadMessagesUpdatedEventData &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.userId == userId &&
          other.unreadCount == unreadCount &&
          other.lastSeenMessageId == lastSeenMessageId &&
          other.destination == destination;

  @override
  int get hashCode =>
      workspaceId.hashCode +
      channelId.hashCode +
      userId.hashCode +
      unreadCount.hashCode +
      lastSeenMessageId.hashCode +
      destination.hashCode;

  factory V3UserUnreadMessagesUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserUnreadMessagesUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserUnreadMessagesUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
