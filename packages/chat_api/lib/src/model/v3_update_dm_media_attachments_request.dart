//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_media_object.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_dm_media_attachments_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateDmMediaAttachmentsRequest {
  /// Returns a new [V3UpdateDmMediaAttachmentsRequest] instance.
  V3UpdateDmMediaAttachmentsRequest({
    this.userId,
    this.messageId,
    this.mediaObjects,
    this.ref,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'messageId',
    required: false,
    includeIfNull: false,
  )
  final String? messageId;

  @Json<PERSON>ey(
    name: r'mediaObjects',
    required: false,
    includeIfNull: false,
  )
  final List<V3MediaObject>? mediaObjects;

  @Json<PERSON>ey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateDmMediaAttachmentsRequest &&
          other.userId == userId &&
          other.messageId == messageId &&
          other.mediaObjects == mediaObjects &&
          other.ref == ref;

  @override
  int get hashCode =>
      userId.hashCode +
      messageId.hashCode +
      mediaObjects.hashCode +
      ref.hashCode;

  factory V3UpdateDmMediaAttachmentsRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateDmMediaAttachmentsRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateDmMediaAttachmentsRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
