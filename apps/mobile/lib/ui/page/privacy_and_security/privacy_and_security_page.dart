import 'dart:io';

import 'package:app_core/core.dart';
import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import 'package:data_router/data_router.dart' as d;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/localization_client.dart';
import 'package:passkeys/authenticator.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class PrivacyAndSecurityPage extends StatefulWidget {
  const PrivacyAndSecurityPage({super.key, this.user});

  final User? user;

  @override
  _PrivacyAndSecurityPageState createState() => _PrivacyAndSecurityPageState();
}

class _PrivacyAndSecurityPageState extends State<PrivacyAndSecurityPage>
    with AutoRouteAwareStateMixin<PrivacyAndSecurityPage> {
  late final SecurityKeyBloc _bloc;
  bool _securityEnable = false;
  bool _isShowWarning = false;
  final _router = GetIt.I.get<AppRouter>();
  late AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();
  d.Session? session;

  @override
  void initState() {
    super.initState();
    _checkShowWarning();
    _bloc = GetIt.I.get<SecurityKeyBloc>();
    _checkSecurityEnabled();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    setState(() {});
    _checkShowWarning();
    _checkSecurityEnabled();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<SecurityKeyBloc>(
      create: (context) => _bloc,
      child: BlocListener<SecurityKeyBloc, SecurityKeyState>(
        listener: _blocListener,
        child: _buildPrivacySecurityPage(),
      ),
    );
  }

  void _blocListener(BuildContext context, SecurityKeyState state) {
    state.maybeWhen(
      viewSecurityKeySuccess: _onViewSuccess,
      viewSecurityKeyFailure: _onError,
      orElse: () {},
    );
  }

  Widget _buildPrivacySecurityPage() {
    return ui.PrivacySecurityPage(
      securityEnabled: _securityEnable,
      onClickBack: _onClickBack,
      onRequestAccountDeletionCode: _onRequestAccountDeletionCode,
      onClickViewAccountDeletionCode: _onClickViewAccountDeletionCode,
      isShowWarning: _isShowWarning,
    );
  }

  Future<void> _checkShowWarning() async {
    _securityEnable =
        widget.user?.setting?.security?.securityKey?.enable ?? false;
    final sessionOutput = await GetIt.instance<SessionLoginIsQRUseCase>()
        .execute(SessionLoginIsQRInput());
    session = sessionOutput.session;

    setState(() {
      _isShowWarning = (session?.isLoginQR ?? false) && _securityEnable;
    });
  }

  Future<void> _checkSecurityEnabled() async {
    try {
      final output = await GetIt.I.get<LoadMeUseCase>().execute(LoadMeInput());
      if (output.user?.setting?.security?.securityKey?.enable != null) {
        _securityEnable = output.user!.setting!.security!.securityKey!.enable!;
        _isShowWarning = (session?.isLoginQR ?? false) && _securityEnable;
      }
    } catch (e) {}

    setState(() {});
  }

  void _onViewSuccess(String securityKey) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _router.push(AccountDeletionCodeRoute(securityKey: securityKey));
    });
    _bloc.add(ResetSecurityKeyRequested());
  }

  void _onError(String? error) {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showFloatingSnackBar(
          context: context,
          content:
              GetIt.I.get<AppLocalizations>().anErrorOccurredPleaseTryAgain,
          snackBarType: ui.SnackBarType.danger,
        );
      },
    );

    _bloc.add(ResetSecurityKeyRequested());
  }

  Future<void> _showAuthenticateNoSupportDialog() async {
    ui.DialogUtils.showAuthenticationNotSupportDialog(
      context,
      onOpenSettings: _onOpenSettings,
      onCancel: _onCancel,
      isIos: Platform.isIOS,
      hasFaceId: await hasFaceId(),
    );
  }

  void _onClickBack() {
    _router.maybePop();
  }

  void _onRequestAccountDeletionCode(bool requested) {
    if (requested) {
      _router.push(AccountDeletionCodeRoute(securityKey: ''));
    }
  }

  Future<void> _onClickViewAccountDeletionCode() async {
    final passkeyMigrated =
        getIt<d.SessionDataOperation>().getActiveSession()!.passkeyMigrated;
    if (!(await canAuthenticate()) ||
        !(await canAuthenticateWithWebAPI(passkeyMigrated))) {
      await _showAuthenticateNoSupportDialog();
      return;
    }

    bool noConnect = getIt<NetworkManager>().noConnection();
    if (noConnect) {
      SnackBarOverlayHelper().showSnackBar(
        widgetBuilder: (T) {
          return ui.SnackBarUtilV2.showFloatingSnackBar(
            context: context,
            content: appLocalizations.noNetworkConnection,
            snackBarType: ui.SnackBarType.warning,
            // duration: Duration(seconds: 3),
          );
        },
      );

      return;
    }
    _bloc.add(const ViewSecurityKeyRequested());
  }

  void _onOpenSettings() {
    PasskeyAuthenticator().goToSettings();
  }

  void _onCancel() {
    _router.maybePop();
  }
}
