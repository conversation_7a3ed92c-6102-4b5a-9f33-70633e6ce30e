import 'package:data_router/data_router.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@Injectable()
class CheckMigratePasskeyStatusUseCase extends BaseFutureUseCase<
    CheckMigratePasskeyStatusInput, CheckMigratePasskeyStatusOutput> {
  CheckMigratePasskeyStatusUseCase(this._sessionDataOperation);

  final SessionDataOperation _sessionDataOperation;

  @override
  Future<CheckMigratePasskeyStatusOutput> buildUseCase(
    CheckMigratePasskeyStatusInput input,
  ) async {
    final response = await _sessionDataOperation.checkMigratePasskeyStatus();

    if (response.isError) {
      throw response.exception ??
          Exception('Failed to check migrate passkey status');
    }

    return CheckMigratePasskeyStatusOutput(ok: response.data ?? false);
  }
}

class CheckMigratePasskeyStatusInput extends BaseInput {
  CheckMigratePasskeyStatusInput();
}

class CheckMigratePasskeyStatusOutput extends BaseOutput {
  CheckMigratePasskeyStatusOutput({required this.ok});

  final bool ok;
}
