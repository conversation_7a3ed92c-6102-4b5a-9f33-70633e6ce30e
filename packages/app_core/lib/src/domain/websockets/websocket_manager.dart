import 'dart:async';
import 'dart:convert';

import 'package:data_router/data_router.dart' as data_router;
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../core.dart';
import '../../common/di/di.dart';
import 'websocket_listener.dart';

/// Enum to maintain internal state compatibility within the core module.
enum ConnectionStatus {
  disconnected,
  connecting,
  connected,
}

/// A refactored WebSocket manager that acts as a client to the `data_router` module.
/// It delegates connection, reconnection, and queueing logic to `data_router.WebSocketProvider`,
/// while preserving the application-level logic and the interface required by `WebSocketListener`.
@LazySingleton()
class WebSocketManager {
  static const String TAG = 'WebSocketManager';

  /// The robust WebSocket provider from the data_router module.
  final data_router.WebSocketProvider _provider;

  late WebSocketBridge _bridge;

  /// The listener responsible for handling business logic for incoming events.
  late final WebSocketListener _listener;

  /// Internal connection state, synchronized with the provider's state.
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;

  // Configuration properties that are passed down to the provider.
  String? _source;
  String? _deviceId;
  String? _myUserId;
  String? _me;

  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();
  late final AppLifecycleListener _appLifecycleListener;
  AppLifecycleState _lastAppLifecycleState = AppLifecycleState.resumed;

  StreamSubscription? _sendEventSubscription;
  StreamSubscription? _providerStatusSubscription;
  StreamSubscription? _networkStatusSubscription;

  /// Public stream for consumers within the core module to listen to connection status.
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  String? get source => _source;

  set source(String? value) {
    _source = value;
    _provider.setSource(value);
  }

  set deviceId(String? value) {
    _deviceId = value;
    _provider.setDeviceId(value);
  }

  set myUserId(String? value) {
    _myUserId = value;
    _provider.setMyUserId(value);
  }

  String? get me => _me;

  set me(String? value) {
    _me = value;
    _provider.setMe(value);
  }

  WebSocketManager(this._provider) {
    _bridge = WebSocketBridge(_provider);
    // Listen for events from AppEventBus to send via WebSocket.
    _sendEventSubscription =
        getIt<AppEventBus>().on<SendCloudEvent>().listen(_onReceivedCloudEvent);

    // Listen to app lifecycle to enrich presence messages.
    _appLifecycleListener = AppLifecycleListener(
      onStateChange: (newState) {
        _lastAppLifecycleState = newState;
      },
    );

    // Listen to the connection status from the data_router provider.
    _providerStatusSubscription =
        _provider.connectionStatusStream.listen(_handleProviderStatusChange);

    // Initialize the business-logic listener.
    _listener = WebSocketListener(this, _bridge);

    // Network monitoring is now handled by data_router's WebSocketReconnector
    // to avoid duplicate reconnection logic
  }

  void _onReceivedCloudEvent(SendCloudEvent event) {
    sendMessage(event.toJson());
  }

  /// Handles status changes from the underlying data_router provider.
  void _handleProviderStatusChange(bool isConnected) {
    Log.ws(
      name: '$TAG-$hashCode',
      "Status from data_router: ${isConnected ? 'Connected' : 'Disconnected'}",
    );

    final newStatus = isConnected
        ? ConnectionStatus.connected
        : ConnectionStatus.disconnected;

    if (_connectionStatus != newStatus) {
      _connectionStatus = newStatus;
      _connectionStatusController.add(isConnected);
      Log.ws(name: TAG, "Internal status updated to: $newStatus");
    }
  }

  /// Delegates the connection request to the data_router provider.
  Future<void> connect() async {
    if (_provider.isConnected || _provider.isConnecting) {
      Log.ws(
        name: TAG,
        "Connection attempt skipped: Already connected or connecting.",
      );
      return;
    }
    if (Config.getInstance().apiAuthToken.isEmpty) {
      Log.ws(name: TAG, "Connection attempt skipped: Auth token is empty.");
      return;
    }

    Log.ws(name: TAG, "Delegating connection request to data_router provider.");
    try {
      _connectionStatus = ConnectionStatus.connecting;
      _connectionStatusController.add(false); // Still not fully connected
      await _provider.connect();
    } catch (e) {
      _connectionStatus = ConnectionStatus.disconnected;
      Log.ws(name: TAG, "Connection attempt via provider failed: $e");
      // Optionally trigger diagnostics on failure.
      if (await _shouldRunDiagnostics()) {
        final diagnostics = await collectNetworkDiagnostics();
        logConnectionError(e as Exception, diagnostics);
      }
    }
  }

  /// Enriches a message with session data and delegates sending to the provider.
  /// The provider will automatically handle JSON encoding and offline queueing.
  void sendMessage(Map<String, dynamic> message) {
    final mutableMessage = Map<String, dynamic>.from(message);

    // This enrichment logic is preserved from the old manager.
    if (mutableMessage['source'] == null ||
        (mutableMessage['source'] as String).isEmpty) {
      mutableMessage['source'] = _source;
    }
    if (mutableMessage['type'] == EventType.PRESENCE_UPDATED.value) {
      mutableMessage['data']['isOnline'] =
          _lastAppLifecycleState == AppLifecycleState.resumed;
      mutableMessage['data']['deviceId'] = _deviceId;
      mutableMessage['data']['userId'] = _myUserId;
    }
    if (mutableMessage['type'] == EventType.PRIVATE_DATA_SYNC.value) {
      mutableMessage['data']['value']['source'] = _source;
    }

    Log.ws(name: '$TAG - Message sent via provider:', mutableMessage);
    _provider.sendMessage(mutableMessage);
  }

  /// Sends all pending messages stored in the local queue.
  void sendPendingMessages() {
    _provider.sendPendingMessages();
  }

  /// A callback for WebSocketListener to signal that the application-level
  /// handshake (`GATEWAY_CONNECTED`) is complete.
  void onConnected() {
    _connectionStatus = ConnectionStatus.connected;
    Log.ws(
      name: TAG,
      "onConnected callback from listener received. State is confirmed.",
    );
  }

  /// Delegates the disconnection request to the data_router provider.
  Future<void> disconnect() async {
    Log.ws(
      name: TAG,
      "Delegating disconnection request to data_router provider.",
    );
    await _provider.disconnect();
  }

  /// A more forceful disconnection. Delegates to the provider's disconnect.
  Future<void> forceClose() async {
    Log.ws(
      name: TAG,
      "Delegating force disconnection to data_router provider.",
    );
    await _provider.disconnect();
  }

  // Network monitoring logic removed to avoid duplication with data_router's WebSocketReconnector
  // The data_router now handles all network-aware reconnection logic

  bool get isConnected => _provider.isConnected;

  bool get isConnecting => _provider.isConnecting;

  bool get isDisconnected => _provider.isDisconnected;

  /// Cleans up all resources, subscriptions, and controllers.
  void dispose() {
    Log.ws(name: TAG, "Disposing...");
    disconnect();
    _connectionStatusController.close();
    _sendEventSubscription?.cancel();
    _providerStatusSubscription?.cancel();
    _networkStatusSubscription?.cancel();
    _appLifecycleListener.dispose();
    _listener.dispose();
  }

  // --- Application-Level Network Diagnostics Logic ---
  // This logic is preserved as it's specific to the application's
  // debugging and error-reporting requirements.

  DateTime? _lastDiagnosticsTime;
  static const Duration DIAGNOSTICS_MIN_INTERVAL = Duration(minutes: 5);

  Future<bool> _shouldRunDiagnostics() async {
    if (_lastDiagnosticsTime == null) return true;
    return DateTime.now().difference(_lastDiagnosticsTime!) >=
        DIAGNOSTICS_MIN_INTERVAL;
  }

  Future<Map<String, dynamic>> collectNetworkDiagnostics() async {
    _lastDiagnosticsTime = DateTime.now();
    // Implementation for diagnostics (ping, nslookup, etc.)
    // This can be copied from the old WebSocketManager.
    return {}; // Placeholder
  }

  void logConnectionError(Exception e, Map<String, dynamic> diagnostics) {
    diagnostics['provider_reconnect_interval'] =
        'Handled by data_router exponential backoff';

    FirebaseCrashlytics.instance.recordError(
      e,
      null,
      reason: 'WebSocket connection error reported by CoreWebSocketManager',
      information: [
        'Diagnostics: ${jsonEncode(diagnostics)}',
      ],
    );
    Log.ws(
      name: TAG,
      "Connection error logged to Crashlytics: ${e.toString()}",
    );
  }
}
