//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_revoke_messages_notification_pushed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RevokeMessagesNotificationPushedEventData {
  /// Returns a new [V3RevokeMessagesNotificationPushedEventData] instance.
  V3RevokeMessagesNotificationPushedEventData({
    this.messageIds,
  });

  @JsonKey(
    name: r'messageIds',
    required: false,
    includeIfNull: false,
  )
  final List<String>? messageIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RevokeMessagesNotificationPushedEventData &&
          other.messageIds == messageIds;

  @override
  int get hashCode => messageIds.hashCode;

  factory V3RevokeMessagesNotificationPushedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3RevokeMessagesNotificationPushedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3RevokeMessagesNotificationPushedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
