//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_ringback_tone_data.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_create_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneCreateResponse {
  /// Returns a new [V3RingbackToneCreateResponse] instance.
  V3RingbackToneCreateResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3RingbackToneData? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneCreateResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3RingbackToneCreateResponse.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneCreateResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneCreateResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
