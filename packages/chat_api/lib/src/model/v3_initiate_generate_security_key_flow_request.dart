//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_generate_security_key_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateGenerateSecurityKeyFlowRequest {
  /// Returns a new [V3InitiateGenerateSecurityKeyFlowRequest] instance.
  V3InitiateGenerateSecurityKeyFlowRequest({
    this.reqChallenge,
  });

  @JsonKey(
    name: r'reqChallenge',
    required: false,
    includeIfNull: false,
  )
  final String? reqChallenge;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateGenerateSecurityKeyFlowRequest &&
          other.reqChallenge == reqChallenge;

  @override
  int get hashCode => reqChallenge.hashCode;

  factory V3InitiateGenerateSecurityKeyFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateGenerateSecurityKeyFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateGenerateSecurityKeyFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
