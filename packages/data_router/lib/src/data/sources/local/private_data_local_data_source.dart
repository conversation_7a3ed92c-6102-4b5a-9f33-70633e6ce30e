import 'dart:async';

import 'package:injectable/injectable.dart';

import '../../../core/enums/entity_enum.dart';
import '../../../domain/repositories/private_data_repository.dart';
import '../../database/entities/call_log_private_data.dart';
import '../../database/entities/channel_private_data.dart';
import '../../database/entities/private_data.dart';
import '../../database/entities/user_private_data.dart';
import 'base_local_data_source.dart';

/// PrivateData-specific local data source implementation
/// Handles PrivateData entity operations with ObjectBox database
///
/// This follows the Single Responsibility Principle by handling only PrivateData entities
/// and provides specific query methods for private data-related operations.
@LazySingleton()
class PrivateDataLocalDataSource extends BaseLocalDataSource<PrivateData> {
  final PrivateDataRepository _privateDataRepository;

  PrivateDataLocalDataSource(this._privateDataRepository);

  @override
  EntityEnum get entity => EntityEnum.privateData;

  //region BaseLocalDataSource Implementation

  @override
  Future<PrivateData?> get(String id) async {
    try {
      logOperation('Getting private data');
      final privateData = _privateDataRepository.getPrivateData();

      if (privateData != null) {
        logOperation('Found private data in local database');
        return privateData;
      } else {
        logOperation('Private data not found in local database');
        return null;
      }
    } catch (e) {
      logOperation('Error getting private data', e.toString());
      throw Exception('Failed to get private data  ${e.toString()}');
    }
  }

  @override
  Future<void> insert(PrivateData privateData) async {
    try {
      _privateDataRepository.insertPrivateData(privateData);
    } catch (e) {
      throw Exception(
        'Failed to insert private data[${privateData.userId}]: $e',
      );
    }
  }

  @override
  Future<bool> delete(String id) async {
    try {
      logOperation('Deleting private data');
      final deleted = _privateDataRepository.deletePrivateData();
      if (deleted) {
        logOperation('Deleted private data successfully');
      } else {
        logOperation('Private data not found for deletion');
      }
      return deleted;
    } catch (e) {
      logOperation('Error deleting private data', e.toString());
      return false;
    }
  }

  @override
  Future<int> deleteAll() async {
    try {
      logOperation('Deleting all private data');
      final deletedCount = _privateDataRepository.deleteAllPrivateData();
      logOperation(
        'Deleted all private data successfully',
        '$deletedCount items',
      );
      return deletedCount;
    } catch (e) {
      logOperation('Error deleting all private data', e.toString());
      return 0;
    }
  }

  //endregion

  //region PrivateData-specific Operations

  @override
  Stream<PrivateData?> watch(String id) {
    try {
      logOperation('Watching private data');
      return _privateDataRepository.watchPrivateData();
    } catch (e) {
      logOperation('Error watching private data', e.toString());
      throw Exception('Failed to watch private data: $e');
    }
  }

  //endregion

  @override
  Future<void> insertAll(List<PrivateData> items) async {}

  @override
  Future<int> count() {
    throw UnimplementedError();
  }

  @override
  Future<bool> deleteAllBySessionKey(String sessionKey) async {
    return _privateDataRepository.deleteAllBySessionKey(sessionKey: sessionKey);
  }

  @override
  Future<bool> exists(String id) {
    throw UnimplementedError();
  }

  @override
  Stream<List<PrivateData>> watchAll() {
    throw UnimplementedError();
  }

  @override
  Future<List<PrivateData>> getAll() {
    // TODO: implement getAll
    throw UnimplementedError();
  }

  PrivateData? getPrivateData() {
    return _privateDataRepository.getPrivateData();
  }

  //region UserPrivateData Operations

  /// Get user private data by user ID directly from repository
  UserPrivateData? getUserPrivateData(String userId) {
    try {
      logOperation('Getting user private data', userId);
      return _privateDataRepository.getUserPrivateData(userId);
    } catch (e) {
      logOperation('Error getting user private data', e.toString());
      throw Exception('Failed to get user private data for user[$userId]: $e');
    }
  }

  /// Get user private data by list of user IDs directly from repository
  List<UserPrivateData> getPrivateDataByIds(List<String> listUserId) {
    try {
      logOperation(
        'Getting user private data by IDs',
        '${listUserId.length} users',
      );
      return _privateDataRepository.getPrivateDataByIds(listUserId);
    } catch (e) {
      logOperation('Error getting user private data by IDs', e.toString());
      throw Exception('Failed to get user private data by IDs: $e');
    }
  }

  /// Insert or update user private data directly through repository
  void insertUserPrivateData(UserPrivateData userPrivateData) {
    try {
      logOperation('Inserting user private data', userPrivateData.userIdField);
      _privateDataRepository.insertUserPrivateData(userPrivateData);
    } catch (e) {
      logOperation('Error inserting user private data', e.toString());
      throw Exception('Failed to insert user private data: $e');
    }
  }

  /// Watch user private data changes directly from repository
  Stream<UserPrivateData?> watchUserPrivateData(String userId) {
    try {
      logOperation('Watching user private data', userId);
      return _privateDataRepository.watchUserPrivateData(userId);
    } catch (e) {
      logOperation('Error watching user private data', e.toString());
      throw Exception(
        'Failed to watch user private data for user[$userId]: $e',
      );
    }
  }

  /// Watch all user private data changes directly from repository
  Stream<List<UserPrivateData>> watchAllUserPrivateData() {
    try {
      logOperation('Watching all user private data');
      return _privateDataRepository.watchAllUserPrivateData();
    } catch (e) {
      logOperation('Error watching all user private data', e.toString());
      throw Exception('Failed to watch all user private data: $e');
    }
  }

  /// Get all user private data directly from repository
  List<UserPrivateData> getAllUserPrivateData() {
    try {
      logOperation('Getting all user private data');
      return _privateDataRepository.getAllUserPrivateData();
    } catch (e) {
      logOperation('Error getting all user private data', e.toString());
      throw Exception('Failed to get all user private data: $e');
    }
  }

  /// Delete user private data directly through repository
  bool deleteUserPrivateData(String userId) {
    try {
      logOperation('Deleting user private data', userId);
      return _privateDataRepository.deleteUserPrivateData(userId);
    } catch (e) {
      logOperation('Error deleting user private data', e.toString());
      throw Exception(
        'Failed to delete user private data for user[$userId]: $e',
      );
    }
  }

  //endregion

  //region ChannelPrivateData Operations

  /// Get channel private data by channel ID directly from repository
  ChannelPrivateData? getChannelPrivateData(String channelId) {
    try {
      logOperation('Getting channel private data', channelId);
      return _privateDataRepository.getChannelPrivateData(channelId);
    } catch (e) {
      logOperation('Error getting channel private data', e.toString());
      throw Exception(
        'Failed to get channel private data for channel[$channelId]: $e',
      );
    }
  }

  /// Insert or update channel private data directly through repository
  void insertChannelPrivateData(ChannelPrivateData channelPrivateData) {
    try {
      logOperation(
        'Inserting channel private data',
        channelPrivateData.channelId,
      );
      _privateDataRepository.updateChannel(channelPrivateData);
    } catch (e) {
      logOperation('Error inserting channel private data', e.toString());
      throw Exception('Failed to insert channel private data: $e');
    }
  }

  /// Watch channel private data changes directly from repository
  Stream<ChannelPrivateData?> watchChannelPrivateData(String channelId) {
    try {
      logOperation('Watching channel private data', channelId);
      return _privateDataRepository.watchChannelPrivateData(channelId);
    } catch (e) {
      logOperation('Error watching channel private data', e.toString());
      throw Exception(
        'Failed to watch channel private data for channel[$channelId]: $e',
      );
    }
  }

  /// Watch all channel private data changes directly from repository
  Stream<List<ChannelPrivateData>> watchAllChannelPrivateData() {
    try {
      logOperation('Watching all channel private data');
      return _privateDataRepository.watchAllChannelPrivateData();
    } catch (e) {
      logOperation('Error watching all channel private data', e.toString());
      throw Exception('Failed to watch all channel private data: $e');
    }
  }

  /// Get all channel private data directly from repository
  List<ChannelPrivateData> getAllChannelPrivateData() {
    try {
      logOperation('Getting all channel private data');
      return _privateDataRepository.getAllChannelPrivateData();
    } catch (e) {
      logOperation('Error getting all channel private data', e.toString());
      throw Exception('Failed to get all channel private data: $e');
    }
  }

  /// Get maximum sort value for pinned channels directly from repository
  int? getMaxSortChannel() {
    try {
      logOperation('Getting max sort channel');
      return _privateDataRepository.getMaxSortChannel();
    } catch (e) {
      logOperation('Error getting max sort channel', e.toString());
      throw Exception('Failed to get max sort channel: $e');
    }
  }

  /// Delete channel private data directly through repository
  bool deleteChannelPrivateData(String channelId) {
    try {
      logOperation('Deleting channel private data', channelId);
      return _privateDataRepository.deleteChannelPrivateData(channelId);
    } catch (e) {
      logOperation('Error deleting channel private data', e.toString());
      throw Exception(
        'Failed to delete channel private data for channel[$channelId]: $e',
      );
    }
  }

  //endregion

  //region CallLogPrivateData Operations

  /// Get call log private data by call ID directly from repository
  CallLogPrivateData? getCallLogPrivateData(String callId) {
    try {
      logOperation('Getting call log private data', callId);
      return _privateDataRepository.getCallLogPrivateData(callId);
    } catch (e) {
      logOperation('Error getting call log private data', e.toString());
      throw Exception(
        'Failed to get call log private data for call[$callId]: $e',
      );
    }
  }

  /// Watch call log private data changes directly from repository
  Stream<CallLogPrivateData?> watchCallLogPrivateData(String callId) {
    try {
      logOperation('Watching call log private data', callId);
      return _privateDataRepository.watchCallLogPrivateData(callId);
    } catch (e) {
      logOperation('Error watching call log private data', e.toString());
      throw Exception(
        'Failed to watch call log private data for call[$callId]: $e',
      );
    }
  }

  /// Watch all call log private data changes directly from repository
  Stream<List<CallLogPrivateData>> watchAllCallLogPrivateData() {
    try {
      logOperation('Watching all call log private data');
      return _privateDataRepository.watchAllCallLogPrivateData();
    } catch (e) {
      logOperation('Error watching all call log private data', e.toString());
      throw Exception('Failed to watch all call log private data: $e');
    }
  }

  /// Get all call log private data directly from repository
  List<CallLogPrivateData> getAllCallLogPrivateData() {
    try {
      logOperation('Getting all call log private data');
      return _privateDataRepository.getAllCallLogPrivateData();
    } catch (e) {
      logOperation('Error getting all call log private data', e.toString());
      throw Exception('Failed to get all call log private data: $e');
    }
  }

  /// Delete call log private data directly through repository
  bool deleteCallLogPrivateData(String callId) {
    try {
      logOperation('Deleting call log private data', callId);
      return _privateDataRepository.deleteCallLogPrivateData(callId);
    } catch (e) {
      logOperation('Error deleting call log private data', e.toString());
      throw Exception(
        'Failed to delete call log private data for call[$callId]: $e',
      );
    }
  }

//endregion

//endregion
}
