//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_assertion_result.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_confirm_recovery_code_generation_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConfirmRecoveryCodeGenerationFlowRequest {
  /// Returns a new [V3ConfirmRecoveryCodeGenerationFlowRequest] instance.
  V3ConfirmRecoveryCodeGenerationFlowRequest({
    this.reqId,
    this.reqVerifier,
    this.assertion,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'reqVerifier',
    required: false,
    includeIfNull: false,
  )
  final String? reqVerifier;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'assertion',
    required: false,
    includeIfNull: false,
  )
  final CommonAssertionResult? assertion;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ConfirmRecoveryCodeGenerationFlowRequest &&
          other.reqId == reqId &&
          other.reqVerifier == reqVerifier &&
          other.assertion == assertion;

  @override
  int get hashCode =>
      reqId.hashCode + reqVerifier.hashCode + assertion.hashCode;

  factory V3ConfirmRecoveryCodeGenerationFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3ConfirmRecoveryCodeGenerationFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ConfirmRecoveryCodeGenerationFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
