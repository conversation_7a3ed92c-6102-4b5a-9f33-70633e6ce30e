import 'dart:async';

/// Public interface for WebSocket provider
/// Provides abstraction for WebSocket operations that can be consumed by external packages
///
/// This interface follows the Dependency Inversion Principle by allowing
/// external consumers (like app_core) to depend on abstractions rather than concrete implementations
abstract class WebSocketProvider {
  /// Raw message stream - compatible with IOWebSocketChannel.stream
  /// This stream emits the exact same format as IOWebSocketChannel for backward compatibility
  Stream<dynamic> get rawMessageStream;

  /// Connection status stream
  /// Emits true when connected, false when disconnected
  Stream<bool> get connectionStatusStream;

  /// Current connection state
  bool get isConnected;
  bool get isConnecting;
  bool get isDisconnected;

  /// Connection management
  Future<void> connect();
  Future<void> disconnect();

  /// Message sending (for backward compatibility with existing WebSocketListener)
  void sendMessage(Map<String, dynamic> message);
  void sendBinaryMessage(List<int> data);
  void sendPendingMessages();

  /// Configuration methods (migrated from WebSocketManager)
  void setSource(String? source);
  void setDeviceId(String? deviceId);
  void setMyUserId(String? userId);
  void setMe(String? me);

  /// Get current configuration
  String? get source;
  String? get deviceId;
  String? get myUserId;
  String? get me;

  /// Dispose resources and close connections
  Future<void> dispose();
}
