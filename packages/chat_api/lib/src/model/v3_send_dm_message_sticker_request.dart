//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_send_dm_message_sticker_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3SendDMMessageStickerRequest {
  /// Returns a new [V3SendDMMessageStickerRequest] instance.
  V3SendDMMessageStickerRequest({
    this.userId,
    this.ref,
    this.stickerId,
    this.fileRef,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'ref',
    required: false,
    includeIfNull: false,
  )
  final String? ref;

  @JsonKey(
    name: r'stickerId',
    required: false,
    includeIfNull: false,
  )
  final String? stickerId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'fileRef',
    required: false,
    includeIfNull: false,
  )
  final String? fileRef;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3SendDMMessageStickerRequest &&
          other.userId == userId &&
          other.ref == ref &&
          other.stickerId == stickerId &&
          other.fileRef == fileRef;

  @override
  int get hashCode =>
      userId.hashCode + ref.hashCode + stickerId.hashCode + fileRef.hashCode;

  factory V3SendDMMessageStickerRequest.fromJson(Map<String, dynamic> json) =>
      _$V3SendDMMessageStickerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3SendDMMessageStickerRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
