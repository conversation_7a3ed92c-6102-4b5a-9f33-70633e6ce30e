//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_accept_message_request_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3AcceptMessageRequestRequest {
  /// Returns a new [V3AcceptMessageRequestRequest] instance.
  V3AcceptMessageRequestRequest({
    this.userId,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3AcceptMessageRequestRequest && other.userId == userId;

  @override
  int get hashCode => userId.hashCode;

  factory V3AcceptMessageRequestRequest.fromJson(Map<String, dynamic> json) =>
      _$V3AcceptMessageRequestRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3AcceptMessageRequestRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
