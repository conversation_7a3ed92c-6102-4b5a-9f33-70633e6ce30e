//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_suggestions.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_suggested_friends_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListSuggestedFriendsResponse {
  /// Returns a new [V3ListSuggestedFriendsResponse] instance.
  V3ListSuggestedFriendsResponse({
    this.ok,
    this.error,
    this.suggestions,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON>ey(
    name: r'suggestions',
    required: false,
    includeIfNull: false,
  )
  final V3Suggestions? suggestions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListSuggestedFriendsResponse &&
          other.ok == ok &&
          other.error == error &&
          other.suggestions == suggestions;

  @override
  int get hashCode => ok.hashCode + error.hashCode + suggestions.hashCode;

  factory V3ListSuggestedFriendsResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListSuggestedFriendsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListSuggestedFriendsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
