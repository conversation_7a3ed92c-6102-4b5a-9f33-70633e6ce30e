//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_session_expiration_setting_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_setting_session_expiration_setting.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class UserSettingSessionExpirationSetting {
  /// Returns a new [UserSettingSessionExpirationSetting] instance.
  UserSettingSessionExpirationSetting({
    this.value,
  });

  @JsonKey(
    name: r'value',
    required: false,
    includeIfNull: false,
  )
  final V3SessionExpirationSettingEnum? value;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserSettingSessionExpirationSetting && other.value == value;

  @override
  int get hashCode => value.hashCode;

  factory UserSettingSessionExpirationSetting.fromJson(
          Map<String, dynamic> json) =>
      _$UserSettingSessionExpirationSettingFromJson(json);

  Map<String, dynamic> toJson() =>
      _$UserSettingSessionExpirationSettingToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
