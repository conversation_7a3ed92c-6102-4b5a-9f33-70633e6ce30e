import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:localization_client/localization_client.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../common/di/di.dart';
import '../../../../domain/event_bus/full_screen/show_message_options_event.dart';
import '../../../../domain/usecase/chat_user/get_me_use_case.dart';
import '../../../message_list/reaction_handler.dart';

abstract class BaseFullscreenWidget extends StatelessWidget
    implements ViewFullScreenPartialInterface {
  final MessageItem messageItem;
  final Message message;
  late final ReactionHandler _reactionHandler;
  final AppLocalizations appLocalizations = getIt<AppLocalizations>();

  BaseFullscreenWidget({
    required this.messageItem,
    required this.message,
    super.key,
  }) {
    _reactionHandler = ReactionHandler.getInstance(
      channelId: messageItem.channelId,
      workspaceId: messageItem.workspaceId,
      userId: messageItem.recipientId,
    );
  }

  @override
  void onBackButtonClicked() {
    AppEventBus.publish(PopToChannelViewEvent());
  }

  @override
  void onListReactionClicked(MessageItem messageItem) {
    _reactionHandler.showListReactions(message, messageItem);
  }

  @override
  void onMoreClicked(MessageItem messageItem) {
    AppEventBus.publish(
      ShowMessageOptionsEvent(
        message: message,
        messageItem: messageItem,
      ),
    );
  }

  @override
  void onReactionClicked(MessageItem messageItem) {
    _reactionHandler.quickReaction(message: message, messageItem: messageItem);
  }

  @override
  void onForwardClicked(MessageItem messageItem) {
    AppEventBus.publish(CallForwardEvent(messageId: messageItem.messageId));
  }

  @override
  void onTranslateButtonClicked() {}

  void onUsernameClicked(BuildContext context, String mention) async {
    final outputMe = await getIt.get<GetMeUseCase>().execute(GetMeInput());

    final currentUser = outputMe.user;
    if (currentUser == null) return;

    final mentionNormalized = mention.replaceAll('@', '').trim();
    if (mentionNormalized == currentUser.username ||
        mentionNormalized == appLocalizations.all) {
      return;
    }

    LoadingOverlayHelper.showLoading(context);

    final outputUser = await getIt
        .get<GetChatUserByUsernameUseCase>()
        .execute(GetChatUserByUsernameInput(userName: mentionNormalized));
    final mentionedUser = outputUser.user;
    LoadingOverlayHelper.hideLoading(context);

    if (mentionedUser == null) {
      DialogUtils.showAccountUnavailableDialog(
        context,
        onFirstAction: (dialogContext) => dialogContext.maybePop(),
      );
      return;
    }

    AppEventBus.publish(OnGoToUserProfileEvent(userId: mentionedUser.userId));
  }
}
