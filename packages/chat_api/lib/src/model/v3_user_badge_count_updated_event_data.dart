//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_badge_count_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserBadgeCountUpdatedEventData {
  /// Returns a new [V3UserBadgeCountUpdatedEventData] instance.
  V3UserBadgeCountUpdatedEventData({
    this.userId,
    this.badgeCount,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'badgeCount',
    required: false,
    includeIfNull: false,
  )
  final int? badgeCount;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserBadgeCountUpdatedEventData &&
          other.userId == userId &&
          other.badgeCount == badgeCount;

  @override
  int get hashCode => userId.hashCode + badgeCount.hashCode;

  factory V3UserBadgeCountUpdatedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3UserBadgeCountUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UserBadgeCountUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
