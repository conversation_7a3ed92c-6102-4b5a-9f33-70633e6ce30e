import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:web_socket_channel/io.dart';

import '../../data/models/websocket_connection_status.dart';
import '../../data/sources/ws/includes_websocket_handler.dart';
import '../logging/data_router_logger.dart';
import 'websocket_config.dart';
import 'websocket_connector.dart';
import 'websocket_provider.dart';
import 'websocket_queue.dart';
import 'websocket_reconnector.dart';

/// Core WebSocket manager for data_router package
/// Migrated from app_core.WebSocketManager with enhanced architecture
///
/// This implementation provides:
/// - Connection lifecycle management
/// - Automatic reconnection with exponential backoff
/// - Message queuing for offline scenarios
/// - Raw message streaming for backward compatibility
/// - Entity-specific event processing
@LazySingleton(as: WebSocketProvider)
class DataRouterWebSocketManager implements WebSocketProvider {
  final IncludesWebSocketHandler _includesWebSocketHandler;
  final WebSocketConnector _connector;
  final WebSocketReconnector _reconnector;
  final WebSocketQueue _queue;
  final DataRouterLogger _logger;

  // Connection state
  IOWebSocketChannel? _channel;
  WebSocketConnectionStatus _connectionStatus =
      WebSocketConnectionStatus.disconnected;

  // Configuration
  String? _source;
  String? _deviceId;
  String? _myUserId;
  String? _me;

  // Streams
  final StreamController<dynamic> _rawMessageController =
      StreamController<dynamic>.broadcast();
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  // App lifecycle
  late final AppLifecycleListener _appLifecycleListener;
  AppLifecycleState _lastAppLifecycleState = AppLifecycleState.resumed;

  // Subscriptions
  StreamSubscription? _channelSubscription;
  bool _disposed = false;

  Completer<void>? _connectionCompleter;

  bool _isReconnectorSetup = false;

  DataRouterWebSocketManager(
    this._connector,
    this._reconnector,
    this._queue,
    this._includesWebSocketHandler,
    this._logger,
  ) {
    _initializeAppLifecycleListener();
  }

  @override
  Stream<dynamic> get rawMessageStream => _rawMessageController.stream;

  @override
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  @override
  bool get isConnected =>
      _connectionStatus == WebSocketConnectionStatus.connected;

  @override
  bool get isConnecting =>
      _connectionStatus == WebSocketConnectionStatus.connecting;

  @override
  bool get isDisconnected =>
      _connectionStatus == WebSocketConnectionStatus.disconnected;

  @override
  String? get source => _source;

  @override
  String? get deviceId => _deviceId;

  @override
  String? get myUserId => _myUserId;

  @override
  String? get me => _me;

  @override
  Future<void> connect() async {
    if (!_isReconnectorSetup) {
      _setupReconnector();
      _isReconnectorSetup = true;
      _logger.webSocketInfo(
        'Reconnector has been set up on the first connect call.',
        methodName: 'connect',
      );
    }

    if (isConnected || _disposed) return;
    if (_connectionCompleter != null) {
      _logger.webSocketInfo(
        'Connection attempt already in progress. Awaiting result.',
        methodName: 'connect',
      );
      return _connectionCompleter!.future;
    }

    _logger.webSocketInfo(
      'Initiating WebSocket connection +${DateTime.now().toIso8601String()}',
      methodName: 'connect',
    );

    _connectionCompleter = Completer<void>();

    try {
      _setConnectionStatus(WebSocketConnectionStatus.connecting);

      final connectionResult = await _connector.connect();

      if (_disposed) return;

      if (connectionResult.isSuccess && connectionResult.channel != null) {
        _channel = connectionResult.channel;
        _setupChannelListeners();
        _setConnectionStatus(WebSocketConnectionStatus.connected);

        _reconnector.reset();

        _logger.webSocketInfo(
          'WebSocket connection established successfully',
          methodName: 'connect',
        );
      } else {
        _handleConnectionFailure(connectionResult.error);
      }
    } catch (error) {
      if (!_disposed) {
        _handleConnectionFailure(error);
      }
    } finally {
      if (_connectionCompleter != null && !_connectionCompleter!.isCompleted) {
        _connectionCompleter!.complete();
      }
      _connectionCompleter = null;
    }
  }

  @override
  Future<void> disconnect() async {
    if (_disposed) return;

    _logger.webSocketInfo(
      'Disconnecting WebSocket',
      methodName: 'disconnect',
    );

    if (_connectionCompleter != null && !_connectionCompleter!.isCompleted) {
      _connectionCompleter!
          .completeError(Exception('Connection cancelled by disconnect'));
      _connectionCompleter = null;
    }

    _setConnectionStatus(WebSocketConnectionStatus.disconnected);

    await _channelSubscription?.cancel();
    _channelSubscription = null;

    await _channel?.sink.close();
    _channel = null;

    _reconnector.stop();
  }

  @override
  void sendMessage(Map<String, dynamic> message) {
    if (_disposed) return;

    if (isDisconnected || isConnecting) {
      _queue.enqueueMessage(message);
      _logger.webSocketDebug(
        'Message queued for later sending',
        methodName: 'sendMessage',
      );
      return;
    }

    try {
      _sendMessageNow(message);
    } catch (error) {
      _logger.webSocketError(
        'Failed to send message: $error',
        methodName: 'sendMessage',
      );

      // Queue the message for retry
      _queue.enqueueMessage(message);

      // Trigger reconnection if send fails
      _scheduleReconnect(error);
    }
  }

  @override
  void sendBinaryMessage(List<int> data) {
    if (_disposed || isDisconnected) return;

    _channel?.sink.add(data);
    _logger.webSocketDebug(
      'Binary message sent',
      methodName: 'sendBinaryMessage',
    );
  }

  @override
  void setSource(String? source) {
    _source = source;
  }

  @override
  void setDeviceId(String? deviceId) {
    _deviceId = deviceId;
  }

  @override
  void setMyUserId(String? userId) {
    _myUserId = userId;
  }

  @override
  void setMe(String? me) {
    _me = me;
  }

  void _initializeAppLifecycleListener() {
    _appLifecycleListener = AppLifecycleListener(
      onStateChange: (newState) {
        _lastAppLifecycleState = newState;
        _logger.webSocketDebug(
          'App lifecycle state changed to: $newState',
          methodName: '_initializeAppLifecycleListener',
        );
      },
    );
  }

  void _setupReconnector() {
    _reconnector.onReconnectAttempt = () async {
      _logger.webSocketInfo(
        'Attempting to reconnect WebSocket',
        methodName: '_setupReconnector',
      );
      debugPrint('DataRouterWebSocketManager._setupReconnector:');
      await connect();
    };

    _reconnector.onHealthCheck = () => _performHealthCheck();
  }

  Future<void> _setupChannelListeners() async {
    if (_channel == null) return;
    await _channelSubscription?.cancel();
    _channelSubscription = _channel!.stream.listen(
      _handleRawMessage,
      onError: _handleChannelError,
      onDone: _handleChannelDone,
    );
  }

  void _handleRawMessage(dynamic message) {
    Log.e(name: 'DataRouterWebSocketManager._handleRawMessage', message);
    if (_disposed) return;

    _rawMessageController.add(message);

    if (WebSocketConfig.enableDebugLogging) {
      _logger.webSocketDebug(
        'Raw message received: ${message.toString().substring(0, 100)}...',
        methodName: '_handleRawMessage',
      );
    }

    _processEntityMessage(message);
  }

  void _processEntityMessage(dynamic message) {
    try {
      Map<String, dynamic>? parsedMessage;

      if (message is String) {
        try {
          parsedMessage = jsonDecode(message) as Map<String, dynamic>?;
        } catch (e) {
          _logger.webSocketDebug(
            'Failed to parse WebSocket message as JSON: $e',
            methodName: '_processEntityMessage',
          );
          return;
        }
      } else if (message is Map<String, dynamic>) {
        parsedMessage = message;
      }

      if (parsedMessage == null) {
        _logger.webSocketDebug(
          'WebSocket message is not a valid format',
          methodName: '_processEntityMessage',
        );
        return;
      }

      try {
        final dataPayload = parsedMessage['data'];
        if (dataPayload is! Map<String, dynamic>) {
          _logger.webSocketDebug(
            'WebSocket message `data` field is not a valid object.',
            methodName: '_processEntityMessage',
          );
          return;
        }

        if (_includesWebSocketHandler.hasIncludesData(dataPayload)) {
          _includesWebSocketHandler.processWebSocketIncludes(dataPayload);
        }
      } catch (e) {
        _logger.webSocketDebug(
          'IncludesWebSocketHandler not available: $e',
          methodName: '_processEntityMessage',
        );
      }
    } catch (e) {
      _logger.webSocketError(
        'Error processing entity message: $e',
        methodName: '_processEntityMessage',
      );
    }
  }

  void _handleChannelError(dynamic error) {
    _logger.webSocketError(
      'WebSocket channel error: $error',
      methodName: '_handleChannelError',
    );

    _setConnectionStatus(WebSocketConnectionStatus.disconnected);
    _scheduleReconnect(error);
  }

  void _handleChannelDone() {
    _logger.webSocketInfo(
      'WebSocket channel closed by server',
      methodName: '_handleChannelDone',
    );

    _setConnectionStatus(WebSocketConnectionStatus.disconnected);
    _scheduleReconnect('Channel closed unexpectedly');
  }

  void _handleConnectionFailure(dynamic error) {
    _logger.webSocketError(
      'WebSocket connection failed: $error',
      methodName: '_handleConnectionFailure',
    );

    _setConnectionStatus(WebSocketConnectionStatus.disconnected);
    _scheduleReconnect();
  }

  void _scheduleReconnect([dynamic error]) {
    // Không lên lịch nếu đang kết nối hoặc đã kết nối
    if (_disposed || isConnected || _connectionCompleter != null) return;

    // Classify error type for better reconnection strategy
    ConnectionFailureType? failureType;
    if (error != null) {
      failureType = _classifyConnectionError(error);
    }

    _reconnector.scheduleReconnect(failureType);
  }

  /// Classify connection errors for better handling
  ConnectionFailureType _classifyConnectionError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (error is TimeoutException) {
      return ConnectionFailureType.timeout;
    }

    if (errorString.contains('network') || errorString.contains('connection')) {
      return ConnectionFailureType.network;
    }

    if (errorString.contains('auth') || errorString.contains('unauthorized')) {
      return ConnectionFailureType.authentication;
    }

    if (errorString.contains('server') ||
        errorString.contains('500') ||
        errorString.contains('503')) {
      return ConnectionFailureType.server;
    }

    return ConnectionFailureType.unknown;
  }

  /// Perform health check for reconnection monitoring
  Future<bool> _performHealthCheck() async {
    try {
      _logger.webSocketDebug(
        'Performing health check',
        methodName: '_performHealthCheck',
      );

      // Simple ping-pong health check
      final pingMessage = {
        'type': 'ping',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      final completer = Completer<bool>();
      Timer? timeoutTimer;

      // Set up timeout
      timeoutTimer = Timer(WebSocketConfig.healthCheckTimeout, () {
        if (!completer.isCompleted) {
          completer.complete(false);
        }
      });

      // Listen for pong response
      final subscription = rawMessageStream.listen((message) {
        try {
          final decoded = jsonDecode(message);
          if (decoded['type'] == 'pong') {
            timeoutTimer?.cancel();
            if (!completer.isCompleted) {
              completer.complete(true);
            }
          }
        } catch (e) {
          // Ignore decode errors for health check
        }
      });

      // Send ping
      sendMessage(pingMessage);

      final result = await completer.future;
      subscription.cancel();
      timeoutTimer.cancel();

      return result;
    } catch (error) {
      _logger.webSocketDebug(
        'Health check failed: $error',
        methodName: '_performHealthCheck',
      );
      return false;
    }
  }

  void _setConnectionStatus(WebSocketConnectionStatus status) {
    if (_connectionStatus == status) return;

    _connectionStatus = status;
    _connectionStatusController.add(isConnected);

    _logger.webSocketInfo(
      'Connection status changed to: $status',
      methodName: '_setConnectionStatus',
    );
  }

  void _sendMessageNow(Map<String, dynamic> message) {
    if (_channel == null || _disposed) return;

    final mutableMessage = Map<String, dynamic>.from(message);

    if (_source != null &&
        (mutableMessage['source'] == null || mutableMessage['source'] == '')) {
      mutableMessage['source'] = _source;
    }

    if (mutableMessage['type'] == EventType.PRESENCE_UPDATED.value) {
      mutableMessage['data'] ??= {};
      mutableMessage['data']['isOnline'] =
          _lastAppLifecycleState == AppLifecycleState.resumed;
      mutableMessage['data']['deviceId'] = _deviceId;
      mutableMessage['data']['userId'] = _myUserId;
    }

    final encodedMessage = jsonEncode(mutableMessage);
    _channel!.sink.add(encodedMessage);

    if (kDebugMode)
      _logger.webSocketDebug(
        'Message sent: ${jsonEncode(mutableMessage)})}',
        methodName: '_sendMessageNow',
      );
  }

  @override
  Future<void> sendPendingMessages() async {
    final pendingMessages = await _queue.getAllPendingMessages();

    if (pendingMessages.isNotEmpty) {
      _logger.webSocketInfo(
        'Sending ${pendingMessages.length} pending messages',
        methodName: '_sendPendingMessages',
      );

      for (final message in pendingMessages) {
        _sendMessageNow(message);
      }

      await _queue.clearAllPendingMessages();
    }
  }

  @override
  Future<void> dispose() async {
    if (_disposed) return;

    _disposed = true;

    _logger.webSocketInfo(
      'Disposing WebSocket manager',
      methodName: 'dispose',
    );

    await disconnect();
    await _rawMessageController.close();
    await _connectionStatusController.close();
    _appLifecycleListener.dispose();
    await _queue.dispose();
  }
}
