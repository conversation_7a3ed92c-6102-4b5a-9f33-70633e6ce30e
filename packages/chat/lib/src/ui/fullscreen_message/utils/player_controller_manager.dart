import 'dart:async';
import 'package:audio_waveforms/audio_waveforms.dart';

class PlayerControllerStateHolder {
  final String key;
  final String messageId;
  final String path;
  final PlayerController controller;
  final List<double> waveFormData;
  final bool isPlaying;
  final int currentPosition;

  PlayerControllerStateHolder({
    required this.key,
    required this.messageId,
    required this.path,
    required this.controller,
    required this.waveFormData,
    required this.isPlaying,
    required this.currentPosition,
  });
}

class PlayerControllerManager {
  PlayerControllerManager._internal();

  static final PlayerControllerManager _instance =
      PlayerControllerManager._internal();

  factory PlayerControllerManager() => _instance;

  final Map<String, PlayerController> _controllers = {};
  final List<String> _usageQueue = [];
  final Map<String, PlayerControllerStateHolder> _stateHolders = {};

  static const int _maxControllers = 5;

  String _buildKey(String messageId) => '$messageId';
  String getKey(String messageId) => _buildKey(messageId);

  PlayerControllerStateHolder? getStateHolder(String messageId) {
    final key = _buildKey(messageId);
    return _stateHolders[key];
  }

  Future<PlayerController> getController({
    required String path,
    required String messageId,
    required List<double> waveFormData,
  }) async {
    final key = _buildKey(messageId);

    if (_controllers.containsKey(key)) {
      _usageQueue.remove(key);
      _usageQueue.add(key);

      final existing = _stateHolders[key];
      _stateHolders[key] = PlayerControllerStateHolder(
        key: key,
        messageId: messageId,
        path: path,
        controller: _controllers[key]!,
        waveFormData: waveFormData,
        isPlaying: existing?.isPlaying ?? false,
        currentPosition: existing?.currentPosition ?? 0,
      );
      return _controllers[key]!;
    }

    final controller = PlayerController();
    await controller.preparePlayer(path: path, shouldExtractWaveform: true);
    await controller.setFinishMode(finishMode: FinishMode.pause);

    _controllers[key] = controller;
    _usageQueue.add(key);

    _stateHolders[key] = PlayerControllerStateHolder(
      key: key,
      messageId: messageId,
      path: path,
      controller: controller,
      waveFormData: waveFormData,
      isPlaying: false,
      currentPosition: 0,
    );

    if (_controllers.length > _maxControllers) {
      final oldestKey = _usageQueue.removeAt(0);
      _controllers[oldestKey]?.dispose();
      _controllers.remove(oldestKey);
      _stateHolders.remove(oldestKey);
    }

    return controller;
  }

  void markControllerAsRecentlyUsed(
    String path,
    String messageId,
    List<double> waveFormData,
  ) {
    final key = _buildKey(messageId);
    if (_controllers.containsKey(key)) {
      _usageQueue.remove(key);
      _usageQueue.add(key);

      final controller = _controllers[key]!;
      final old = _stateHolders[key];

      _stateHolders[key] = PlayerControllerStateHolder(
        key: key,
        messageId: messageId,
        controller: controller,
        path: path,
        waveFormData: waveFormData,
        isPlaying: old?.isPlaying ?? false,
        currentPosition: old?.currentPosition ?? 0,
      );
    }
  }

  void saveState(String messageId,
      {required bool isPlaying, required int position}) {
    final key = _buildKey(messageId);
    final holder = _stateHolders[key];
    if (holder != null) {
      _stateHolders[key] = PlayerControllerStateHolder(
        key: holder.key,
        messageId: holder.messageId,
        path: holder.path,
        controller: holder.controller,
        waveFormData: holder.waveFormData,
        isPlaying: isPlaying,
        currentPosition: position,
      );
    }
  }

  void dispose(String messageId) {
    final key = _buildKey(messageId);
    if (_controllers.containsKey(key)) {
      _controllers[key]?.dispose();
      _controllers.remove(key);
      _usageQueue.remove(key);
      _stateHolders.remove(key);
    }
  }

  void disposeAll() {
    for (final key in _controllers.keys) {
      _controllers[key]?.dispose();
    }
    _controllers.clear();
    _usageQueue.clear();
    _stateHolders.clear();
  }
}
