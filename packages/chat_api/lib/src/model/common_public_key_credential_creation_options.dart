//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_authenticator_selection_criteria.dart';
import 'package:chat_api/src/model/common_auth_user.dart';
import 'package:chat_api/src/model/common_public_key_cred_param.dart';
import 'package:chat_api/src/model/common_relay_party.dart';
import 'package:json_annotation/json_annotation.dart';

part 'common_public_key_credential_creation_options.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonPublicKeyCredentialCreationOptions {
  /// Returns a new [CommonPublicKeyCredentialCreationOptions] instance.
  CommonPublicKeyCredentialCreationOptions({
    this.rp,
    this.user,
    this.challenge,
    this.pubKeyCredParams,
    this.timeout,
    this.attestation,
    this.authenticatorSelection,
  });

  @JsonKey(
    name: r'rp',
    required: false,
    includeIfNull: false,
  )
  final CommonRelayParty? rp;

  @Json<PERSON><PERSON>(
    name: r'user',
    required: false,
    includeIfNull: false,
  )
  final CommonAuthUser? user;

  @JsonKey(
    name: r'challenge',
    required: false,
    includeIfNull: false,
  )
  final String? challenge;

  @JsonKey(
    name: r'pubKeyCredParams',
    required: false,
    includeIfNull: false,
  )
  final List<CommonPublicKeyCredParam>? pubKeyCredParams;

  /// represents a timeout value, indicating the maximum waiting time allowed for a particular operation or process.
  @JsonKey(
    name: r'timeout',
    required: false,
    includeIfNull: false,
  )
  final int? timeout;

  /// represents an attestation.
  @JsonKey(
    name: r'attestation',
    required: false,
    includeIfNull: false,
  )
  final String? attestation;

  @JsonKey(
    name: r'authenticatorSelection',
    required: false,
    includeIfNull: false,
  )
  final CommonAuthenticatorSelectionCriteria? authenticatorSelection;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonPublicKeyCredentialCreationOptions &&
          other.rp == rp &&
          other.user == user &&
          other.challenge == challenge &&
          other.pubKeyCredParams == pubKeyCredParams &&
          other.timeout == timeout &&
          other.attestation == attestation &&
          other.authenticatorSelection == authenticatorSelection;

  @override
  int get hashCode =>
      rp.hashCode +
      user.hashCode +
      challenge.hashCode +
      pubKeyCredParams.hashCode +
      timeout.hashCode +
      attestation.hashCode +
      authenticatorSelection.hashCode;

  factory CommonPublicKeyCredentialCreationOptions.fromJson(
          Map<String, dynamic> json) =>
      _$CommonPublicKeyCredentialCreationOptionsFromJson(json);

  Map<String, dynamic> toJson() =>
      _$CommonPublicKeyCredentialCreationOptionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
