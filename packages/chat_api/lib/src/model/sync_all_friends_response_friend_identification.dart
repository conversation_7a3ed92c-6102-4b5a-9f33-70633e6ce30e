//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'sync_all_friends_response_friend_identification.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class SyncAllFriendsResponseFriendIdentification {
  /// Returns a new [SyncAllFriendsResponseFriendIdentification] instance.
  SyncAllFriendsResponseFriendIdentification({
    this.friendId,
    this.userId,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'friendId',
    required: false,
    includeIfNull: false,
  )
  final String? friendId;

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SyncAllFriendsResponseFriendIdentification &&
          other.friendId == friendId &&
          other.userId == userId;

  @override
  int get hashCode => friendId.hashCode + userId.hashCode;

  factory SyncAllFriendsResponseFriendIdentification.fromJson(
          Map<String, dynamic> json) =>
      _$SyncAllFriendsResponseFriendIdentificationFromJson(json);

  Map<String, dynamic> toJson() =>
      _$SyncAllFriendsResponseFriendIdentificationToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
