import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:data_router/data_router.dart' as d;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/mapper/friend_mapper.dart';
import '../../../domain/usecase/chat_user/delete_request_use_case.dart';
import '../../../domain/usecase/friend_request/get_friend_requests_use_case.dart';

part 'friend_request_bloc.freezed.dart';
part 'friend_request_event.dart';
part 'friend_request_state.dart';

@injectable
class FriendRequestBloc
    extends BaseBloc<FriendRequestEvent, FriendRequestState> {
  FriendRequestBloc(
    // this._loadFriendRequestsUseCase,
    this._getFriendRequestUseCase,
    this._acceptRequestUseCase,
    this._deleteRequestUseCase,
    this._friendDataOperation,
    this._userDataOperation,
    this._sessionDataOperation,
  ) : super(FriendRequestState.initial()) {
    on<InitiateFriendRequestEvent>(_onInit);
    on<UnSubscriptionEvent>(_onUnSubscription);
    on<LoadedFriendRequestEvent>(_onLoadedFriends);
    on<LoadMoreFriendsEvent>(_onLoadMoreFriends);
    on<AcceptFriendRequestsEvent>(_onAcceptFriendRequests);
    on<DeleteFriendRequestsEvent>(_onDeleteFriendRequests);
    on<DeleteFriendRequestsInLocalEvent>(_onDeleteFriendRequestInLocal);
    on<UndoDeleteFriendRequestsEvent>(_onUndoDeleteFriendRequests);
    on<ErrorEvent>(_onError);
    on<RefreshEvent>(_onRefresh);
  }

  // final LoadFriendRequestsUseCase _loadFriendRequestsUseCase;
  final GetFriendRequestUseCase _getFriendRequestUseCase;
  final AcceptRequestUseCase _acceptRequestUseCase;
  final DeleteRequestUseCase _deleteRequestUseCase;
  final d.UserDataOperation _userDataOperation;

  late bool _noMoreItems;

  static const _friendRemotePageSize = 500;

  final _userId = Config.getInstance().activeSessionKey;

  final userId = Config.getInstance().activeSessionKey;

  late Set<String> setPreviousIds = {};

  String deletedUsedId = '';

  bool _isUndo = false;

  StreamSubscription? _friendSubscription;
  StreamSubscription? _userSubscription;
  final d.FriendDataOperation _friendDataOperation;
  final d.SessionDataOperation _sessionDataOperation;

  Future<void> _onInit(
    InitiateFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    emit(FriendRequestState.initial());

    await _subscribeToFriendRequests();

    await _friendRequestsFromApi();
  }

  FutureOr<void> _subscribeToFriendRequests() async {
    _noMoreItems = _sessionDataOperation.getLoadedAllFriendRequests() ?? false;
    _friendSubscription?.cancel();
    _friendSubscription =
        await _friendDataOperation.watchFriendsRequest().listen((friends) {
      _observerFriendRequest(friends);
    });
  }

  void _observerFriendRequest(List<d.Friend> friends) {
    if (isClosed) return;

    final sorted = <ChatUser>[];
    for (final f in friends) {
      final user = f.friendUser.target;

      if (user != null && user.username != GlobalConfig.ghost) {
        final chatUser = ChatUserSerializer.convertUserToChatUser(user);
        if (deletedUsedId.isEmpty || chatUser.userId != deletedUsedId) {
          sorted.add(chatUser);
        }
      }
    }

    deletedUsedId = '';

    add(
      LoadedFriendRequestEvent(
        friendsRequests: sorted,
        noMoreItems: _noMoreItems,
      ),
    );
  }

  FutureOr<void> _friendRequestsFromApi() async {
    await _getFriendRequestUseCase.execute(GetFriendRequestInput());
  }

  Future<List<ChatUser>> _extractFriends(
    List<ChatFriend> chatFriends,
    String userId,
  ) async {
    List<ChatUser> chatUsers = [];

    for (final friend in chatFriends) {
      if (friend.participantIds?.contains(userId) != true) continue;

      final friendId = friend.participantIds!.firstWhere((id) => id != userId);
      final user = await _userDataOperation.getUserById(friendId);
      if (user != null && user.username != GlobalConfig.ghost) {
        chatUsers.add(ChatUserSerializer.convertUserToChatUser(user));
      }
    }

    return chatUsers;
  }

  @override
  Future<void> close() {
    _friendSubscription?.cancel();
    _userSubscription?.cancel();
    return super.close();
  }

  FutureOr<void> _onUnSubscription(
    UnSubscriptionEvent event,
    Emitter<FriendRequestState> emit,
  ) {
    _friendSubscription?.cancel();
    _userSubscription?.cancel();
  }

  FutureOr<void> _onLoadedFriends(
    LoadedFriendRequestEvent event,
    Emitter<FriendRequestState> emit,
  ) {
    emit(
      FriendRequestState.loaded(
        friends: event.friendsRequests,
        noMoreItems: event.noMoreItems,
      ),
    );
  }

  FutureOr<void> _onLoadMoreFriends(
    LoadMoreFriendsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    try {
      // TODO: Handle paging
      await _friendRequestsFromApi();
      _noMoreItems = true;
      if (_noMoreItems) {
        _sessionDataOperation.updateLoadedAllFriendRequests(true);
      }
    } catch (ex) {
      Log.e(name: 'FriendRequestBloc._onLoadMoreFriends', ex);
    }
  }

  FutureOr<void> _onAcceptFriendRequests(
    AcceptFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    final output = await _acceptRequestUseCase
        .execute(AcceptRequestInput(userId: event.userId));
    if (!output.ok) {
      add(RefreshEvent());
      add(ErrorEvent(code: output.code, message: output.message));
    }
  }

  FutureOr<void> _onDeleteFriendRequests(
    DeleteFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    _isUndo = false;
    deletedUsedId = event.userId;
    var temp = event.requests
        .where(
          (request) => request.userId != event.userId,
        )
        .toList();
    emit(FriendRequestState.loaded(friends: temp, noMoreItems: _noMoreItems));
  }

  FutureOr<void> _onDeleteFriendRequestInLocal(
    DeleteFriendRequestsInLocalEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    Future.delayed(
      const Duration(milliseconds: 2400),
      () async {
        if (!_isUndo) {
          final output = await _deleteRequestUseCase
              .execute(DeleteRequestInput(userId: event.userId));
          if (!output.ok && output.message != null || output.code != null) {
            add(RefreshEvent());
            add(ErrorEvent(code: output.code, message: output.message));
          }
        }
      },
    );
  }

  FutureOr<void> _onUndoDeleteFriendRequests(
    UndoDeleteFriendRequestsEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    _isUndo = true;
    final sessionKey = Config.getInstance().activeSessionKey ?? '';
    final friends =
        await _friendDataOperation.getIncomingRequestsForSession(sessionKey);
    var friendRequests =
        friends.map((friend) => friend.toChatFriend()).toList();
    friendRequests.sort((a, b) {
      final dateA = TimeUtils.parseUTCStringToDateTime(a.createTime) ??
          DateTime.fromMillisecondsSinceEpoch(0);
      final dateB = TimeUtils.parseUTCStringToDateTime(b.createTime) ??
          DateTime.fromMillisecondsSinceEpoch(0);
      return dateB.compareTo(dateA);
    });
    final requests = await _extractFriends(friendRequests, _userId!);
    deletedUsedId = '';
    emit(FriendRequestState.loaded(friends: requests));
  }

  Future<void> _onError(
    ErrorEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    emit(FriendRequestState.onError(code: event.code, message: event.message));
  }

  Future<void> _onRefresh(
    RefreshEvent event,
    Emitter<FriendRequestState> emit,
  ) async {
    emit(
      FriendRequestState.refresh(),
    );
  }
}
