//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_credential_request_options.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_generate_security_key_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateGenerateSecurityKeyFlowResponse {
  /// Returns a new [V3InitiateGenerateSecurityKeyFlowResponse] instance.
  V3InitiateGenerateSecurityKeyFlowResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final CommonCredentialRequestOptions? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateGenerateSecurityKeyFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3InitiateGenerateSecurityKeyFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateGenerateSecurityKeyFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateGenerateSecurityKeyFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
