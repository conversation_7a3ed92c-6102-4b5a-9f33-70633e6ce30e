//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_smart_otp_auth_flow_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateSmartOtpAuthFlowRequest {
  /// Returns a new [V3InitiateSmartOtpAuthFlowRequest] instance.
  V3InitiateSmartOtpAuthFlowRequest({
    this.reqId,
    this.deviceId,
  });

  @JsonKey(
    name: r'reqId',
    required: false,
    includeIfNull: false,
  )
  final String? reqId;

  @JsonKey(
    name: r'deviceId',
    required: false,
    includeIfNull: false,
  )
  final String? deviceId;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateSmartOtpAuthFlowRequest &&
          other.reqId == reqId &&
          other.deviceId == deviceId;

  @override
  int get hashCode => reqId.hashCode + deviceId.hashCode;

  factory V3InitiateSmartOtpAuthFlowRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateSmartOtpAuthFlowRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateSmartOtpAuthFlowRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
