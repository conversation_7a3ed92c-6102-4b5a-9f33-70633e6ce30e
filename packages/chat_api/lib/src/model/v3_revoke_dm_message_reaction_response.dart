//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_message_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_revoke_dm_message_reaction_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RevokeDMMessageReactionResponse {
  /// Returns a new [V3RevokeDMMessageReactionResponse] instance.
  V3RevokeDMMessageReactionResponse({
    this.ok,
    this.data,
    this.error,
    this.includes,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3MessageData? data;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RevokeDMMessageReactionResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.includes == includes;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + includes.hashCode;

  factory V3RevokeDMMessageReactionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3RevokeDMMessageReactionResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3RevokeDMMessageReactionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
