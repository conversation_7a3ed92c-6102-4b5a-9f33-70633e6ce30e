import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:chat/chat.dart' as chat;
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../../common/di/di.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage(name: 'MessageRequestRoute')
class MessageRequest extends StatefulWidget {
  const MessageRequest({super.key});

  @override
  State<MessageRequest> createState() => _MessageRequestState();
}

class _MessageRequestState extends State<MessageRequest> {
  late StreamSubscription _popEventSubscription;
  final _appEventBus = GetIt.instance.get<AppEventBus>();

  @override
  void initState() {
    _listenPopToEvent();
    super.initState();
  }

  void _listenPopToEvent() {
    _popEventSubscription =
        _appEventBus.on<PopToEvent>().listen(_onReceivedFromPopToEvent);
  }

  void _onReceivedFromPopToEvent(event) {
    if (!mounted) return;
    if (event is PopToMessageRequestEvent) {
      getIt<AppRouter>().popUntilRouteWithName(MessageRequestRoute.name);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _popEventSubscription.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return chat.MessageRequestPage(
      onTapMessage: (channel, isBlocked) {
        context.pushRoute(
          ChannelViewRoute(
            workspaceId: channel.workspaceId,
            channelId: channel.channelId,
            userId: channel.userId,
            isBlockedUser: isBlocked,
          ),
        );
      },
    );
  }
}
