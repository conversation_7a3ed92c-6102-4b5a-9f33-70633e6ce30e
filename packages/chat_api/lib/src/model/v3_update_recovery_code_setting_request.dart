//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_recovery_code_setting_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateRecoveryCodeSettingRequest {
  /// Returns a new [V3UpdateRecoveryCodeSettingRequest] instance.
  V3UpdateRecoveryCodeSettingRequest({
    this.userId,
    this.enable,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @Json<PERSON>ey(
    name: r'enable',
    required: false,
    includeIfNull: false,
  )
  final bool? enable;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateRecoveryCodeSettingRequest &&
          other.userId == userId &&
          other.enable == enable;

  @override
  int get hashCode => userId.hashCode + enable.hashCode;

  factory V3UpdateRecoveryCodeSettingRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateRecoveryCodeSettingRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateRecoveryCodeSettingRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
