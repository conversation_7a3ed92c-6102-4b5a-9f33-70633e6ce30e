import 'dart:async';

import 'package:injectable/injectable.dart' hide Order;
import 'package:shared/shared.dart';

import '../../core/config/config.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/private_data_repository.dart';
import '../database/entities/entities.dart';
import '../database/generated/objectbox.g.dart';
import '../events/private_data_events.dart';
import '../local/session_box.dart';
import '../models/deleted_user.dart';

/// [OPTIMIZED]
/// Implementation of PrivateDataRepository using SessionBox.
/// This version focuses on optimizing existing methods for robustness and clarity
/// without adding new ones.
@LazySingleton(as: PrivateDataRepository)
class PrivateDataRepositoryImpl extends BaseRepository<PrivateData>
    implements PrivateDataRepository {
  final SessionBox<ChannelPrivateData> _channel;
  final SessionBox<CallLogPrivateData> _callLog;
  final SessionBox<UserPrivateData> _userPrivateDataBox;
  final SessionBox<User> _userBox;
  final SessionBox<Channel> _channelBox;
  final Store _store;

  PrivateDataRepositoryImpl(
    SessionBox<PrivateData> privateDataBox,
    this._channel,
    this._callLog,
    this._userPrivateDataBox,
    this._userBox,
    this._channelBox,
    this._store,
  ) : super(privateDataBox);

  @override
  PrivateData? getPrivateData() {
    try {
      final sessionKey = Config.getInstance().activeSessionKey;
      // Optimization: Add null check for safety.
      if (sessionKey == null) return null;
      return getFirst(PrivateData_.sessionKeyField.equals(sessionKey));
    } catch (e) {
      throw Exception('Failed to get private data: $e');
    }
  }

  @override
  Stream<PrivateData?> watchPrivateData() {
    try {
      final sessionKey = Config.getInstance().activeSessionKey;
      // Optimization: Add null check for safety.
      if (sessionKey == null) return Stream.value(null);

      return watch(PrivateData_.sessionKeyField.equals(sessionKey)).map(
        (
          query,
        ) =>
            query.findFirst(),
      );
    } catch (e) {
      throw Exception('Failed to watch private data: $e');
    }
  }

  /// Insert or update PrivateData entity and its relationships within a single transaction.
  @override
  int insertPrivateData(PrivateData privateData) {
    try {
      final id = _store.runInTransaction(TxMode.write, () {
        // Optimization: Ensure we don't create duplicate parent objects.
        // If a PrivateData object for the current session already exists,
        // reuse its ID to ensure we are performing an update.
        final existingParent = getPrivateData();
        if (existingParent != null) {
          privateData.id = existingParent.id;
        }

        final parentId = super.put(privateData);
        privateData.id = parentId;

        // Delegate to helper methods that handle UPSERT logic for children.
        // These helpers now run within the parent transaction.
        _handleChannelPrivateData(privateData);
        _handleUserPrivateData(privateData);

        return parentId;
      });

      // [RACE CONDITION FIX] Notify that private data sync is complete
      // This ensures UserPrivateData is available after the transaction is committed.
      _notifyPrivateDataSyncComplete(privateData);

      return id;
    } catch (e) {
      throw Exception(
        'Failed to insert private data for user ${privateData.userId}: $e',
      );
    }
  }

  /// Ensures that ChannelPrivateData children are correctly inserted or updated (UPSERT).
  /// This method MUST be called from within an active write transaction.
  void _handleChannelPrivateData(PrivateData privateData) {
    final channels = privateData.channels;
    if (channels.isEmpty) {
      return;
    }

    // 1. Collect all channelIds from the incoming data.
    final channelIds = channels.map((c) => c.channelId).toSet().toList();
    if (channelIds.isEmpty) return;

    // 2. Find all existing records for these channels in a single query.
    final queryBuilder = _channel.queryWith(
      ChannelPrivateData_.channelIdField.oneOf(channelIds),
    );
    final query = queryBuilder.build();

    final List<ChannelPrivateData> existingData;
    try {
      existingData = query.find();
    } finally {
      query.close();
    }

    // 3. Create a map for efficient lookup of existing data by channelId.
    final existingDataMap = {
      for (var data in existingData) data.channelId: data,
    };

    // 4. For each incoming channel, check if it exists and assign its ID if it does.
    for (final channel in channels) {
      final existing = existingDataMap[channel.channelId];
      if (existing != null) {
        // If the record exists, reuse its database ID to trigger an UPDATE.
        channel.id = existing.id;
      }
      // Ensure the relationship to the parent is set.
      channel.privateData.target = privateData;
    }

    // 5. Use putMany for a high-performance batch insert/update operation.
    _channel.putMany(channels);

    // 6. Update Channel entities to link with ChannelPrivateData
    _updateChannelPrivateDataRelationships(channels);
  }

  /// Updates Channel entities to link with their corresponding ChannelPrivateData.
  /// This method MUST be called from within an active write transaction.
  void _updateChannelPrivateDataRelationships(
    List<ChannelPrivateData> channelPrivateDataList,
  ) {
    try {
      if (channelPrivateDataList.isEmpty) return;

      // Validate channelIds are not empty to prevent ObjectBox crash
      final validChannelIds = channelPrivateDataList
          .where(
            (cpd) => cpd.channelId.trim().isNotEmpty,
          )
          .map((cpd) => cpd.channelId)
          .toList();

      if (validChannelIds.isEmpty) return;

      // Find corresponding Channel entities
      final channelQuery = _channelBox
          .queryWith(
            Channel_.channelId.oneOf(validChannelIds),
          )
          .build();

      final correspondingChannels = channelQuery.find();
      channelQuery.close();

      if (correspondingChannels.isEmpty) return;

      // Create a map for efficient lookup of ChannelPrivateData by channelId
      final channelPrivateDataMap = {
        for (var cpd in channelPrivateDataList) cpd.channelId: cpd,
      };

      // Link ChannelPrivateData to Channel entities
      for (final channel in correspondingChannels) {
        final privateDataToLink = channelPrivateDataMap[channel.channelId];
        if (privateDataToLink != null) {
          channel.privateData.target = privateDataToLink;
          channel.sort = privateDataToLink.sort;
          channel.pinned = privateDataToLink.pinned;
        }
      }

      // Batch update all modified Channel entities
      _channelBox.putMany(correspondingChannels);
    } catch (e) {
      // Log error but don't throw to avoid breaking the main transaction
      // This is a relationship update, not critical for data integrity
    }
  }

  /// Updates Channel entities to link with their corresponding UserPrivateData.
  /// This method MUST be called from within an active write transaction.
  void _updateUserPrivateDataRelationships(
    List<UserPrivateData> userPrivateDataList,
  ) {
    try {
      if (userPrivateDataList.isEmpty) return;

      // Validate userIds are not empty to prevent ObjectBox crash
      final validUserIds = userPrivateDataList
          .where(
            (upd) => upd.userIdField.trim().isNotEmpty,
          )
          .map((upd) => upd.userIdField)
          .toList();

      if (validUserIds.isEmpty) return;

      // Find corresponding Channel entities where userId matches
      final channelQuery = _channelBox
          .queryWith(
            Channel_.userId.oneOf(validUserIds),
          )
          .build();

      final correspondingChannels = channelQuery.find();
      channelQuery.close();

      // Find corresponding User entities
      final userQuery = _userBox
          .queryWith(
            User_.userId.oneOf(validUserIds),
          )
          .build();

      final correspondingUsers = userQuery.find();
      userQuery.close();

      // Create a map for efficient lookup of UserPrivateData by userId
      final userPrivateDataMap = {
        for (var upd in userPrivateDataList) upd.userIdField: upd,
      };

      final channelsToUpdate = <Channel>[];
      final usersToUpdate = <User>[];

      // Update Channel entities with aliasName from UserPrivateData
      for (final channel in correspondingChannels) {
        final privateDataToLink = userPrivateDataMap[channel.userId];
        if (privateDataToLink != null) {
          // Only update if aliasName is different to avoid unnecessary updates
          if (channel.aliasName != privateDataToLink.aliasName) {
            channel.aliasName = privateDataToLink.aliasName;
            channelsToUpdate.add(channel);
          }
        }
      }

      // Update User entities with aliasName and blocked from UserPrivateData
      for (final user in correspondingUsers) {
        final privateDataToLink = userPrivateDataMap[user.userId];
        if (privateDataToLink != null) {
          bool needsUpdate = false;

          // Only update if aliasName is different
          if (user.aliasName != privateDataToLink.aliasName) {
            user.aliasName = privateDataToLink.aliasName;
            needsUpdate = true;
          }

          // Only update if blocked status is different
          if (user.blocked != privateDataToLink.blocked) {
            user.blocked = privateDataToLink.blocked;
            needsUpdate = true;
          }

          if (needsUpdate) {
            usersToUpdate.add(user);
          }
        }
      }

      // Batch update only modified entities to avoid unnecessary database operations
      if (channelsToUpdate.isNotEmpty) {
        _channelBox.putMany(channelsToUpdate);
      }

      if (usersToUpdate.isNotEmpty) {
        _userBox.putMany(usersToUpdate);
      }
    } catch (e) {
      // Log error but don't throw to avoid breaking the main transaction
      // This is a relationship update, not critical for data integrity
    }
  }

  /// Ensures that UserPrivateData children are correctly inserted or updated (UPSERT).
  /// This method MUST be called from within an active write transaction.
  void _handleUserPrivateData(PrivateData privateData) {
    final users = privateData.users;
    if (users.isEmpty) {
      return;
    }

    final userIds = users.map((u) => u.userIdField).toSet().toList();
    if (userIds.isEmpty) return;

    // [DEFENSIVE FIX]
    // The query now includes the sessionKey to ensure we only find records
    // relevant to the current active session. This prevents conflicts with
    // phantom data that might have an empty or different session key.
    final currentSessionKey = Config.getInstance().activeSessionKey;
    if (currentSessionKey == null) {
      return;
    } // Cannot proceed without a session key

    // 1. Batch query the database to find all users that already exist FOR THE CURRENT SESSION.
    final queryBuilder = _userPrivateDataBox.queryWith(
      UserPrivateData_.userIdField.oneOf(userIds).and(
            UserPrivateData_.sessionKeyField.equals(currentSessionKey),
          ),
    );
    final query = queryBuilder.build();

    final existedUsersInDb;
    try {
      existedUsersInDb = query.find();
    } finally {
      query.close();
    }

    // 2. Create a map of userId to existing id for fast lookup.
    final existedIdMap = {
      for (var u in existedUsersInDb) u.userIdField: u.id,
    };

    // 3. For each input user, establish relationships and assign existing IDs.
    for (final u in users) {
      u.sessionKeyField =
          currentSessionKey; // Ensure session key is set correctly
      u.privateData.target = privateData;
      final oldId = existedIdMap[u.userIdField];
      if (oldId != null) {
        // If user exists, assign the old id to perform an UPDATE.

        u.id = oldId;
      }
      // Otherwise, id remains 0, and ObjectBox will perform an INSERT.
    }

    // 4. Batch insert or update all users.
    _userPrivateDataBox.putMany(users);

    // 5. Update Channel entities to link with UserPrivateData
    _updateUserPrivateDataRelationships(users);

    // 6. Update the relationship in the User entity using a batch approach.
    final updatedUserIds = users.map((u) => u.userIdField).toList();
    if (updatedUserIds.isEmpty) {
      return;
    }

    // Fetch all corresponding User entities in a single query.
    final userQuery =
        _userBox.queryWith(User_.userId.oneOf(updatedUserIds)).build();
    final correspondingUsers = userQuery.find();
    userQuery.close();

    if (correspondingUsers.isEmpty) {
      return;
    }

    // Note: privateData relationship has been removed from User entity
    // User data is now handled through direct fields like blocked, aliasName, etc.
    // The _updateUserPrivateDataRelationships method handles the actual field updates

    // Batch update all the modified User entities.
    _userBox.putMany(correspondingUsers);
  }

  /// [RACE CONDITION FIX] Notify that private data sync is complete
  /// This triggers an event that can be listened to by other components
  void _notifyPrivateDataSyncComplete(PrivateData privateData) {
    // Use a short delay to ensure all database operations are committed
    // before emitting the event
    Future.delayed(const Duration(milliseconds: 100), () {
      try {
        final sessionKey = privateData.sessionKey;
        final userIds = privateData.users.map((u) => u.userIdField).toList();

        if (userIds.isNotEmpty) {
          // Emit event that private data sync is complete
          final event = PrivateDataSyncCompleteEvent(
            sessionKey: sessionKey,
            userIds: userIds,
          );

          PrivateDataEventBus().emitPrivateDataSyncComplete(event);
        }
      } catch (e) {}
    });
  }

  @override
  bool deletePrivateData() {
    try {
      final privateData = getPrivateData();
      if (privateData == null) return false;

      return remove(privateData.id);
    } catch (e) {
      throw Exception('Failed to delete private data: $e');
    }
  }

  @override
  int deleteAllPrivateData() {
    try {
      return removeAll();
    } catch (e) {
      throw Exception('Failed to delete all private data: $e');
    }
  }

  @override
  Future<bool> deleteAllBySessionKey({required String sessionKey}) async {
    final query = messageBox.box
        .query(PrivateData_.sessionKey.equals(sessionKey))
        .build();
    final count = query.remove();
    query.close();
    return count > 0;
  }

  // region Child entity operations
  @override
  ChannelPrivateData? getChannelPrivateData(String channelId) {
    try {
      if (channelId.trim().isEmpty) {
        return null;
      }

      final result = _channel.getFirstWith(
        ChannelPrivateData_.channelIdField.equals(channelId),
      );
      return result;
    } catch (e) {
      throw Exception(
        'Failed to get channel private data for channel[$channelId]: $e',
      );
    }
  }

  @override
  UserPrivateData? getUserPrivateData(String userId) {
    try {
      if (userId.trim().isEmpty) {
        return null;
      }

      // Optimization: Provide a full implementation instead of a stub.
      final result = _userPrivateDataBox.getFirstWith(
        UserPrivateData_.userIdField.equals(userId),
      );
      return result;
    } catch (e) {
      throw Exception('Failed to get user private data for user[$userId]: $e');
    }
  }

  @override
  CallLogPrivateData? getCallLogPrivateData(String callId) {
    try {
      if (callId.trim().isEmpty) {
        return null;
      }

      final result = _callLog.getFirstWith(
        CallLogPrivateData_.callIdField.equals(callId),
      );
      return result;
    } catch (e) {
      throw Exception(
        'Failed to get call log private data for call[$callId]: $e',
      );
    }
  }

  @override
  Stream<ChannelPrivateData?> watchChannelPrivateData(String channelId) {
    try {
      if (channelId.trim().isEmpty) {
        return Stream.value(null);
      }

      return _channel
          .watch(
            ChannelPrivateData_.channelIdField.equals(channelId),
          )
          .map(
            (query) => query.findFirst(),
          )
          .handleError((error) {
        return null;
      });
    } catch (e) {
      throw Exception(
        'Failed to watch channel private data for channel[$channelId]: $e',
      );
    }
  }

  @override
  Stream<UserPrivateData?> watchUserPrivateData(String userId) {
    try {
      if (userId.trim().isEmpty) {
        return Stream.value(null);
      }

      return _userPrivateDataBox
          .watch(
            UserPrivateData_.userIdField.equals(userId),
          )
          .map(
            (query) => query.findFirst(),
          )
          .handleError((error) {
        return null;
      });
    } catch (e) {
      throw Exception(
        'Failed to watch user private data for user[$userId]: $e',
      );
    }
  }

  @override
  Stream<CallLogPrivateData?> watchCallLogPrivateData(String callId) {
    try {
      if (callId.trim().isEmpty) {
        return Stream.value(null);
      }

      return _callLog
          .watch(CallLogPrivateData_.callIdField.equals(callId))
          .map((query) => query.findFirst())
          .handleError((error) {
        return null;
      });
    } catch (e) {
      throw Exception(
        'Failed to watch call log private data for call[$callId]: $e',
      );
    }
  }

  @override
  bool deleteChannelPrivateData(String channelId) {
    try {
      if (channelId.trim().isEmpty) {
        return false;
      }

      final channelPrivateData = getChannelPrivateData(channelId);
      if (channelPrivateData == null) {
        return false;
      }

      return _store.runInTransaction(TxMode.write, () {
        final result = _channel.remove(channelPrivateData.id);

        // Update Channel entity to remove the link to this ChannelPrivateData
        if (result) {
          final channelQuery = _channelBox
              .queryWith(
                Channel_.channelId.equals(channelId),
              )
              .build();
          final correspondingChannel = channelQuery.findFirst();
          channelQuery.close();

          if (correspondingChannel != null) {
            correspondingChannel.privateData.target = null;
            _channelBox.put(correspondingChannel);
          }
        }
        return result;
      });
    } catch (e) {
      throw Exception(
        'Failed to delete channel private data for channel[$channelId]: $e',
      );
    }
  }

  @override
  bool deleteUserPrivateData(String userId) {
    try {
      if (userId.trim().isEmpty) {
        return false;
      }

      final userPrivateData = getUserPrivateData(userId);
      if (userPrivateData == null) {
        return false;
      }

      return _store.runInTransaction(TxMode.write, () {
        final result = _userPrivateDataBox.remove(userPrivateData.id);

        // Update User entity to remove the link to this UserPrivateData
        if (result) {
          final userQuery = _userBox
              .queryWith(
                User_.userId.equals(userId),
              )
              .build();
          final correspondingUser = userQuery.findFirst();
          userQuery.close();

          if (correspondingUser != null) {
            // Note: privateData relationship has been removed from User entity
            // User data is now handled through direct fields like blocked, aliasName, etc.
            _userBox.put(correspondingUser);
          }
        }
        return result;
      });
    } catch (e) {
      throw Exception(
        'Failed to delete user private data for user[$userId]: $e',
      );
    }
  }

  @override
  bool deleteCallLogPrivateData(String callId) {
    try {
      if (callId.trim().isEmpty) {
        return false;
      }

      final callLogPrivateData = getCallLogPrivateData(callId);
      if (callLogPrivateData == null) {
        return false;
      }

      final result = _callLog.remove(callLogPrivateData.id);
      return result;
    } catch (e) {
      throw Exception(
        'Failed to delete call log private data for call[$callId]: $e',
      );
    }
  }

  @override
  List<ChannelPrivateData> getAllChannelPrivateData() {
    try {
      final result = _channel.getAll();
      return result;
    } catch (e) {
      throw Exception('Failed to get all channel private data: $e');
    }
  }

  @override
  List<UserPrivateData> getAllUserPrivateData() {
    try {
      final result = _userPrivateDataBox.getAll();
      return result;
    } catch (e) {
      throw Exception('Failed to get all user private data: $e');
    }
  }

  @override
  List<CallLogPrivateData> getAllCallLogPrivateData() {
    try {
      final result = _callLog.getAll();
      return result;
    } catch (e) {
      throw Exception('Failed to get all call log private data: $e');
    }
  }

  @override
  Stream<List<ChannelPrivateData>> watchAllChannelPrivateData() {
    try {
      return _channel.watchAll().map((query) => query.find()).handleError((
        error,
      ) {
        return <ChannelPrivateData>[];
      });
    } catch (e) {
      throw Exception('Failed to watch all channel private data: $e');
    }
  }

  @override
  Stream<List<UserPrivateData>> watchAllUserPrivateData() {
    try {
      return _userPrivateDataBox
          .watchAll()
          .map((query) => query.find())
          .handleError((error) {
        return <UserPrivateData>[];
      });
    } catch (e) {
      throw Exception('Failed to watch all user private data: $e');
    }
  }

  @override
  Stream<List<CallLogPrivateData>> watchAllCallLogPrivateData() {
    try {
      return _callLog.watchAll().map((query) => query.find()).handleError((
        error,
      ) {
        return <CallLogPrivateData>[];
      });
    } catch (e) {
      throw Exception('Failed to watch all call log private data: $e');
    }
  }

// endregion

  // region UserPrivateData operations
  @override
  List<UserPrivateData> getPrivateDataByIds(List<String> listUserId) {
    try {
      if (listUserId.isEmpty) {
        return [];
      }

      // Validate all userIds are not empty to prevent ObjectBox crash
      final validUserIds = listUserId
          .where(
            (id) => id.trim().isNotEmpty,
          )
          .toList();

      if (validUserIds.isEmpty) {
        return [];
      }

      final data = _getExistingPrivateDataByUserIds(validUserIds);

      return data.values.toList();
    } catch (e) {
      throw Exception('Failed to get private data by IDs: $e');
    }
  }

  @override
  void insertUserPrivateData(UserPrivateData dUserPrivateData) {
    try {
      // Validate userId is not empty to prevent ObjectBox crash
      if (dUserPrivateData.userIdField.trim().isEmpty) {
        throw Exception('Cannot insert UserPrivateData: userId is empty');
      }

      final sessionKey = dUserPrivateData.sessionKey.isNotEmpty
          ? dUserPrivateData.sessionKey
          : Config.getInstance().activeSessionKey;

      if (sessionKey == null || sessionKey.isEmpty) {
        throw Exception(
          'Cannot insert UserPrivateData: sessionKey is null or empty',
        );
      }

      final parentPrivateData = getFirst(
        PrivateData_.sessionKeyField.equals(sessionKey),
      );

      if (parentPrivateData == null) {
        throw Exception(
          'Cannot insert UserPrivateData: Parent PrivateData not found for session $sessionKey',
        );
      }

      _store.runInTransaction(TxMode.write, () {
        dUserPrivateData.privateData.target = parentPrivateData;

        final existingData = _getExistingPrivateDataByUserIds(
          [dUserPrivateData.userIdField],
        )[dUserPrivateData.userIdField];

        if (existingData != null) {
          dUserPrivateData.id = existingData.id;
        }

        _userPrivateDataBox.put(dUserPrivateData);

        // Update Channel entity to link with this UserPrivateData
        _updateUserPrivateDataRelationships([dUserPrivateData]);

        // Update User entity to link with this UserPrivateData
        final userQuery = _userBox
            .queryWith(
              User_.userId.equals(dUserPrivateData.userIdField),
            )
            .build();
        final correspondingUser = userQuery.findFirst();
        userQuery.close();

        if (correspondingUser != null) {
          correspondingUser.aliasName = dUserPrivateData.aliasName;
          correspondingUser.blocked = dUserPrivateData.blocked;
          correspondingUser.updateTime = TimeUtils.now().toUtc();
          _userBox.put(correspondingUser);
        }

        // Update Channel entity (for DM channels)
        final channelQuery = _channelBox
            .queryWith(
              Channel_.userId.equals(dUserPrivateData.userIdField),
            )
            .build();
        final correspondingChannel = channelQuery.findFirst();
        channelQuery.close();

        if (correspondingChannel != null) {
          correspondingChannel.aliasName = dUserPrivateData.aliasName;
          correspondingChannel.updateTime = TimeUtils.now().toUtc();
          _channelBox.put(correspondingChannel);
        }
      });
    } catch (e) {
      throw Exception('Failed to insert UserPrivateData: $e');
    }
  }

  @override
  Future<void> syncDeleteUserPrivateData(List<DeletedUser> deletedUsers) async {
    if (deletedUsers.isEmpty) return;

    final List<Query> openQueries = [];
    final List<User> usersToUpdate = [];
    final List<Channel> channelsToUpdate = [];

    try {
      // Validate all userIds are not empty to prevent ObjectBox crash
      final validDeletedUsers = deletedUsers
          .where(
            (user) => user.userId.trim().isNotEmpty,
          )
          .toList();

      if (validDeletedUsers.isEmpty) {
        return;
      }

      _store.runInTransaction(TxMode.write, () {
        // [BATCH PROCESSING] Process all deletions atomically
        for (final deletedUser in validDeletedUsers) {
          final userPrivateData = _userPrivateDataBox.getFirstWith(
            UserPrivateData_.userIdField.equals(deletedUser.userId),
          );

          if (userPrivateData != null) {
            _userPrivateDataBox.remove(userPrivateData.id);

            // Find and reset User entity
            final userQuery = _userBox
                .queryWith(
                  User_.userId.equals(deletedUser.userId),
                )
                .build();
            openQueries.add(userQuery);
            final correspondingUser = userQuery.findFirst();

            if (correspondingUser != null) {
              // Reset UserPrivateData fields to default values
              correspondingUser.aliasName = null;
              correspondingUser.blocked = false;
              usersToUpdate.add(correspondingUser);

              // Find related DM channels to reset
              final channelQuery = _channelBox
                  .queryWith(
                    Channel_.userId.equals(deletedUser.userId),
                  )
                  .build();
              openQueries.add(channelQuery);
              final dmChannels = channelQuery.find();

              for (final channel in dmChannels) {
                channel.aliasName = '';
                channelsToUpdate.add(channel);
              }
            }
          }
        }

        // [ATOMIC PERSISTENCE] Persist all updates in batches
        if (usersToUpdate.isNotEmpty) {
          _userBox.putMany(usersToUpdate);
        }
        if (channelsToUpdate.isNotEmpty) {
          _channelBox.putMany(channelsToUpdate);
        }
      });
    } catch (e) {
      throw Exception('Failed to sync delete UserPrivateData: $e');
    } finally {
      // [MEMORY SAFETY] Ensure all queries are closed to prevent memory leaks
      for (final query in openQueries) {
        try {
          query.close();
        } catch (e) {
          // Log warning but don't throw to avoid masking original error
        }
      }
    }
  }

  /// Helper method to get existing private data by user IDs
  Map<String, UserPrivateData> _getExistingPrivateDataByUserIds(
    List<String> userIds,
  ) {
    try {
      if (userIds.isEmpty) {
        return {};
      }

      // Validate all userIds are not empty to prevent ObjectBox crash
      final validUserIds = userIds
          .where(
            (id) => id.trim().isNotEmpty,
          )
          .toList();

      if (validUserIds.isEmpty) {
        return {};
      }

      final queryBuilder = _userPrivateDataBox.queryWith(
        UserPrivateData_.userIdField.oneOf(validUserIds),
      );
      final query = queryBuilder.build();

      try {
        final entities = query.find();
        final result = {
          for (var entity in entities) entity.userIdField: entity,
        };
        return result;
      } finally {
        // Always close the query to prevent memory leaks and crashes
        query.close();
      }
    } catch (e) {
      throw Exception('Failed to get existing private data by user IDs: $e');
    }
  }

// endregion

  // region ChannelPrivateData operations (moved from UserRepository)
  @override
  ChannelPrivateData? getChannel(String channelId) {
    try {
      // Validate channelId is not empty to prevent ObjectBox crash
      if (channelId.trim().isEmpty) {
        return null;
      }

      return _channel.getFirstWith(
        ChannelPrivateData_.channelIdField.equals(channelId),
      );
    } catch (e) {
      throw Exception(
        'Failed to get channel private data for channel[$channelId]: $e',
      );
    }
  }

  @override
  void updateChannel(ChannelPrivateData channel) {
    try {
      final sessionKey = Config.getInstance().activeSessionKey;

      if (sessionKey == null || sessionKey.isEmpty) {
        throw Exception(
          'Cannot put ChannelPrivateData: sessionKey is null or empty',
        );
      }

      final parentPrivateData = getFirst(
        PrivateData_.sessionKeyField.equals(sessionKey),
      );

      if (parentPrivateData == null) {
        throw Exception(
          'Cannot put ChannelPrivateData: Parent PrivateData not found for session $sessionKey',
        );
      }

      _store.runInTransaction(TxMode.write, () {
        channel.privateData.target = parentPrivateData;

        final existingChannel = getChannel(channel.channelId);
        if (existingChannel != null) {
          channel.id = existingChannel.id;
        }

        _channel.put(channel);

        // Update Channel entity to link with this ChannelPrivateData
        _updateChannelPrivateDataRelationships([channel]);

        // Update Channel entity to link with this ChannelPrivateData
        final channelQuery = _channelBox
            .queryWith(
              Channel_.channelId.equals(channel.channelId),
            )
            .build();
        final correspondingChannel = channelQuery.findFirst();
        channelQuery.close();

        if (correspondingChannel != null) {
          correspondingChannel.privateData.target = channel;
          _channelBox.put(correspondingChannel);
        }
      });
    } catch (e) {
      throw Exception('Failed to update channel private data: $e');
    }
  }

  @override
  int? getMaxSortChannel() {
    try {
      final allChannels = _channel.getAll();
      if (allChannels.isEmpty) {
        return null;
      }

      final pinnedChannels = allChannels.where((c) => c.pinned).toList();
      if (pinnedChannels.isEmpty) {
        return 0;
      }

      return pinnedChannels.map((c) => c.sort).reduce((a, b) => a > b ? a : b);
    } catch (e) {
      throw Exception('Failed to get max sort channel: $e');
    }
  }
// endregion
}
