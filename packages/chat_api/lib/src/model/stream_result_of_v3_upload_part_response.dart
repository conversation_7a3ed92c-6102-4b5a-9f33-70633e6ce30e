//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/rpc_status.dart';
import 'package:chat_api/src/model/v3_upload_part_response.dart';
import 'package:json_annotation/json_annotation.dart';

part 'stream_result_of_v3_upload_part_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class StreamResultOfV3UploadPartResponse {
  /// Returns a new [StreamResultOfV3UploadPartResponse] instance.
  StreamResultOfV3UploadPartResponse({
    this.result,
    this.error,
  });

  @JsonKey(
    name: r'result',
    required: false,
    includeIfNull: false,
  )
  final V3UploadPartResponse? result;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final RpcStatus? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StreamResultOfV3UploadPartResponse &&
          other.result == result &&
          other.error == error;

  @override
  int get hashCode => result.hashCode + error.hashCode;

  factory StreamResultOfV3UploadPartResponse.fromJson(
          Map<String, dynamic> json) =>
      _$StreamResultOfV3UploadPartResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$StreamResultOfV3UploadPartResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
