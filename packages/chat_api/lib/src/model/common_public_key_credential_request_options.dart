//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/common_public_key_credential_descriptor.dart';
import 'package:json_annotation/json_annotation.dart';

part 'common_public_key_credential_request_options.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonPublicKeyCredentialRequestOptions {
  /// Returns a new [CommonPublicKeyCredentialRequestOptions] instance.
  CommonPublicKeyCredentialRequestOptions({
    this.challenge,
    this.timeout,
    this.rpId,
    this.attestation,
    this.userVerification,
    this.allowCredentials,
  });

  /// The challenge value used in the credential request.
  @Json<PERSON>ey(
    name: r'challenge',
    required: false,
    includeIfNull: false,
  )
  final String? challenge;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'timeout',
    required: false,
    includeIfNull: false,
  )
  final int? timeout;

  /// Making the credential request.
  @Json<PERSON>ey(
    name: r'rpId',
    required: false,
    includeIfNull: false,
  )
  final String? rpId;

  /// The desired attestation conveyance preference.
  @JsonKey(
    name: r'attestation',
    required: false,
    includeIfNull: false,
  )
  final String? attestation;

  /// Indicates the desired level of user verification required for the credential request.
  @JsonKey(
    name: r'userVerification',
    required: false,
    includeIfNull: false,
  )
  final String? userVerification;

  /// A list of allowed public key credentials for the request.
  @JsonKey(
    name: r'allowCredentials',
    required: false,
    includeIfNull: false,
  )
  final List<CommonPublicKeyCredentialDescriptor>? allowCredentials;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonPublicKeyCredentialRequestOptions &&
          other.challenge == challenge &&
          other.timeout == timeout &&
          other.rpId == rpId &&
          other.attestation == attestation &&
          other.userVerification == userVerification &&
          other.allowCredentials == allowCredentials;

  @override
  int get hashCode =>
      challenge.hashCode +
      timeout.hashCode +
      rpId.hashCode +
      attestation.hashCode +
      userVerification.hashCode +
      allowCredentials.hashCode;

  factory CommonPublicKeyCredentialRequestOptions.fromJson(
          Map<String, dynamic> json) =>
      _$CommonPublicKeyCredentialRequestOptionsFromJson(json);

  Map<String, dynamic> toJson() =>
      _$CommonPublicKeyCredentialRequestOptionsToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
