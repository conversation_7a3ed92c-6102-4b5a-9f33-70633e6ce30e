//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_channel.dart';
import 'package:chat_api/src/model/v3_data_include.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_incoming_message_request_accepted_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3IncomingMessageRequestAcceptedEventData {
  /// Returns a new [V3IncomingMessageRequestAcceptedEventData] instance.
  V3IncomingMessageRequestAcceptedEventData({
    this.channel,
    this.includes,
  });

  @JsonKey(
    name: r'channel',
    required: false,
    includeIfNull: false,
  )
  final V3Channel? channel;

  @JsonKey(
    name: r'includes',
    required: false,
    includeIfNull: false,
  )
  final V3DataInclude? includes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3IncomingMessageRequestAcceptedEventData &&
          other.channel == channel &&
          other.includes == includes;

  @override
  int get hashCode => channel.hashCode + includes.hashCode;

  factory V3IncomingMessageRequestAcceptedEventData.fromJson(
          Map<String, dynamic> json) =>
      _$V3IncomingMessageRequestAcceptedEventDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3IncomingMessageRequestAcceptedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
