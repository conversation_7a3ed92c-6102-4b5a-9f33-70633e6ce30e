import 'dart:async';

import '../../data/database/entities/entities.dart';
import '../../data/models/deleted_user.dart';

/// Repository interface for PrivateData entity operations
/// Provides session-based data access with automatic filtering
abstract class PrivateDataRepository {
  /// Get private data by user ID
  /// Returns null if not found
  PrivateData? getPrivateData();

  /// Watch private data changes by user ID
  /// Returns stream of private data updates
  Stream<PrivateData?> watchPrivateData();

  /// Insert or update private data
  /// Returns the ObjectBox ID of the saved entity
  int insertPrivateData(PrivateData privateData);

  /// Delete private data by user ID
  /// Returns true if deleted, false if not found
  bool deletePrivateData();

  /// Delete all private data for current session
  /// Returns number of deleted entities
  int deleteAllPrivateData();

  /// Get channel private data for a specific channel
  ChannelPrivateData? getChannelPrivateData(String channelId);

  /// Get user private data for a specific user
  UserPrivateData? getUserPrivateData(String userId);

  /// Get call log private data for a specific call
  CallLogPrivateData? getCallLogPrivateData(String callId);

  /// Watch channel private data changes
  Stream<ChannelPrivateData?> watchChannelPrivateData(String channelId);

  /// Watch user private data changes
  Stream<UserPrivateData?> watchUserPrivateData(String userId);

  /// Watch call log private data changes
  Stream<CallLogPrivateData?> watchCallLogPrivateData(String callId);

  /// Delete channel private data
  bool deleteChannelPrivateData(String channelId);

  /// Delete user private data
  bool deleteUserPrivateData(String userId);

  /// Delete call log private data
  bool deleteCallLogPrivateData(String callId);

  /// Get all channel private data for current session
  List<ChannelPrivateData> getAllChannelPrivateData();

  /// Get all user private data for current session
  List<UserPrivateData> getAllUserPrivateData();

  /// Get all call log private data for current session
  List<CallLogPrivateData> getAllCallLogPrivateData();

  /// Watch all channel private data changes
  Stream<List<ChannelPrivateData>> watchAllChannelPrivateData();

  /// Watch all user private data changes
  Stream<List<UserPrivateData>> watchAllUserPrivateData();

  /// Watch all call log private data changes
  Stream<List<CallLogPrivateData>> watchAllCallLogPrivateData();

  /// Get user private data by list of user IDs
  List<UserPrivateData> getPrivateDataByIds(List<String> listUserId);

  /// Insert or update user private data
  void insertUserPrivateData(UserPrivateData dUserPrivateData);

  /// Sync delete user private data for deleted users
  Future<void> syncDeleteUserPrivateData(List<DeletedUser> deletedUsers);

  /// Get channel private data by channel ID (moved from UserRepository)
  ChannelPrivateData? getChannel(String channelId);

  /// Update channel private data (moved from UserRepository)
  void updateChannel(ChannelPrivateData channel);

  /// Get maximum sort value for pinned channels (moved from UserRepository)
  int? getMaxSortChannel();

  Future<bool> deleteAllBySessionKey({required String sessionKey});
}
