import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../sources/local/channel_local_data_source.dart';
import '../sources/local/member_local_data_source.dart';
import '../sources/local/message_local_data_source.dart';
import '../sources/remote/channel_remote_data_source.dart';
import '../sources/ws/channel_websocket_data_source.dart';
import '../sources/ws/events/incoming_message_request_accepted_event.dart';
import '../sources/ws/events/incoming_message_request_created_event.dart';

/// Channel-specific data operation implementation
/// Handles Channel entity operations following UserDataOperation patterns
///
/// This follows the Single Responsibility Principle by handling only Channel entities
/// and provides local-first data access with reactive streams and optimistic updates.
@Singleton()
class ChannelDataOperation extends BaseDataOperation<Channel> {
  final ChannelLocalDataSource localSource;
  final ChannelRemoteDataSource remoteSource;
  final ChannelWebSocketDataSource webSocketSource;

  final UserDataOperation _userDataOperation;
  final MemberLocalDataSource _memberLocalDataSource;
  final MessageLocalDataSource _messageLocalDataSource;

  ChannelDataOperation(
    this.localSource,
    this.remoteSource,
    this.webSocketSource,
    this._userDataOperation,
    this._memberLocalDataSource,
    this._messageLocalDataSource,
  ) : super(
          localSource: localSource,
          remoteSource: remoteSource,
          webSocketSource: webSocketSource,
        );

  //region WebSocket Event Handling

  @override
  Future<void> handleRealtimeEvent(DataRouterCloudEvent event) async {
    try {
      switch (event.type) {
        // Channel CRUD events
        case EventType.CHANNEL_CREATED:
          await _handleChannelCreatedEvent(event);
          break;
        case EventType.CHANNEL_UPDATED:
          await _handleChannelUpdatedEvent(event);
          break;
        case EventType.CHANNEL_DELETED:
          await _handleChannelDeletedEvent(event);
          break;

        // Channel Property events
        case EventType.CHANNEL_NAME_UPDATED:
          await _handleChannelNameUpdatedEvent(event);
          break;
        case EventType.CHANNEL_AVATAR_UPDATED:
          await _handleChannelAvatarUpdatedEvent(event);
          break;

        // DM Channel events
        case EventType.CHANNEL_DM_CREATE:
          await _handleDMChannelCreatedEvent(event);
          break;

        case EventType.INCOMING_MESSAGE_REQUEST_CREATED:
          await _handleIncomingMessageRequestCreatedEvent(event);
          break;
        case EventType.INCOMING_MESSAGE_REQUEST_ACCEPTED:
          await _handleIncomingMessageRequestAcceptedEvent(event);
          break;

        default:
          logOperation('Unhandled Channel event type', event.type.toString());
          break;
      }
    } catch (error) {
      logOperation(
        'Error handling realtime event',
        '${event.type} - ${event.id}: $error',
      );
    }
  }

  /// Handle CHANNEL_CREATED event
  Future<void> _handleChannelCreatedEvent(DataRouterCloudEvent event) async {
    try {
      logOperation('Processing CHANNEL_CREATED event', event.id);

      final eventData = ChannelCreatedEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation('Invalid CHANNEL_CREATED event data', event.id);
        return;
      }

      final channel =
          ChannelSerializer.fromV3ChannelCreatedEventData(eventData);
      if (channel != null) {
        await localSource.insert(channel);
        logOperation('✅ Channel created via WebSocket', channel.channelId);
      }
    } catch (e) {
      logOperation('❌ Error handling CHANNEL_CREATED event', '${event.id}: $e');
    }
  }

  /// Handle CHANNEL_UPDATED event
  Future<void> _handleChannelUpdatedEvent(DataRouterCloudEvent event) async {
    try {
      logOperation('Processing CHANNEL_UPDATED event', event.id);

      final eventData = ChannelUpdatedEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation('Invalid CHANNEL_UPDATED event data', event.id);
        return;
      }

      final updatedChannel =
          ChannelSerializer.fromV3ChannelUpdatedEventData(eventData);
      if (updatedChannel != null) {
        // Channel doesn't exist locally, insert as new
        await localSource.insert(updatedChannel);
        logOperation(
          '✅ New channel inserted via WebSocket',
          updatedChannel.channelId,
        );
      }
    } catch (e) {
      logOperation('❌ Error handling CHANNEL_UPDATED event', '${event.id}: $e');
    }
  }

  /// Handle CHANNEL_DELETED event
  Future<void> _handleChannelDeletedEvent(DataRouterCloudEvent event) async {
    try {
      logOperation('Processing CHANNEL_DELETED event', event.id);

      final eventData = ChannelDeletedEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation('Invalid CHANNEL_DELETED event data', event.id);
        return;
      }

      final channelId = eventData.channelId;
      final workspaceId = eventData.workspaceId;
      if (channelId != null && workspaceId != null) {
        deleteLocalDataForChannel(
          workspaceId: workspaceId,
          channelId: channelId,
        );
        logOperation('✅ Channel deleted via WebSocket', channelId);
      }
    } catch (e) {
      logOperation('❌ Error handling CHANNEL_DELETED event', '${event.id}: $e');
    }
  }

  /// Handle CHANNEL_NAME_UPDATED event
  Future<void> _handleChannelNameUpdatedEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation('Processing CHANNEL_NAME_UPDATED event', event.id);

      final eventData = ChannelNameUpdatedEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation('Invalid CHANNEL_NAME_UPDATED event data', event.id);
        return;
      }

      final channelId = eventData.channelId;
      final workspaceId = eventData.workspaceId;
      final newName = eventData.newChannelName;

      if (channelId != null && workspaceId != null && newName != null) {
        final existingChannel = await localSource.getChannel(
          channelId: channelId,
          workspaceId: workspaceId,
        );
        if (existingChannel != null) {
          final updatedChannel = existingChannel.copyWith(
            name: newName,
            updateTime: DateTime.now(),
          );
          await localSource.insert(updatedChannel);
          logOperation(
            '✅ Channel name updated via WebSocket',
            '$channelId: $newName',
          );
        }
      }
    } catch (e) {
      logOperation(
        '❌ Error handling CHANNEL_NAME_UPDATED event',
        '${event.id}: $e',
      );
    }
  }

  /// Handle CHANNEL_AVATAR_UPDATED event
  Future<void> _handleChannelAvatarUpdatedEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation('Processing CHANNEL_AVATAR_UPDATED event', event.id);

      final eventData = event.data as ChannelAvatarUpdatedEventData?;
      if (eventData?.isValid != true) {
        logOperation('Invalid CHANNEL_AVATAR_UPDATED event data', event.id);
        return;
      }

      final channelId = eventData!.channelId;
      final workspaceId = eventData.workspaceId;
      final newAvatar = eventData.newChannelAvatar;

      if (channelId != null && workspaceId != null && newAvatar != null) {
        final existingChannel = await localSource.getChannel(
          channelId: channelId,
          workspaceId: workspaceId,
        );
        if (existingChannel != null) {
          final updatedChannel = existingChannel.copyWith(
            avatar: newAvatar,
            updateTime: DateTime.now(),
          );
          await localSource.insert(updatedChannel);
          logOperation('✅ Channel avatar updated via WebSocket', channelId);
        }
      }
    } catch (e) {
      logOperation(
        '❌ Error handling CHANNEL_AVATAR_UPDATED event',
        '${event.id}: $e',
      );
    }
  }

  /// Handle CHANNEL_DM_CREATE event
  Future<void> _handleDMChannelCreatedEvent(DataRouterCloudEvent event) async {
    try {
      logOperation('Processing CHANNEL_DM_CREATE event', event.id);

      final eventData = event.data as DMChannelCreatedEventData?;
      if (eventData?.isValid != true) {
        logOperation('Invalid CHANNEL_DM_CREATE event data', event.id);
        return;
      }

      final dmChannel =
          ChannelSerializer.fromV3DMChannelCreatedEventData(eventData!);
      if (dmChannel != null) {
        await localSource.insert(dmChannel);
        logOperation('✅ DM Channel created via WebSocket', dmChannel.channelId);
      }
    } catch (e) {
      logOperation(
        '❌ Error handling CHANNEL_DM_CREATE event',
        '${event.id}: $e',
      );
    }
  }

  /// Handle CHANNEL_DM_CREATE event
  /// TODO: Handle DM Channel events data saved but not displayed
  Future<void> _handleIncomingMessageRequestCreatedEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation(
        'Processing INCOMING_MESSAGE_REQUEST_CREATED event',
        event.id,
      );

      final eventData =
          IncomingFriendRequestCreatedEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation(
          'Invalid INCOMING_MESSAGE_REQUEST_CREATED event data',
          event.id,
        );
        return;
      }

      final dmChannel = ChannelSerializer.fromV3Channel(
        eventData.channel!,
        includes: eventData.includes,
      );
      await localSource.insert(dmChannel);
      logOperation(
        '✅ INCOMING_MESSAGE_REQUEST_CREATED via WebSocket',
        dmChannel.channelId,
      );
    } catch (e) {
      logOperation(
        '❌ Error handling INCOMING_MESSAGE_REQUEST_CREATED event',
        '${event.id}: $e',
      );
    }
  }

  Future<void> _handleIncomingMessageRequestAcceptedEvent(
    DataRouterCloudEvent event,
  ) async {
    try {
      logOperation(
        'Processing INCOMING_MESSAGE_REQUEST_ACCEPTED event',
        event.id,
      );

      final eventData =
          IncomingFriendRequestAcceptedEventData.fromJson(event.data);
      if (eventData.isValid != true) {
        logOperation(
          'Invalid INCOMING_MESSAGE_REQUEST_CREATED event data',
          event.id,
        );
        return;
      }

      final dmChannel = ChannelSerializer.fromV3Channel(
        eventData.channel!,
        includes: eventData.includes,
      );
      await localSource.insert(dmChannel);
      logOperation(
        '✅ INCOMING_MESSAGE_REQUEST_ACCEPTED via WebSocket',
        dmChannel.channelId,
      );
    } catch (e) {
      logOperation(
        '❌ Error handling INCOMING_MESSAGE_REQUEST_ACCEPTED event',
        '${event.id}: $e',
      );
    }
  }

  /// Delete all channels for a specific session
  /// This is a Channel-specific operation for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting all channels for session', sessionKey);
      await localSource.deleteAllBySessionKey(sessionKey);
      logOperation('Deleted all channels for session', sessionKey);
    } catch (e) {
      logOperation(
        'Error deleting all channels for session',
        '$sessionKey: $e',
      );
    }
  }

  //endregion

  //region Local Data Source Operations

  /// Insert channel to local storage
  Future<void> insertChannel(Channel channel) async {
    try {
      logOperation(
        'Inserting channel',
        'channelId: ${channel.channelId}, sessionKey: ${channel.sessionKey}',
      );
      await localSource.insert(channel);
      logOperation('✅ Channel inserted successfully');
    } catch (e) {
      logOperation('❌ Error inserting channel', '${channel.channelId}: $e');
    }
  }

  /// Watch channels for a specific workspace
  Stream<List<Channel>> watchChannelsForWorkspace(String workspaceId) {
    logOperation('Watching channels for workspace', workspaceId);
    return localSource.watchChannelsForWorkspace(workspaceId);
  }

  /// Watch specific channel with mutually exclusive validation
  /// Either (workspaceId + channelId) OR recipientId must be non-null
  Stream<Channel?> watchChannel({
    String? channelId,
    String? workspaceId,
    String? recipientId,
  }) {
    // Validate mutually exclusive parameters
    final hasChannel = workspaceId != null &&
        workspaceId.isNotEmpty &&
        channelId != null &&
        channelId.isNotEmpty;
    final hasRecipientId = recipientId != null && recipientId.isNotEmpty;

    logOperation(
      'Watching channel',
      'channelId: $channelId, workspaceId: $workspaceId, recipientId: $recipientId',
    );

    if (!hasChannel && !hasRecipientId) {
      throw Exception(
        'Either (workspaceId + channelId) OR recipientId must be provided',
      );
    }

    if (hasRecipientId) {
      return localSource.watchDMChannel(recipientId);
    }

    return localSource.watchChannel(
      channelId: channelId!,
      workspaceId: workspaceId!,
    );
  }

  /// Watch DM channels
  Stream<List<Channel>> watchDMChannels() {
    logOperation('Watching DM channels');
    return localSource.watchDMChannels();
  }

  /// Watch channels that meet filtering criteria
  /// Returns channels where type == 1 (regular channels) OR (dmStatusRaw == 1 AND userId == sessionKey)
  Stream<List<Channel>> watchChannels() {
    logOperation('Watching filtered channels');
    return localSource.watchChannels();
  }

  /// Watch message requests (pending DM channels from other users)
  /// Returns channels where:
  /// - channelType == dm (direct message)
  /// - dmStatus == pending
  /// - userId != activeSessionKey (exclude current user's own channels)
  Stream<List<Channel>> watchMessageRequests() {
    logOperation('Watching message requests');
    return localSource.watchMessageRequests();
  }

  /// Get channels for a specific workspace
  Future<List<Channel>> getChannelsForWorkspace(String workspaceId) async {
    logOperation('Getting channels for workspace', workspaceId);
    return localSource.getChannelsForWorkspace(workspaceId);
  }

  /// Get DM channels
  Future<List<Channel>> getDMChannels() async {
    logOperation('Getting DM channels');
    return localSource.getDMChannels();
  }

  List<Channel> getChannels({
    int limit = 100,
    int offset = 0,
  }) {
    logOperation('Getting  channels');
    return localSource.getAllChannel(
      limit: limit,
      offset: offset,
    );
  }

  /// Get All channels
  List<Channel> getAllChannels() {
    logOperation('Getting channels');
    return localSource.getAllSync();
  }

  /// Check if channel exists locally
  Future<bool> channelExists(String channelId) async {
    logOperation('Checking channel existence', channelId);
    return localSource.exists(channelId);
  }

  /// Get channel by workspace and channel ID
  Channel? getChannel({
    required String workspaceId,
    required String channelId,
  }) {
    logOperation('Getting channel', '$workspaceId:$channelId');
    return localSource.getChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  /// Get DM channel by recipient ID
  Channel? getDMChannel(String recipientId) {
    logOperation('Getting DM channel', recipientId);
    return localSource.getDMChannel(recipientId: recipientId);
  }

  /// Delete channel from local storage.
  /// This method is used to remove a channel and all related data
  /// from local storage, including members and messages.
  void deleteLocalDataForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    // Delete channel from local storage
    localSource.deleteChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
    // Delete all members of the channel
    _memberLocalDataSource.deleteMembersForChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    // Delete all messages of the channel
    _messageLocalDataSource.deleteMessagesForChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  /// Get all unread channel IDs
  List<String> getUnreadChannelIds() {
    logOperation('Getting unread channel IDs');
    return localSource.getUnreadChannelIds();
  }

  Stream<int> streamHomeBadgeCount() {
    logOperation('Watching home badge count');
    return localSource.streamHomeBadgeCount();
  }

  //endregion

  //region Remote Data Source Operations

  /// Load and save channel data optimistically
  Future<Resource<Channel?>> loadChannel({
    String? channelId,
    String? workspaceId,
    String? recipientId,
  }) async {
    try {
      Channel? channel;
      if (recipientId != null && recipientId.isNotEmpty) {
        // Load DM channel using getDMChannel method
        channel = await remoteSource.getDMChannel(recipientId);
      } else if (workspaceId != null &&
          workspaceId.isNotEmpty &&
          channelId != null &&
          channelId.isNotEmpty) {
        // Load workspace channel using the standard load method
        channel = await remoteSource.getChannel(
          workspaceId: workspaceId,
          channelId: channelId,
        );
      }

      if (channel != null) {
        await localSource.insert(channel);
        logOperation(
          '✅ Channel cached locally',
          'channelId: $channelId, sessionKey: ${channel.sessionKey}',
        );
        return Resource.success(channel, isFromCache: false);
      } else {
        logOperation('⚠️ Channel not found on remote', channelId);
        return Resource.error('Channel not found');
      }
    } catch (e) {
      logOperation('❌ Error loading channel from remote', '$channelId: $e');
      return Resource.error('Error loading channel: $e');
    }
  }

  /// Load all channels with pagination support from remote API
  ///
  /// This method fetches channels from the remote API with pagination
  /// and caches all received channels in local storage for offline access.
  /// Use this for pagination scenarios where you need access to pagination metadata.
  ///
  /// Optional pagination parameters:
  /// - [limit]: Number of items to return per request (default: no limit)
  /// - [nextPageToken]: Token for forward pagination
  /// - [prevPageToken]: Token for backward pagination
  ///
  /// Returns [Resource<PaginatedChannelsResult>] containing both channels and pagination metadata
  Future<Resource<PaginatedChannelsResult>> loadAllChannelsWithPagination({
    int? limit,
    String? nextPageToken,
    String? prevPageToken,
  }) async {
    try {
      logOperation(
        'Loading channels with pagination from remote',
        'limit: $limit, nextPageToken: $nextPageToken, prevPageToken: $prevPageToken',
      );

      final result = await remoteSource.loadAllWithPagination(
        limit: limit,
        nextPageToken: nextPageToken,
        prevPageToken: prevPageToken,
      );

      // Cache all received channels in local storage
      if (result.hasChannels) {
        try {
          await localSource.insertAll(result.channels);
          logOperation(
            '✅ Cached channels to local storage',
            '${result.channelCount} channels cached',
          );
        } catch (localError) {
          // Log the local storage error but don't fail the entire operation
          logOperation(
            '⚠️ Warning: Failed to cache channels locally',
            localError.toString(),
          );
        }
      }

      logOperation(
        '✅ Loaded channels with pagination successfully',
        '${result.channelCount} channels, hasNext: ${result.hasNext}, hasPrev: ${result.hasPrev}',
      );

      return Resource.success(result, isFromCache: false);
    } catch (e) {
      logOperation('❌ Error loading channels with pagination', e.toString());
      return Resource.error('Error loading channels with pagination', e);
    }
  }

  /// Load all message requests with pagination support from remote API
  ///
  /// This method fetches message requests from the remote API with pagination
  /// and caches all received message request channels in local storage for offline access.
  /// Use this for pagination scenarios where you need access to pagination metadata.
  ///
  /// Optional pagination parameters:
  /// - [limit]: Number of items to return per request (default: no limit)
  /// - [nextPageToken]: Token for forward pagination
  /// - [prevPageToken]: Token for backward pagination
  ///
  /// Returns [Resource<PaginatedChannelsResult>] containing both message request channels and pagination metadata
  Future<Resource<PaginatedChannelsResult>> loadAllMessageRequest({
    int? limit,
    String? nextPageToken,
    String? prevPageToken,
  }) async {
    try {
      logOperation(
        'Loading message requests with pagination from remote',
        'limit: $limit, nextPageToken: $nextPageToken, prevPageToken: $prevPageToken',
      );

      final result = await remoteSource.loadAllMessageRequest(
        limit: limit,
        nextPageToken: nextPageToken,
        prevPageToken: prevPageToken,
      );

      // Cache all received message request channels in local storage
      if (result.hasChannels) {
        try {
          await localSource.insertAll(result.channels);
          logOperation(
            '✅ Cached message request channels to local storage',
            '${result.channelCount} message request channels cached',
          );
        } catch (localError) {
          // Log the local storage error but don't fail the entire operation
          logOperation(
            '⚠️ Warning: Failed to cache message request channels locally',
            localError.toString(),
          );
        }
      }

      logOperation(
        '✅ Loaded message requests with pagination successfully',
        '${result.channelCount} message requests, hasNext: ${result.hasNext}, hasPrev: ${result.hasPrev}',
      );

      return Resource.success(result, isFromCache: false);
    } catch (e) {
      logOperation(
        '❌ Error loading message requests with pagination',
        e.toString(),
      );
      return Resource.error(
        'Error loading message requests with pagination',
        e,
      );
    }
  }

  /// Create new channel with optimistic update
  Future<Resource<Channel>> createChannel({
    required String workspaceId,
    required String name,
    String? avatar,
    String? description,
    String? topic,
    List<String>? memberIds,
  }) async {
    try {
      logOperation('Creating channel', 'name: $name, workspace: $workspaceId');

      // Step 1: Optimistic update - create temporary channel locally
      // No optimistic update needed for channel creation

      // Step 2: Call remote API
      final createdChannel = await remoteSource.createChannel(
        workspaceId: workspaceId,
        name: name,
        avatar: avatar,
        description: description,
        topic: topic,
        memberIds: memberIds,
      );

      if (createdChannel != null) {
        await localSource.insert(createdChannel);

        logOperation(
          '✅ Channel created successfully',
          createdChannel.channelId,
        );
        return Resource.success(createdChannel, isFromCache: false);
      } else {
        logOperation('❌ Channel creation failed - rolled back');
        return Resource.error('Failed to create channel');
      }
    } catch (e) {
      logOperation('❌ Error creating channel', e.toString());
      return Resource.error('Error creating channel', e);
    }
  }

  /// Update channel name with optimistic update
  Future<Resource<String>> updateChannelName({
    required String workspaceId,
    required String channelId,
    required String newName,
  }) async {
    try {
      logOperation(
        'Updating channel name',
        'channelId: $channelId, newName: $newName',
      );

      // Step 1: Get current channel
      final currentChannel = await localSource.getChannel(
        channelId: channelId,
        workspaceId: workspaceId,
      );
      if (currentChannel == null) {
        return Resource.error('Channel not found');
      }

      final originalName = currentChannel.name;

      // Step 2: Optimistic update
      final updatedChannel = currentChannel.copyWith(
        name: newName,
        updateTime: DateTime.now(),
      );

      await localSource.insert(updatedChannel);
      logOperation('✅ Optimistic name update completed');

      // Step 3: Call remote API
      final success = await remoteSource.updateChannelName(
        workspaceId: workspaceId,
        channelId: channelId,
        newName: newName,
      );

      if (success) {
        logOperation('✅ Channel name updated successfully');
        return Resource.success(newName);
      } else {
        // Rollback
        final rollbackChannel = currentChannel.copyWith(
          name: originalName,
          updateTime: DateTime.now(),
        );
        await localSource.insert(rollbackChannel);

        logOperation('❌ Channel name update failed - rolled back');
        return Resource.error('Failed to update channel name');
      }
    } catch (e) {
      logOperation('❌ Error updating channel name', e.toString());
      return Resource.error('Error updating channel name', e);
    }
  }

  /// Accept message request with optimistic update
  Future<Resource<bool>> acceptMessageRequest({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      logOperation(
        'Accepting message request',
        'channel: $workspaceId-$channelId',
      );

      // Step 1: Get current channel and validate
      final currentChannel = await localSource.getChannel(
        workspaceId: workspaceId,
        channelId: channelId,
      );
      if (currentChannel == null) {
        return Resource.error('Channel not found');
      }

      // Validate this is a DM channel with pending status
      if (!currentChannel.isDMChannel) {
        return Resource.error('Channel is not a DM channel');
      }

      if (!currentChannel.isDMPending) {
        return Resource.error('Message request is not in pending status');
      }

      // Step 2: Optimistic update - accept the DM request
      currentChannel.dmStatus = DirectMessageStatusEnum.contacted;

      await localSource.insert(currentChannel);
      logOperation('✅ Optimistic message request acceptance completed');

      // Step 3: Call remote API
      // Note: API expects userId (recipient), but we have channelId
      // For DM channels, we need to use the recipient's userId
      final recipientUserId = currentChannel.recipientId;

      if (recipientUserId.isEmpty) {
        // Rollback on invalid recipient
        currentChannel.dmStatus = DirectMessageStatusEnum.pending;
        await localSource.insert(currentChannel);
        return Resource.error('Invalid recipient user ID');
      }

      final success = await remoteSource.acceptMessageRequest(recipientUserId);

      if (success) {
        logOperation('✅ Message request accepted successfully');
        return Resource.success(true);
      } else {
        // Rollback on API failure
        currentChannel.dmStatus = DirectMessageStatusEnum.pending;
        await localSource.insert(currentChannel);

        logOperation('❌ Message request acceptance failed - rolled back');
        return Resource.error('Failed to accept message request');
      }
    } catch (e) {
      logOperation('❌ Error accepting message request', e.toString());
      return Resource.error('Error accepting message request', e);
    }
  }

  Future<Resource<bool>> rejectMessageRequest({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      logOperation(
        'Rejecting message request',
        'channel: $workspaceId-$channelId',
      );

      // Step 1: Get current channel and validate
      final currentChannel = await localSource.getChannel(
        workspaceId: workspaceId,
        channelId: channelId,
      );
      if (currentChannel == null) {
        return Resource.error('Channel not found');
      }
      if (!currentChannel.isDMChannel) {
        return Resource.error('Channel is not a DM channel');
      }
      if (!currentChannel.isDMPending) {
        return Resource.error('Message request is not in pending status');
      }
      final recipientUserId = currentChannel.recipientId;
      if (recipientUserId.isEmpty) {
        return Resource.error('Invalid recipient user ID');
      }

      // Step 2: Optimistic update - reject the DM request
      deleteLocalDataForChannel(
        workspaceId: workspaceId,
        channelId: channelId,
      );

      logOperation('✅ Optimistic message request rejection completed');

      // Step 3: Call remote API
      final success = await remoteSource.rejectMessageRequest(recipientUserId);

      if (success) {
        logOperation('✅ Message request rejected successfully');
        return Resource.success(true);
      } else {
        // Rollback on API failure
        await localSource.insert(currentChannel);

        logOperation('❌ Message request rejection failed - rolled back');
        return Resource.error('Failed to reject message request');
      }
    } catch (e) {
      logOperation('❌ Error rejecting message request', e.toString());
      return Resource.error('Error rejecting message request', e);
    }
  }

  /// Get or create temporary DM channel
  /// Migrated from GetOrCreateTempDMChannelUseCase.buildUseCase
  ///
  /// This method:
  /// 1. Checks if a DM channel already exists for the given userId
  /// 2. If not found, loads user data and creates a temporary DM channel
  /// 3. Returns the existing or newly created channel
  Future<Channel?> getOrCreateTempDMChannel(String userId) async {
    try {
      logOperation('Getting or creating temp DM channel', userId);

      // Step 1: Check if DM channel already exists
      Channel? channel = getDMChannel(userId);

      if (channel == null) {
        logOperation(
          'DM channel not found, creating temporary channel',
          userId,
        );

        // Step 2: Get user data (try local first, then remote)
        final localUser = await _userDataOperation.get(userId).first;

        User? user = localUser.data;
        if (user == null) {
          logOperation('User not found locally, loading from remote', userId);
          final userResult = await _userDataOperation.loadUser(userId);
          if (!userResult.isError || userResult.data != null) {
            user = userResult.data!;
            logOperation('User loaded from remote', userId);
          }
        }

        if (user != null) {
          // Step 3: Create temporary DM channel
          final sessionKey = Config.getInstance().activeSessionKey ?? '';
          final channelId = UUIDUtils.random();

          // Get DM channel name from user profile
          String dmChannelName = '';
          if (user.profile.target != null) {
            final profile = user.profile.target!;
            dmChannelName = profile.displayName.isNotEmpty
                ? profile.displayName
                : user.username.isNotEmpty
                    ? user.username
                    : '';
          } else {
            dmChannelName = user.username.isNotEmpty ? user.username : '';
          }

          // Get avatar from user profile
          String avatarUrl = '';
          if (user.profile.target != null) {
            avatarUrl = user.profile.target!.avatar;
          }

          channel = Channel.create(
            workspaceId: '0',
            channelId: channelId,
            sessionKey: sessionKey,
            channelOwnerUserId: '',
            recipientId: userId,
          )
            ..name = dmChannelName
            ..avatar = avatarUrl
            ..channelTypeRaw = ChannelTypeEnum.dm.value
            ..dmStatusRaw = DirectMessageStatusEnum.contacted.value
            ..isPartial = true; // Use isPartial instead of isTemp

          logOperation('✅ Created temporary DM channel', channelId);
        } else {
          logOperation('❌ User not found, cannot create DM channel', userId);
        }
      } else {
        logOperation('✅ Found existing DM channel', channel.channelId);
      }

      return channel;
    } catch (e) {
      logOperation(
        '❌ Error getting or creating temp DM channel',
        '$userId: $e',
      );
      return null;
    }
  }

  /// Delete channel from remote API and local storage.
  /// This method handles both remote deletion and local cleanup.
  /// It ensures that all related data (members, messages) are also removed.
  Future<Resource<bool>> deleteChannel({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      logOperation('Deleting channel', '$workspaceId - $channelId');

      // Step 1: call remote API to delete channel
      final success = await remoteSource.deleteChannel(
        workspaceId: workspaceId,
        channelId: channelId,
      );

      if (success) {
        // Step 2: Delete channel from local storage
        deleteLocalDataForChannel(
          workspaceId: workspaceId,
          channelId: channelId,
        );

        logOperation('✅ Channel deleted successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to delete channel');
        return Resource.error('Failed to delete channel');
      }
    } catch (e) {
      logOperation('❌ Error deleting channel', e.toString());
      return Resource.error('Error deleting channel', e);
    }
  }

  // CHANNEL PRIVATE DATA OPERATIONS

  /// Insert or update channel private data
  Future<Resource<ChannelPrivateData>> insertChannelPrivateData(
    ChannelPrivateData channelPrivateData,
  ) async {
    try {
      logOperation(
        'Inserting channel private data',
        'channelId: ${channelPrivateData.channelId}, sessionKey: ${channelPrivateData.sessionKey}',
      );
      await localSource.insertChannelPrivateData(channelPrivateData);
      logOperation('✅ Channel private data inserted successfully');
      return Resource.success(channelPrivateData, isFromCache: false);
    } catch (e) {
      logOperation('❌ Error inserting channel private data', e.toString());
      return Resource.error('Error inserting channel private data', e);
    }
  }

  /// Mark channel as read
  Future<Resource<bool>> markChannelAsRead(
    String channelId,
    String messageId,
  ) async {
    try {
      logOperation('Marking channel as read', '$channelId - $messageId');
      final success = await localSource.markChannelAsRead(channelId, messageId);
      if (success) {
        logOperation('✅ Channel marked as read successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to mark channel as read');
        return Resource.error('Failed to mark channel as read');
      }
    } catch (e) {
      logOperation('❌ Error marking channel as read', e.toString());
      return Resource.error('Error marking channel as read', e);
    }
  }

  /// Pin channel
  Future<Resource<bool>> pinChannel(String channelId) async {
    try {
      logOperation('Pinning channel', channelId);
      final success = await localSource.pinChannel(channelId);
      if (success) {
        logOperation('✅ Channel pinned successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to pin channel');
        return Resource.error('Failed to pin channel');
      }
    } catch (e) {
      logOperation('❌ Error pinning channel', e.toString());
      return Resource.error('Error pinning channel', e);
    }
  }

  /// Unpin channel
  Future<Resource<bool>> unpinChannel(String channelId) async {
    try {
      logOperation('Unpinning channel', channelId);
      final success = await localSource.unpinChannel(channelId);
      if (success) {
        logOperation('✅ Channel unpinned successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to unpin channel');
        return Resource.error('Failed to unpin channel');
      }
    } catch (e) {
      logOperation('❌ Error unpinning channel', e.toString());
      return Resource.error('Error unpinning channel', e);
    }
  }

  /// Update channel unread count
  Future<Resource<bool>> updateChannelUnreadCount(
    String channelId,
    int unreadCount,
  ) async {
    try {
      logOperation(
        'Updating channel unread count',
        '$channelId - $unreadCount',
      );
      final success =
          await localSource.updateChannelUnreadCount(channelId, unreadCount);
      if (success) {
        logOperation('✅ Channel unread count updated successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to update channel unread count');
        return Resource.error('Failed to update channel unread count');
      }
    } catch (e) {
      logOperation('❌ Error updating channel unread count', e.toString());
      return Resource.error('Error updating channel unread count', e);
    }
  }

  Future<Map<String, dynamic>> syncAllChannels({
    required String updateTimeAfter,
  }) {
    return remoteSource.syncAllChannels(updateTimeAfter: updateTimeAfter);
  }

  // void updateLastSeenMessageId({
  //   required String workspaceId,
  //   required String channelId,
  //   required String lastSeenMessageId,
  // }) {
  //   return localSource.updateLastSeenMessageId(
  //     workspaceId: workspaceId,
  //     channelId: channelId,
  //     lastSeenMessageId: lastSeenMessageId,
  //   );
  // }

  Future<Resource<String>> updateChannelAvatar({
    required String workspaceId,
    required String channelId,
    required String avatarPath,
  }) async {
    try {
      logOperation(
        'Updating channel avatar',
        'channelId: $channelId, avatar: $avatarPath',
      );

      // Step 1: Get current channel
      final currentChannel = await localSource.getChannel(
        channelId: channelId,
        workspaceId: workspaceId,
      );
      if (currentChannel == null) {
        return Resource.error('Channel not found');
      }

      final originalAvatar = currentChannel.avatar;

      // Step 2: Optimistic update
      final updatedChannel = currentChannel.copyWith(
        avatar: avatarPath,
        updateTime: DateTime.now(),
      );

      await localSource.insert(updatedChannel);
      logOperation('✅ Optimistic avatar update completed');

      // Step 3: Call remote API
      final success = await remoteSource.updateChannelAvatar(
        workspaceId: workspaceId,
        channelId: channelId,
        avatarPath: avatarPath,
      );

      if (success) {
        logOperation('✅ Channel avatar updated successfully');
        return Resource.success(avatarPath);
      } else {
        // Rollback
        final rollbackChannel = currentChannel.copyWith(
          avatar: originalAvatar,
          updateTime: DateTime.now(),
        );
        await localSource.insert(rollbackChannel);

        logOperation('❌ Channel avatar update failed - rolled back');
        return Resource.error('Failed to update channel avatar');
      }
    } catch (e) {
      logOperation('❌ Error updating channel name', e.toString());
      return Resource.error('Error updating channel avatar', e);
    }
  }

  Future<Resource<bool>> deleteChannelAvatar({
    required String workspaceId,
    required String channelId,
  }) async {
    try {
      logOperation(
        'Deleting channel avatar',
        'channelId: $channelId, ',
      );

      // Step 1: Get current channel
      final currentChannel = await localSource.getChannel(
        channelId: channelId,
        workspaceId: workspaceId,
      );
      if (currentChannel == null) {
        return Resource.error('Channel not found');
      }

      final originalAvatar = currentChannel.avatar;

      // Step 2: Optimistic update
      final updatedChannel = currentChannel.copyWith(
        avatar: '',
        updateTime: DateTime.now(),
      );

      await localSource.insert(updatedChannel);
      logOperation('✅ Optimistic avatar update completed');

      // Step 3: Call remote API
      final success = await remoteSource.deleteChannelAvatar(
        workspaceId: workspaceId,
        channelId: channelId,
      );

      if (success) {
        logOperation('✅ Channel avatar updated successfully');
        return Resource.success(true);
      } else {
        // Rollback
        final rollbackChannel = currentChannel.copyWith(
          avatar: originalAvatar,
          updateTime: DateTime.now(),
        );
        await localSource.insert(rollbackChannel);

        logOperation('❌ Channel avatar update failed - rolled back');
        return Resource.error('Failed to update channel avatar');
      }
    } catch (e) {
      logOperation('❌ Error updating channel name', e.toString());
      return Resource.error('Error updating channel avatar', e);
    }
  }

  Future<void> updateChannelNoti({
    required String workspaceId,
    required String channelId,
    required isNotification,
  }) async {
    try {
      await localSource.updateChannelNoti(
        workspaceId: workspaceId,
        channelId: channelId,
        isNotification: isNotification,
      );
    } catch (e) {
      logOperation('Error updating channel noti', '$channelId: $e');
    }
  }

  Future<Resource<bool>> subscribeChannel({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) async {
    try {
      logOperation(
        'Subscribing to channel',
        'workspace: $workspaceId, channel: $channelId, user: $userId',
      );

      // Validate parameters
      if ((StringUtils.isNullOrEmpty(workspaceId) ||
              StringUtils.isNullOrEmpty(channelId)) &&
          StringUtils.isNullOrEmpty(userId)) {
        return Resource.error('Workspace ID is required');
      }

      String? channelIdToUse = channelId;
      String? workspaceIdToUse = workspaceId;
      if (!StringUtils.isNullOrEmpty(userId)) {
        final channel = (await loadChannel(
          recipientId: userId!,
          workspaceId: workspaceId,
          channelId: channelId,
        ))
            .data;
        if (channel != null) {
          channelIdToUse = channel.channelId;
          workspaceIdToUse = channel.workspaceId;
        } else {
          logOperation('Channel not found for user', 'userId: $userId');
          return Resource.error('DM Channel not found for user');
        }
      }

      // Call remote API to subscribe
      final success = await remoteSource.subscribeChannel(
        workspaceId: workspaceIdToUse!,
        channelId: channelIdToUse!,
      );

      if (success) {
        logOperation('✅ Subscribed to channel successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to subscribe to channel');
        return Resource.error('Failed to subscribe to channel');
      }
    } catch (e) {
      logOperation('❌ Error subscribing to channel', e.toString());
      return Resource.error('Error subscribing to channel', e);
    }
  }

  Future<Resource<bool>> unsubscribeChannel({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) async {
    try {
      logOperation(
        'Unsubscribing from channel',
        'workspace: $workspaceId, channel: $channelId, user: $userId',
      );

      // Validate parameters
      if ((StringUtils.isNullOrEmpty(workspaceId) ||
              StringUtils.isNullOrEmpty(channelId)) &&
          StringUtils.isNullOrEmpty(userId)) {
        return Resource.error('Workspace ID is required');
      }

      String? channelIdToUse = channelId;
      String? workspaceIdToUse = workspaceId;
      if (!StringUtils.isNullOrEmpty(userId)) {
        final channel = (await loadChannel(
          recipientId: userId!,
          workspaceId: workspaceId,
          channelId: channelId,
        ))
            .data;
        if (channel != null) {
          channelIdToUse = channel.channelId;
          workspaceIdToUse = channel.workspaceId;
        } else {
          logOperation('Channel not found for user', 'userId: $userId');
          return Resource.error('DM Channel not found for user');
        }
      }

      // Call remote API to unsubscribe
      final success = await remoteSource.unsubscribeChannel(
        workspaceId: workspaceIdToUse!,
        channelId: channelIdToUse!,
      );

      if (success) {
        logOperation('✅ Unsubscribed from channel successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to unsubscribe from channel');
        return Resource.error('Failed to unsubscribe from channel');
      }
    } catch (e) {
      logOperation('❌ Error unsubscribing from channel', e.toString());
      return Resource.error('Error unsubscribing from channel', e);
    }
  }

  Future<Resource<bool>> unsubscribeAll() async {
    try {
      logOperation('Unsubscribing from all channels');

      // Call remote API to unsubscribe from all channels
      final success = await remoteSource.unsubscribeAll();

      if (success) {
        logOperation('✅ Unsubscribed from channel successfully');
        return Resource.success(true, isFromCache: false);
      } else {
        logOperation('❌ Failed to unsubscribe from channel');
        return Resource.error('Failed to unsubscribe from channel');
      }
    } catch (e) {
      logOperation('❌ Error unsubscribing from channel', e.toString());
      return Resource.error('Error unsubscribing from channel', e);
    }
  }

  Future<Resource<Channel?>> acceptInvitation({
    required String invitationLink,
  }) async {
    try {
      final channel = await remoteSource.acceptInvitation(
        invitationLink: invitationLink,
      );

      if (channel != null) {
        await localSource.insert(channel);
        return Resource.success(channel);
      }

      return Resource.success(null);
    } catch (e) {
      logOperation(
        'Error acceptInvitation',
        e.toString(),
      );
      return Resource.error('Error when accept invitation', e);
    }
  }

  Future<Resource<bool>> inviteToChannel({
    required String invitationLink,
    required List<String> userIds,
  }) async {
    try {
      final result = await remoteSource.inviteToChannel(
        invitationLink: invitationLink,
        userIds: userIds,
      );

      return Resource.success(result);
    } catch (e) {
      logOperation(
        'Error inviteToChannel',
        e.toString(),
      );

      throw Exception('Error inviteToChannel');
    }
  }

  Future<Map<String, dynamic>?> getInvitation({required String code}) async {
    return await remoteSource.getInvitation(code: code);
  }

//endregion
}
