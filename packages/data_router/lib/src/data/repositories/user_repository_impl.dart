import 'dart:async';
import 'dart:convert';

import 'package:injectable/injectable.dart' hide Order;
import 'package:rxdart/rxdart.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../core/cache/cache.dart';
import '../../core/metrics/metrics.dart';
import '../../core/models/base_model.dart';
import '../../core/models/related_user_data.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/user_repository.dart';
import '../database/generated/objectbox.g.dart';
import '../helpers/relationship_linker.dart';

@LazySingleton(as: UserRepository)
class UserRepositoryImpl extends BaseRepository<User>
    implements UserRepository {
  final SessionBox<UserPresence> _presenceSessionBox;
  final SessionBox<UserStatus> _statusSessionBox;
  final SessionBox<User> _userSessionBox;
  final SessionBox<Friend> _friendSessionBox;
  final SessionBox<UserPrivateData> _userPrivateDataSessionBox;

  final SessionBox<SessionLocalMetadata> _sessionLocalMetadataSessionBox;
  final SessionBox<Profile> _profileSessionBox;
  final Store _store;

  // Enhanced Cache Management with timestamp validation
  final EnhancedCacheManager<User> _userCacheManager =
      EnhancedCacheManager<User>(1000);
  final EnhancedCacheManager<Profile> _profileCacheManager =
      EnhancedCacheManager<Profile>(500);
  final CacheManager<List<User>> _legacySearchCacheManager =
      CacheManager<List<User>>(100);
  final SearchCacheManager _searchCacheManager = SearchCacheManager(
    expiry: Duration(minutes: 5),
    maxSize: 100,
  );

  // Performance tracking
  final UserRepositoryMetrics _metrics = UserRepositoryMetrics();
  DateTime _lastPerformanceLog = DateTime.now();

  UserRepositoryImpl(
    this._userSessionBox,
    this._presenceSessionBox,
    this._friendSessionBox,
    this._statusSessionBox,
    this._userPrivateDataSessionBox,
    this._sessionLocalMetadataSessionBox,
    this._store,
    this._profileSessionBox,
  ) : super(_userSessionBox);

  /// Generic method for batch entity lookup to reduce code duplication
  Map<String, T> _batchGetEntities<T extends BaseModel>(
    SessionBox<T> box,
    QueryStringProperty<T> property,
    List<String> values,
    String Function(T) keyExtractor,
  ) {
    if (values.isEmpty) return {};
    _metrics.queryCount++;
    final query = box.queryWith(property.oneOf(values)).build();
    final entities = query.find();
    query.close();
    return {for (var entity in entities) keyExtractor(entity): entity};
  }

  /// Batch load all related data for users to optimize relationship linking
  Map<String, RelatedUserData> _loadAllRelatedData(List<String> userIds) {
    if (userIds.isEmpty) return {};

    final stopwatch = Stopwatch()..start();

    // Batch load all related entities in parallel
    final profiles = _batchGetEntities(
      _profileSessionBox,
      Profile_.userIdField,
      userIds,
      (p) => p.userIdField,
    );

    final presences = _batchGetEntities(
      _presenceSessionBox,
      UserPresence_.userId,
      userIds,
      (p) => p.userId,
    );

    final statuses = _batchGetEntities(
      _statusSessionBox,
      UserStatus_.userId,
      userIds,
      (s) => s.userId,
    );

    final privateData = _batchGetEntities(
      _userPrivateDataSessionBox,
      UserPrivateData_.userIdField,
      userIds,
      (p) => p.userIdField,
    );

    stopwatch.stop();
    _metrics.recordOperation('loadAllRelatedData', stopwatch.elapsed);

    return {
      for (final userId in userIds)
        userId: RelatedUserData(
          profile: profiles[userId],
          presence: presences[userId],
          status: statuses[userId],
          privateData: privateData[userId],
        ),
    };
  }

  /// Optimized relationship linking using batch loading
  void _triggerRelationshipLinking(User user) {
    final stopwatch = Stopwatch()..start();

    // Batch load all related data in one operation
    final relatedData = _loadAllRelatedData([user.userId]);
    final userData = relatedData[user.userId];

    if (userData != null) {
      // Link Profile
      RelationshipLinker.linkToOneSync<User, Profile>(
        parent: user,
        toOne: user.profile,
        fetcher: () => userData.profile,
        saver: (p) {
          if (p.profile.target != null && p.profile.target!.id > 0) {
            _profileSessionBox.put(p.profile.target!);
          }
        },
      );

      // Link Presence

      RelationshipLinker.linkToOneSync<User, UserPresence>(
        parent: user,
        toOne: user.presence,
        fetcher: () => userData.presence,
        saver: (p) {
          if (p.presence.target != null && p.presence.target!.id > 0) {
            _presenceSessionBox.put(p.presence.target!);
          }
        },
      );

      // --- Link Status (ToOne) ---
      if (user.isPartial) {
        RelationshipLinker.linkToOneSync<User, UserStatus>(
          parent: user,
          toOne: user.status,
          fetcher: () => userData.status,
          saver: (p) {
            if (p.status.target != null && p.status.target!.id > 0) {
              _statusSessionBox.put(p.status.target!);
            }
          },
        );
      } else if (user.status.target != null && userData.status != null) {
        user.status.target!.id = userData.status!.id;
        _statusSessionBox.put(user.status.target!);
      }

      // Note: privateData relationship has been removed from User entity
      // User data is now handled through direct fields like blocked, aliasName, etc.
      // Sync UserPrivateData fields to User entity atomically
      if (userData.privateData != null) {
        _syncUserPrivateDataAtomic(user, userData.privateData!);
      }
    }

    // Link Friend Data
    _syncFriendData(user);

    stopwatch.stop();
    _metrics.recordOperation('triggerRelationshipLinking', stopwatch.elapsed);
  }

  /// Optimized batch relationship linking for multiple users
  void _triggerBatchRelationshipLinking(List<User> users) {
    if (users.isEmpty) return;

    final stopwatch = Stopwatch()..start();
    final userIds = users.map((u) => u.userId).toList();

    // Batch load all related data for all users
    final allRelatedData = _loadAllRelatedData(userIds);

    // Link relationships for each user
    for (final user in users) {
      final userData = allRelatedData[user.userId];
      if (userData != null) {
        if (userData.profile != null &&
            user.profile.target == null &&
            user.isPartial) {
          user.profile.target = userData.profile;
        } else if (user.profile.target != null && userData.profile != null) {
          user.profile.target!.id = userData.profile!.id;
          _profileSessionBox.put(user.profile.target!);
        }

        if (userData.presence != null && user.presence.target == null) {
          user.presence.target = userData.presence;
        } else if (user.presence.target != null && userData.presence != null) {
          user.presence.target!.id = userData.presence!.id;
          _presenceSessionBox.put(user.presence.target!);
        }

        if (user.status.target != null && userData.status != null) {
          user.status.target!.id = userData.status!.id;
          _statusSessionBox.put(user.status.target!);
        }

        // Note: privateData relationship has been removed from User entity
        // User data is now handled through direct fields like blocked, aliasName, etc.
        // Sync UserPrivateData fields to User entity atomically
        if (userData.privateData != null) {
          _syncUserPrivateDataAtomic(user, userData.privateData!);
        }
      }

      _syncFriendData(user);
    }

    stopwatch.stop();
    _metrics.recordOperation(
      'triggerBatchRelationshipLinking',
      stopwatch.elapsed,
    );
  }

  /// [ATOMIC SYNC] Synchronizes UserPrivateData fields to User entity atomically
  /// Ensures data consistency and prevents partial updates
  void _syncUserPrivateDataAtomic(User user, UserPrivateData userPrivateData) {
    try {
      // Perform atomic field synchronization
      bool hasChanges = false;

      if (user.aliasName != userPrivateData.aliasName) {
        user.aliasName = userPrivateData.aliasName;
        hasChanges = true;
      }

      if (user.blocked != userPrivateData.blocked) {
        user.blocked = userPrivateData.blocked;
        hasChanges = true;
      }

      // Log sync operation for debugging
      if (hasChanges) {
        Log.d(
          name: 'UserRepositoryImpl._syncUserPrivateDataAtomic',
          'Synced UserPrivateData for userId: ${user.userId}, '
          'aliasName: ${userPrivateData.aliasName}, blocked: ${userPrivateData.blocked}',
        );
      }

      // Note: User will be persisted by the calling method
    } catch (e) {
      Log.e(
        name: 'UserRepositoryImpl._syncUserPrivateDataAtomic',
        'Failed to sync UserPrivateData for userId: ${user.userId}, error: $e',
      );
      // Re-throw to ensure calling method handles the error
      rethrow;
    }
  }

  bool _syncFriendData(User user) {
    if (user.chatFriendDataRaw != null && user.chatFriendDataRaw!.isNotEmpty) {
      final json = jsonDecode(user.chatFriendDataRaw!);
      if (json['friendId'] == null) {
        return true;
      }
      final oldFriend = _friendSessionBox
          .getFirstWith(Friend_.participantId.equals(user.userId));
      final friend = FriendSerializer.fromV3Friend(
        V3Friend.fromJson(json),
        sessionKey: Config.getInstance().activeSessionKey,
      )!;

      if (oldFriend != null) {
        friend.id = oldFriend.id;
      }

      friend.friendUser.target = user;
      _friendSessionBox.put(friend);
      return true;
    }

    // If chatFriendDataRaw is null or empty, try to find the friend.
    final friend = _friendSessionBox
        .getFirstWith(Friend_.participantId.equals(user.userId));

    if (friend != null) {
      user.chatFriendDataRaw = jsonEncode(friend);
      return true; // Success.
    }

    return true; // Not found, retry.
  }

  Box<Profile> get _profileBox => _store.box<Profile>();

  Box<VisitedProfile> get _visitedProfileBox => _store.box<VisitedProfile>();

  // Enhanced cache management methods
  /// Update user cache with timestamp validation
  void _updateUserCache(String userId, User user) {
    // Skip caching for partial users
    if (user.isPartial || user.id == 0) return;

    final cached = _userCacheManager.putIfNewer(userId, user);
    if (!cached) {
      // Log when we skip caching due to stale data
      _metrics.recordCacheSkip('user', userId);
    }
  }

  /// Update profile cache with timestamp validation
  void _updateProfileCache(String userId, Profile profile) {
    final cached = _profileCacheManager.putIfNewer(userId, profile);
    if (!cached) {
      // Log when we skip caching due to stale data
      _metrics.recordCacheSkip('profile', userId);
    }
  }

  void _removeFromUserCache(String userId) {
    _userCacheManager.remove(userId);
  }

  void _removeFromProfileCache(String userId) {
    _profileCacheManager.remove(userId);
  }

  /// Check if cached user is newer than database version
  bool _isCachedUserNewer(String userId, User dbUser) {
    return _userCacheManager.isNewerThan(userId, dbUser);
  }

  void _trackTransaction() {
    _metrics.transactionCount++;
  }

  void _logPerformanceMetrics() {
    final now = DateTime.now();
    if (now.difference(_lastPerformanceLog).inMinutes >= 5) {
      _metrics.logMetrics();
      _lastPerformanceLog = now;
    }
  }

  /// Get comprehensive cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'metrics': _metrics.toMap(),
      'userCache': _userCacheManager.getStats(),
      'profileCache': _profileCacheManager.getStats(),
      'legacySearchCache': {
        'size': _legacySearchCacheManager.length,
        'maxSize': _legacySearchCacheManager.maxSize,
      },
    };
  }

  /// Clear all caches (useful for testing or memory management)
  void clearAllCaches() {
    _userCacheManager.clear();
    _profileCacheManager.clear();
    _legacySearchCacheManager.clear();
    _searchCacheManager.clear();
  }

  @override
  int put(User user) {
    // Add existence check to prevent duplicates
    final existingUser = getFirst(User_.userId.equals(user.userId));
    if (existingUser != null) {
      user.id = existingUser.id;
      _keepUserMetadata(user, existingUser);
    }
    _triggerRelationshipLinking(user);

    // Update cache after successful save
    _updateUserCache(user.userId, user);

    final result = super.put(user);

    return result;
  }

  @override
  List<int> putMany(List<User> users) {
    if (users.isEmpty) return [];

    return _store.runInTransaction(TxMode.write, () {
      final stopwatch = Stopwatch()..start();
      _trackTransaction();

      // Prepare data outside transaction scope
      final userIds = users.map((e) => e.userId).toList();
      final existingUsersMap = _getExistingUsersByUserIds(userIds);

      // Process users in memory
      for (final user in users) {
        final existingUser = existingUsersMap[user.userId];
        if (existingUser != null) {
          user.id = existingUser.id;
          _keepUserMetadata(user, existingUser);
        }
      }

      // Optimized batch relationship linking
      _triggerBatchRelationshipLinking(users);

      // Update cache after successful transaction with timestamp validation
      for (final user in users) {
        _updateUserCache(user.userId, user);
        if (user.profile.target != null) {
          _updateProfileCache(user.userId, user.profile.target!);
        }
      }

      // Single batch database operation
      final result = super.putMany(users);

      stopwatch.stop();
      _metrics.recordOperation('putMany', stopwatch.elapsed);
      return result;
    });
  }

  void _keepUserMetadata(User user, User existingUser) {
    if (StringUtils.isNullOrEmpty(user.userConnectLink)) {
      user.userConnectLink = existingUser.userConnectLink;
    }

    if (StringUtils.isNullOrEmpty(user.sipCredentials)) {
      user.sipCredentials = existingUser.sipCredentials;
    }

    if (StringUtils.isNullOrEmpty(user.callToken)) {
      user.callToken = existingUser.callToken;
    }

    if (StringUtils.isNullOrEmpty(user.sipAddress)) {
      user.sipAddress = existingUser.sipAddress;
    }

    if (StringUtils.isNullOrEmpty(user.setting)) {
      user.setting = existingUser.setting;
    }

    if (user.isPartial) {
      user.blocked = existingUser.blocked;
    }

    // Note: privateData relationship has been removed from User entity
    // User data is now handled through direct fields like blocked, aliasName, etc.

    if (user.profile.target == null && existingUser.profile.target != null) {
      user.profile.target = existingUser.profile.target;
    }

    if (user.presence.target == null && existingUser.presence.target != null) {
      user.presence.target = existingUser.presence.target;
    }

    if (user.status.target == null &&
        existingUser.status.target != null &&
        user.isPartial) {
      user.status.target = existingUser.status.target;
    }
  }

  @override
  int insert(User user) {
    return put(user);
  }

  @override
  List<int> insertAll(List<User> users) {
    return putMany(users);
  }

  // Optimized methods using generic _batchGetEntities
  Map<String, Profile> _getExistingProfilesByUserIds(List<String> userIds) {
    return _batchGetEntities(
      _profileSessionBox,
      Profile_.userIdField,
      userIds,
      (p) => p.userIdField,
    );
  }

  // Removed unused methods _getExistingPresencesByUserIds and _getExistingStatusesByUserIds
  // These are now handled by _loadAllRelatedData method

  Map<String, User> _getExistingUsersByUserIds(List<String> userIds) {
    if (userIds.isEmpty) return {};
    _metrics.queryCount++;
    final users = query(User_.userId.oneOf(userIds));
    return {for (var user in users) user.userId: user};
  }

  @override
  User? getUser(String userId) {
    return getFirst(User_.userId.equals(userId));
    // Validate userId is not empty to prevent ObjectBox crash
    // if (userId.trim().isEmpty) {
    //   Log.e('Error: userId is empty in getUser', name: 'UserRepository');
    //   return null;
    // }
    //
    // final stopwatch = Stopwatch()..start();
    //
    // // Check cache first
    // final cachedUser = _userCacheManager.get(userId);
    // if (cachedUser != null) {
    //   // Verify cache freshness by checking database
    //   final dbUser = getFirst(User_.userId.equals(userId));
    //   if (dbUser != null) {
    //     // If cached version is newer or same, return cached
    //     if (_isCachedUserNewer(userId, dbUser)) {
    //       _metrics.cacheHits++;
    //       stopwatch.stop();
    //       _metrics.recordOperation('getUser_cache_hit', stopwatch.elapsed);
    //       _logPerformanceMetrics();
    //       return cachedUser;
    //     } else {
    //       // Database has newer version, update cache and return db version
    //       _updateUserCache(userId, dbUser);
    //       stopwatch.stop();
    //       _metrics.recordOperation('getUser_cache_refresh', stopwatch.elapsed);
    //       _logPerformanceMetrics();
    //       return dbUser;
    //     }
    //   } else {
    //     // User no longer exists in database, remove from cache
    //     _removeFromUserCache(userId);
    //     stopwatch.stop();
    //     _metrics.recordOperation('getUser_cache_invalidate', stopwatch.elapsed);
    //     return null;
    //   }
    // }
    //
    // // Cache miss - query database
    // _metrics.cacheMisses++;
    // final user = getFirst(User_.userId.equals(userId));
    //
    // // Update cache if user found
    // if (user != null) {
    //   _updateUserCache(userId, user);
    // }
    //
    // stopwatch.stop();
    // _metrics.recordOperation('getUser_cache_miss', stopwatch.elapsed);
    // _logPerformanceMetrics();
    // return user;
  }

  @override
  Future<User?> getUserAsync(String userId) async {
    final stopwatch = Stopwatch()..start();

    // Check cache first
    final cachedUser = _userCacheManager.get(userId);
    if (cachedUser != null) {
      // Verify cache freshness by checking database
      final dbUser = getFirst(User_.userId.equals(userId));
      if (dbUser != null) {
        // If cached version is newer or same, return cached
        if (_isCachedUserNewer(userId, dbUser)) {
          _metrics.cacheHits++;
          stopwatch.stop();
          _metrics.recordOperation('getUserAsync_cache_hit', stopwatch.elapsed);
          return cachedUser;
        } else {
          // Database has newer version, update cache and return db version
          _updateUserCache(userId, dbUser);
          stopwatch.stop();
          _metrics.recordOperation(
            'getUserAsync_cache_refresh',
            stopwatch.elapsed,
          );
          return dbUser;
        }
      } else {
        // User no longer exists in database, remove from cache
        _removeFromUserCache(userId);
        stopwatch.stop();
        _metrics.recordOperation(
          'getUserAsync_cache_invalidate',
          stopwatch.elapsed,
        );
        return null;
      }
    }

    // Cache miss - query database asynchronously
    _metrics.cacheMisses++;
    final user = getFirst(User_.userId.equals(userId));

    // Update cache if user found
    if (user != null) {
      _updateUserCache(userId, user);
    }

    stopwatch.stop();
    _metrics.recordOperation('getUserAsync_cache_miss', stopwatch.elapsed);
    return user;
  }

  @override
  List<User> getUsers() {
    return getAll();
  }

  @override
  Future<List<User>> getUsersAsync() async {
    final query = queryBuilder().build();
    final users = await query.findAsync();
    query.close();
    return users;
  }

  @override
  List<User> getUsersBySession(String sessionKey) {
    final query =
        messageBox.box.query(User_.sessionKey.equals(sessionKey)).build();
    final users = query.find();
    query.close();
    return users;
  }

  @override
  Stream<List<User>> getUsersStream() {
    return _userSessionBox.watchAll().map((query) => query.find());
  }

  @override
  Stream<User?> getUserStream(String userId) {
    return watch(User_.userId.equals(userId))
        .map((query) => query.findFirst())
        .distinct()
        .handleError((error) {
      Log.e(
        'Error in getUserStream for $userId: $error',
        name: 'UserRepository',
      );
      return null;
    });
  }

  @override
  Stream<User?> getMeStream() {
    final activeSessionKey = Config.getInstance().activeSessionKey ?? '';
    return _userSessionBox
        .watch(User_.userId.equals(activeSessionKey))
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.findFirst())
        .distinct()
        .handleError((error) {
      return null;
    });
  }

  @override
  Future<void> saveProfile(Profile profile) async {
    await _profileBox.put(profile);
  }

  @override
  Stream<List<User>> getUsersBySessionStream(String sessionKey) {
    return messageBox.box
        .query(User_.sessionKey.equals(sessionKey))
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  List<User> getUsersBySessionKey(String sessionKey) {
    final query =
        messageBox.box.query(User_.sessionKey.equals(sessionKey)).build();
    final users = query.find();
    query.close();
    return users;
  }

  @override
  bool deleteUser(String userId) {
    final user = getFirst(User_.userId.equals(userId));
    if (user != null) {
      final result = remove(user.id);
      if (result) {
        // Remove from cache after successful deletion
        _removeFromUserCache(userId);
        _removeFromProfileCache(userId);
      }
      return result;
    }
    return false;
  }

  @override
  int deleteUsersBySession(String sessionKey) {
    return _store.runInTransaction(TxMode.write, () {
      _trackTransaction();

      // Get users to be deleted for cache cleanup
      final query =
          messageBox.box.query(User_.sessionKey.equals(sessionKey)).build();
      final usersToDelete = query.find();
      final userIds = usersToDelete.map((u) => u.userId).toList();

      // Delete from database
      final count = query.remove();
      query.close();

      // Remove from cache after successful deletion
      for (final userId in userIds) {
        _removeFromUserCache(userId);
        _removeFromProfileCache(userId);
      }

      return count;
    });
  }

  @override
  int deleteAllUsers() {
    final stopwatch = Stopwatch()..start();
    final count = removeAll();

    // Clear all caches after successful deletion
    _userCacheManager.clear();
    _profileCacheManager.clear();
    _searchCacheManager.clear();

    stopwatch.stop();
    _metrics.recordOperation('deleteAllUsers', stopwatch.elapsed);
    return count;
  }

  @override
  bool userExists(String userId) {
    return getUser(userId) != null;
  }

  @override
  int getUserCount() {
    return count();
  }

  @override
  int getUserCountBySession(String sessionKey) {
    final query =
        messageBox.box.query(User_.sessionKey.equals(sessionKey)).build();
    final count = query.count();
    query.close();
    return count;
  }

  @override
  List<User> searchUsersByUsername(String searchTerm) {
    final stopwatch = Stopwatch()..start();

    // Check cache first
    final cachedResults = _searchCacheManager.get(searchTerm);
    if (cachedResults != null) {
      _metrics.cacheHits++;
      stopwatch.stop();
      _metrics.recordOperation(
        'searchUsersByUsername_cache_hit',
        stopwatch.elapsed,
      );
      _logPerformanceMetrics();
      return cachedResults;
    }

    // Cache miss - query database
    _metrics.cacheMisses++;
    final results =
        query(User_.username.contains(searchTerm, caseSensitive: false));

    // Update cache
    _searchCacheManager.put(searchTerm, results);

    stopwatch.stop();
    _metrics.recordOperation(
      'searchUsersByUsername_cache_miss',
      stopwatch.elapsed,
    );
    _logPerformanceMetrics();
    return results;
  }

  @override
  Future<List<User>> searchUsersByUsernameAsync(String searchTerm) async {
    return query(User_.username.contains(searchTerm, caseSensitive: false));
  }

  @override
  List<User> getUsersByType(int userType) {
    return query(User_.userType.equals(userType));
  }

  @override
  Future<void> updateUserLastSeen(String userId, DateTime lastSeen) async {
    final user = getUser(userId);
    if (user != null) {
      user.updateTime = lastSeen;
      put(user);
    }
  }

  @override
  Future<void> updateUserNotificationStatus(String userId, bool enabled) async {
    final user = getUser(userId);
    if (user != null) {
      user.globalNotificationStatus = enabled;
      put(user);
    }
  }

  @override
  List<User> getUsersWithNotificationsEnabled() {
    return query(User_.globalNotificationStatus.equals(true));
  }

  @override
  User createUser({
    required String sessionKey,
    required String userId,
    required String username,
    int userType = 0,
    bool globalNotificationStatus = true,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return User.create(
      sessionKey: sessionKey,
      userId: userId,
      username: username,
      userType: userType,
      globalNotificationStatus: globalNotificationStatus,
      createTime: createTime ?? DateTime.now(),
      updateTime: updateTime ?? DateTime.now(),
    );
  }

  @override
  Future<void> updateUserProfile({
    required String userId,
    String? username,
    int? userType,
    String? userConnectLink,
  }) async {
    final user = getUser(userId);
    if (user != null) {
      if (username != null) user.username = username;
      if (userType != null) user.userType = userType;
      if (userConnectLink != null && userConnectLink.isNotEmpty) {
        user.userConnectLink = userConnectLink;
      }
      user.updateTime = DateTime.now();
      put(user);
    }
  }

  @override
  List<User> getUsersCreatedAfter(DateTime time) {
    return query(User_.createTime.greaterThan(time.millisecondsSinceEpoch));
  }

  @override
  List<User> getUsersUpdatedAfter(DateTime time) {
    return query(User_.updateTime.greaterThan(time.millisecondsSinceEpoch));
  }

  @override
  Future<void> markUserAsPartial(String userId, bool isPartial) async {
    final user = getUser(userId);
    if (user != null) {
      user.isPartial = isPartial;
      put(user);
    }
  }

  @override
  List<User> getPartialUsers() {
    return query(User_.isPartial.equals(true));
  }

  @override
  List<User> getCompleteUsers() {
    return query(User_.isPartial.equals(false));
  }

  @override
  Stream<List<User>> getAllUsersBySetUserIdOnChannelStream(
    Set<String> setUserId,
  ) {
    return watch(User_.userId.oneOf(setUserId.toList()))
        .map((query) => query.find());
  }

  @override
  VisitedProfile? getVisitedProfile(String sessionKey, String visitedUserId) {
    final query = _visitedProfileBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey) &
              VisitedProfile_.visitedUserIdField.equals(visitedUserId),
        )
        .build();
    final visitedProfile = query.findFirst();
    query.close();
    return visitedProfile;
  }

  @override
  int insertVisitedProfile(VisitedProfile visitedProfile) {
    final existing = getVisitedProfile(
      visitedProfile.sessionKey,
      visitedProfile.visitedUserId,
    );
    if (existing != null) {
      visitedProfile.id = existing.id;
    }
    return _visitedProfileBox.put(visitedProfile);
  }

  @override
  List<int> insertVisitedProfiles(List<VisitedProfile> visitedProfiles) {
    if (visitedProfiles.isEmpty) return [];
    final existingProfilesMap = _getExistingVisitedProfiles(visitedProfiles);
    for (final visitedProfile in visitedProfiles) {
      final key =
          '${visitedProfile.sessionKey}_${visitedProfile.visitedUserId}';
      final existing = existingProfilesMap[key];
      if (existing != null) {
        visitedProfile.id = existing.id;
      }
    }
    return _visitedProfileBox.putMany(visitedProfiles);
  }

  @override
  bool deleteVisitedProfile(String sessionKey, String visitedUserId) {
    final visitedProfile = getVisitedProfile(sessionKey, visitedUserId);
    if (visitedProfile != null) {
      return _visitedProfileBox.remove(visitedProfile.id);
    }
    return false;
  }

  @override
  int deleteAllVisitedProfilesBySession(String sessionKey) {
    final query = _visitedProfileBox
        .query(VisitedProfile_.sessionKey.equals(sessionKey))
        .build();
    final count = query.remove();
    query.close();
    return count;
  }

  @override
  Stream<List<VisitedProfile>> getVisitedProfilesStream(String sessionKey) {
    return _visitedProfileBox
        .query(VisitedProfile_.sessionKey.equals(sessionKey))
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map(
          (query) => query.find()
            ..sort(
              (a, b) => (b.updateTime ?? DateTime.now())
                  .compareTo(a.updateTime ?? DateTime.now()),
            ),
        );
  }

  @override
  int getUnreadVisitedProfilesCount(String sessionKey) {
    final query = _visitedProfileBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey) &
              VisitedProfile_.isRead.equals(false),
        )
        .build();
    final count = query.count();
    query.close();
    return count;
  }

  @override
  int markAllVisitedProfilesAsRead(String sessionKey) {
    final query = _visitedProfileBox
        .query(
          VisitedProfile_.sessionKey.equals(sessionKey) &
              VisitedProfile_.isRead.equals(false),
        )
        .build();
    final unreadProfiles = query.find();
    query.close();
    for (final profile in unreadProfiles) {
      profile.markAsRead();
    }
    if (unreadProfiles.isNotEmpty) {
      _visitedProfileBox.putMany(unreadProfiles);
    }
    return unreadProfiles.length;
  }

  @override
  int updateAvatar(String userId, String avatarPath) {
    final profile = _getExistingProfilesByUserIds([userId])[userId] ??
        Profile.create(
          userId: userId,
          sessionKey: Config.getInstance().activeSessionKey ?? '',
        );
    profile.avatar = avatarPath;
    profile.originalAvatar = avatarPath;
    _profileBox.put(profile);

    final user = getUser(userId);
    if (user != null) {
      put(user); // Re-put user to trigger linking
      return user.id;
    }
    return 0;
  }

  @override
  int updateCover(String userId, String coverPath) {
    final profile = _getExistingProfilesByUserIds([userId])[userId] ??
        Profile.create(
          userId: userId,
          sessionKey: Config.getInstance().activeSessionKey ?? '',
        );
    profile.cover = coverPath;
    _profileBox.put(profile);

    final user = getUser(userId);
    if (user != null) {
      put(user); // Re-put user to trigger linking
      return user.id;
    }
    return 0;
  }

  @override
  User? getUserBySessionAndUserId(String userId) {
    return getFirst(User_.userId.equals(userId));
  }

  @override
  Future<void> updateUserWithProfile(User user, Profile profile) async {
    await _profileBox.put(profile);
    put(user);
  }

  @override
  void deleteMyStatus({required User user}) {
    final status = user.status.target;
    if (status != null) {
      _statusSessionBox.remove(status.id);
      user.status.target = null;
      put(user);
    }
  }

  // **[REFACTORED & SIMPLIFIED]** Logic is now much cleaner thanks to @Unique constraint.
  @override
  void updateMyStatus({required User user, required UserStatus userStatus}) {
    // Create a new status object with the correct, trusted information.
    _statusSessionBox.put(userStatus);
    put(user);
  }

  @override
  void updateUserConnectLink(String userId, String link) {
    final user = getUser(userId);
    if (user != null) {
      if (link.isNotEmpty) {
        user.userConnectLink = link;
      }
      put(user);
    }
  }

  @override
  String? getUserConnectLink(String userId) {
    final user = getUser(userId);
    return user?.userConnectLink;
  }

  Map<String, VisitedProfile> _getExistingVisitedProfiles(
    List<VisitedProfile> visitedProfiles,
  ) {
    if (visitedProfiles.isEmpty) return {};
    final Map<String, List<String>> sessionToUserIds = {};
    for (final profile in visitedProfiles) {
      sessionToUserIds
          .putIfAbsent(profile.sessionKey, () => [])
          .add(profile.visitedUserId);
    }
    final Map<String, VisitedProfile> existingProfilesMap = {};
    for (final entry in sessionToUserIds.entries) {
      final sessionKey = entry.key;
      final userIds = entry.value;
      final query = _visitedProfileBox
          .query(
            VisitedProfile_.sessionKey.equals(sessionKey) &
                VisitedProfile_.visitedUserIdField.oneOf(userIds),
          )
          .build();
      final existing = query.find();
      query.close();
      for (final profile in existing) {
        existingProfilesMap['${profile.sessionKey}_${profile.visitedUserId}'] =
            profile;
      }
    }
    return existingProfilesMap;
  }

  @override
  List<User> getManyUsers(List<String> userIds) {
    if (userIds.isEmpty) return [];
    return query(User_.userId.oneOf(userIds));
  }

  @override
  Stream<List<Friend>> watchAllUserStatus() {
    return _friendSessionBox
        .queryWith(Friend_.statusValue.equals(FriendStatusEnum.friend.value))
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  User? getMe() {
    return _userSessionBox
        .getFirstWith(User_.userId.equals(_userSessionBox.activeSessionKey));
  }

  @override
  List<User> getChatUsers() {
    final users = _userSessionBox.getAll();
    return users;
  }

  @override
  SessionLocalMetadata? getSessionMetadata() {
    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null || sessionKey.isEmpty) {
      return null;
    }

    final sessionLocalMetadata = _sessionLocalMetadataSessionBox
        .getFirstWith(SessionLocalMetadata_.sessionKey.equals(sessionKey));

    if (sessionLocalMetadata == null) {
      return null;
    }

    return sessionLocalMetadata;
  }

  @override
  Future<void> deleteChatUser(String userId) async {
    final user = _userSessionBox.getFirstWith(User_.userId.equals(userId));
    if (user != null) {
      _userSessionBox.remove(user.id);
    }
  }

  @override
  Future<void> updateUserUpdateTimeAfter(DateTime syncTime) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null || sessionKey.isEmpty) {
      return;
    }

    final sessionLocalMetadata = _sessionLocalMetadataSessionBox
        .getFirstWith(SessionLocalMetadata_.sessionKey.equals(sessionKey));

    if (sessionLocalMetadata != null) {
      sessionLocalMetadata.userUpdateTimeAfter = syncTime;
      _sessionLocalMetadataSessionBox.put(sessionLocalMetadata);
    } else {
      final newMetadata = SessionLocalMetadata.create(
        sessionKey: sessionKey,
        userUpdateTimeAfter: syncTime,
      );
      _sessionLocalMetadataSessionBox.put(newMetadata);
    }
  }

  @override
  Future<void> deleteChatFriends(List<String> userIds) async {
    _store.runInTransaction(TxMode.write, () {
      _trackTransaction();

      for (final userId in userIds) {
        final friend = _friendSessionBox
            .getFirstWith(Friend_.participantId.equals(userId));
        if (friend != null) {
          _friendSessionBox.remove(friend.id);
        }
      }
    });
  }

  @override
  void syncUserStatus(List<User> users) {
    final stopwatch = Stopwatch()..start();

    // Prepare data outside transaction
    // Note: privateData relationship has been removed from User entity
    // User data is now handled through direct fields like blocked, aliasName, etc.

    _store.runInTransaction(TxMode.write, () {
      _trackTransaction();

      // Only remove statuses for users being synced (memory efficient)
      final userIds = users.map((u) => u.userId).toList();
      if (userIds.isNotEmpty) {
        final query = _statusSessionBox
            .queryWith(
              UserStatus_.userId.oneOf(userIds),
            )
            .build();
        query.remove();
        query.close();
      }

      // Use optimized putMany which already handles caching
      putMany(users);
    });

    stopwatch.stop();
    _metrics.recordOperation('syncUserStatus', stopwatch.elapsed);
  }

  @override
  User? getUserByUserIdAndSessionKey({
    required String userId,
    required String sessionKey,
  }) {
    final user = _store
        .box<User>()
        .query(
          User_.userId.equals(userId) & User_.sessionKey.equals(sessionKey),
        )
        .build()
        .findFirst();

    if (user != null) {
      _updateUserCache(userId, user);
    }

    return user;
  }
}
