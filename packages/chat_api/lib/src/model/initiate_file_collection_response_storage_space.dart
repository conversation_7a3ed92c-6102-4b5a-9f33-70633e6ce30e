//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'initiate_file_collection_response_storage_space.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class InitiateFileCollectionResponseStorageSpace {
  /// Returns a new [InitiateFileCollectionResponseStorageSpace] instance.
  InitiateFileCollectionResponseStorageSpace({
    this.usedStorage,
    this.remainingStorage,
  });

  /// The amount of storage that is currently being used.
  @JsonKey(
    name: r'usedStorage',
    required: false,
    includeIfNull: false,
  )
  final double? usedStorage;

  /// The amount of storage that is still available for use.
  @JsonKey(
    name: r'remainingStorage',
    required: false,
    includeIfNull: false,
  )
  final double? remainingStorage;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InitiateFileCollectionResponseStorageSpace &&
          other.usedStorage == usedStorage &&
          other.remainingStorage == remainingStorage;

  @override
  int get hashCode => usedStorage.hashCode + remainingStorage.hashCode;

  factory InitiateFileCollectionResponseStorageSpace.fromJson(
          Map<String, dynamic> json) =>
      _$InitiateFileCollectionResponseStorageSpaceFromJson(json);

  Map<String, dynamic> toJson() =>
      _$InitiateFileCollectionResponseStorageSpaceToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
