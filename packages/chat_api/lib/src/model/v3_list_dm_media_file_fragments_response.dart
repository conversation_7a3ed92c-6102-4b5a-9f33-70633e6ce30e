//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_dm_media_file_fragments_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListDMMediaFileFragmentsResponse {
  /// Returns a new [V3ListDMMediaFileFragmentsResponse] instance.
  V3ListDMMediaFileFragmentsResponse({
    this.ok,
    this.data,
    this.error,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<String>? data;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListDMMediaFileFragmentsResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3ListDMMediaFileFragmentsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListDMMediaFileFragmentsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListDMMediaFileFragmentsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
