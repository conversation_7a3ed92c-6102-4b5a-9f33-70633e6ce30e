//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_avatar_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_avatar_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserAvatarUpdatedEventData {
  /// Returns a new [V3UserAvatarUpdatedEventData] instance.
  V3UserAvatarUpdatedEventData({
    this.actorId,
    this.avatar,
    this.videoAvatar,
    this.avatarType,
  });

  @Json<PERSON>ey(
    name: r'actorId',
    required: false,
    includeIfNull: false,
  )
  final String? actorId;

  @JsonKey(
    name: r'avatar',
    required: false,
    includeIfNull: false,
  )
  final String? avatar;

  @J<PERSON><PERSON><PERSON>(
    name: r'videoAvatar',
    required: false,
    includeIfNull: false,
  )
  final String? videoAvatar;

  @J<PERSON><PERSON><PERSON>(
    name: r'avatarType',
    required: false,
    includeIfNull: false,
  )
  final V3UserAvatarTypeEnum? avatarType;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserAvatarUpdatedEventData &&
          other.actorId == actorId &&
          other.avatar == avatar &&
          other.videoAvatar == videoAvatar &&
          other.avatarType == avatarType;

  @override
  int get hashCode =>
      actorId.hashCode +
      avatar.hashCode +
      videoAvatar.hashCode +
      avatarType.hashCode;

  factory V3UserAvatarUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserAvatarUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserAvatarUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
