import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart' as chat;
import '../../../common/di/di.dart';
import '../../../domain/usecase/chat_user/get_me_use_case.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class LinkFullscreenWidgetImpl extends BaseFullscreenWidget {
  LinkFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    this.messageContent,
    this.quoteMessageItem,
    this.onQuoteMessageClicked,
    this.onButtonTranslateClick,
    this.hasShimmerEffect = false,
    super.key,
  });

  final String? messageContent;
  final QuoteMessage? quoteMessageItem;
  final void Function()? onQuoteMessageClicked;
  final void Function(MessageItem messageItem)? onButtonTranslateClick;
  final bool hasShimmerEffect;

  @override
  Widget build(BuildContext context) {
    var embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    var embedData = embed?.embedData;

    var linkMessage = LinkMessage(
      messageContent: messageContent ?? message.content!,
      title: embedData?.title,
      description: embedData?.description,
      imageUrl: embedData?.thumbnailUrl,
    );

    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    void onClickLink(String link) {
      AppEventBus.publish(
        OnLinkClickedEvent(
          link: link,
          workspaceId: message.workspaceId,
          channelId: message.channelId,
          userId: message.userId,
          messageId: message.messageId,
        ),
      );
    }

    final translateButtonStatus =
        messageItem.translateButtonStatus != TranslateButtonStatus.active;

    return ViewFullScreenLinkWidget(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      quickReact: quickReact,
      mentions: [],
      totalReactions: ValueNotifier(totalReactions),
      linkMessage: linkMessage,
      quoteMessage: quoteMessageItem,
      onQuoteClicked: onQuoteMessageClicked,
      mentionRegex: GlobalConfig.mentionRegex,
      onUsernameClicked: (String username) {
        onUsernameClicked(context, username);
      },
      onLinkClicked: onClickLink,
      onOpenLink: (MessageItem messageItem) {
        onClickLink(embedData!.url!);
      },
      hasShimmerEffect: hasShimmerEffect,
      translateButtonStatus: translateButtonStatus,
      transitionStatus: messageItem.translateButtonStatus,
    );
  }

  @override
  void onTranslateButtonClicked() {
    onButtonTranslateClick?.call(messageItem);
  }

  void onClickLink(String link) {
    AppEventBus.publish(
      OnLinkClickedEvent(
        link: link,
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        userId: message.userId,
        messageId: message.messageId,
      ),
    );
  }
}
