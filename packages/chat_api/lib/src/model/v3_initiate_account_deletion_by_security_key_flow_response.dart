//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_request_account_deletion_key.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_account_deletion_by_security_key_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateAccountDeletionBySecurityKeyFlowResponse {
  /// Returns a new [V3InitiateAccountDeletionBySecurityKeyFlowResponse] instance.
  V3InitiateAccountDeletionBySecurityKeyFlowResponse({
    this.ok,
    this.error,
    this.data,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @Json<PERSON>ey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final V3RequestAccountDeletionKey? data;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateAccountDeletionBySecurityKeyFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.data == data;

  @override
  int get hashCode => ok.hashCode + error.hashCode + data.hashCode;

  factory V3InitiateAccountDeletionBySecurityKeyFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateAccountDeletionBySecurityKeyFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateAccountDeletionBySecurityKeyFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
