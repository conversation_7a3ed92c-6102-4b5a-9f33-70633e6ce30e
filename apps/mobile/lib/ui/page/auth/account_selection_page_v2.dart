import 'package:app_core/core.dart' as core;
import 'package:auth/auth.dart' as auth hide Session;
import 'package:auto_route/auto_route.dart';
import 'package:data_router/data_router.dart' show Session, UserDataOperation;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../common/di/di.dart';
import '../../../domain/handler/app_initializer.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class AccountSelectionPageV2 extends StatefulWidget {
  const AccountSelectionPageV2({super.key});

  @override
  AccountSelectionPageV2State createState() => AccountSelectionPageV2State();
}

class AccountSelectionPageV2State extends core
    .BasePageState<AccountSelectionPageV2, auth.InitialUserKeyAuthBloc> {
  late final auth.UserSelectionBloc _userSelectionBloc;
  late final UserDataOperation _userDataOperation;

  @override
  void initState() {
    super.initState();

    _userDataOperation = getIt<UserDataOperation>();
    _initializeBlocs();
  }

  void _initializeBlocs() {
    _userSelectionBloc = getIt<auth.UserSelectionBloc>();
    _userSelectionBloc.add(auth.InitiateUserSelectionEvent());
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: _buildBlocProviders(),
      child: BlocListener<auth.InitialUserKeyAuthBloc,
          auth.InitialUserKeyAuthState>(
        listener: _authBlocListener,
        child: BlocBuilder<auth.UserSelectionBloc, auth.UserSelectionState>(
          builder: (context, state) => _buildUserSelectionState(state),
        ),
      ),
    );
  }

  List<BlocProvider> _buildBlocProviders() {
    return [
      BlocProvider<auth.InitialUserKeyAuthBloc>.value(value: bloc),
      BlocProvider<auth.UserSelectionBloc>.value(value: _userSelectionBloc),
    ];
  }

  void _authBlocListener(
    BuildContext context,
    auth.InitialUserKeyAuthState state,
  ) {
    state.maybeWhen(
      goToAuthProgressPage: (username, challenge, req, hashCash, isLogin) {
        getIt<AppRouter>().push(
          AuthProgressRoute(
            username: username,
            reqChallenge: challenge,
            request: req,
            hashCash: hashCash,
            backStack: AccountSelectionRouteV2.name,
            isLogin: isLogin,
          ),
        );
      },
      authSuccess: (String sessionKey, String authToken) {
        _onAuthSuccess(sessionKey, authToken);
      },
      orElse: () {},
    );
  }

  Widget _buildUserSelectionState(auth.UserSelectionState state) {
    return state.when(
      initial: () => const SizedBox.shrink(),
      loaded: (sessions) => _buildLoadedState(sessions),
      error: (error) => Center(child: Text(error)),
    );
  }

  Widget _buildLoadedState(List<Session> sessions) {
    if (sessions.isEmpty) {
      getIt<AppRouter>().replace(AuthRoute());
      return SizedBox.shrink();
    }
    final sortedSessions = _getSortedSessions(sessions);
    final _users = _mapSessionsToUsers(sortedSessions);

    return auth.AccountSelectionPage(
      accounts: _users,
      onUseAnotherAccount: () => getIt<AppRouter>().replace(const AuthRoute()),
      onDelete: (userId) =>
          _userSelectionBloc.add(auth.DeleteUserEvent(userId)),
    );
  }

  List<Session> _getSortedSessions(List<Session> sessions) {
    return List.from(sessions)
      ..sort((a, b) {
        if (a.logoutTime == null && b.logoutTime == null) return 0;
        if (a.logoutTime == null) return -1;
        if (b.logoutTime == null) return 1;
        return b.logoutTime!.compareTo(a.logoutTime!);
      });
  }

  List<ItemAccountAccountSelection> _mapSessionsToUsers(
    List<Session> sortedSessions,
  ) {
    return sortedSessions
        .map((session) {
          // final user = session.associatedUser;
          final user = _userDataOperation.getUserByIdAndSessionKey(
            sessionKey: session.sessionKey,
            userId: session.sessionKey,
          );
          return user != null
              ? ItemAccountAccountSelection(
                  id: user.userId,
                  name: user.username,
                  url: user.profile.target != null
                      ? UrlUtils.parseAvatar(user.profile.target!.avatar)
                      : null,
                )
              : null;
        })
        .whereType<ItemAccountAccountSelection>()
        .toList();
  }

  void _onAuthSuccess(String sessionKey, String token) {
    getIt<AppInitializer>().initModules(sessionKey, token);
    getIt<InitialUserBloc>()
        .add(InitialUser(userId: sessionKey, token: sessionKey));
    _goToHomePage();
  }

  Future<void> _goToHomePage() async {
    getIt<AppRouter>().replaceAll([HomeRoute()]);
  }
}
