//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_view.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_visited_profile_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserVisitedProfileEventData {
  /// Returns a new [V3UserVisitedProfileEventData] instance.
  V3UserVisitedProfileEventData({
    this.userId,
    this.userData,
    this.createTime,
    this.updateTime,
  });

  @Json<PERSON>ey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'userData',
    required: false,
    includeIfNull: false,
  )
  final V3UserView? userData;

  @J<PERSON><PERSON><PERSON>(
    name: r'createTime',
    required: false,
    includeIfNull: false,
  )
  final String? createTime;

  @J<PERSON><PERSON><PERSON>(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserVisitedProfileEventData &&
          other.userId == userId &&
          other.userData == userData &&
          other.createTime == createTime &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      userId.hashCode +
      userData.hashCode +
      createTime.hashCode +
      updateTime.hashCode;

  factory V3UserVisitedProfileEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserVisitedProfileEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserVisitedProfileEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
