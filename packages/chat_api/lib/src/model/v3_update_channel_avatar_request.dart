//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_channel_avatar_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateChannelAvatarRequest {
  /// Returns a new [V3UpdateChannelAvatarRequest] instance.
  V3UpdateChannelAvatarRequest({
    this.workspaceId,
    this.channelId,
    this.avatarPath,
  });

  @J<PERSON><PERSON><PERSON>(
    name: r'workspaceId',
    required: false,
    includeIfNull: false,
  )
  final String? workspaceId;

  @JsonKey(
    name: r'channelId',
    required: false,
    includeIfNull: false,
  )
  final String? channelId;

  @JsonKey(
    name: r'avatarPath',
    required: false,
    includeIfNull: false,
  )
  final String? avatarPath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateChannelAvatarRequest &&
          other.workspaceId == workspaceId &&
          other.channelId == channelId &&
          other.avatarPath == avatarPath;

  @override
  int get hashCode =>
      workspaceId.hashCode + channelId.hashCode + avatarPath.hashCode;

  factory V3UpdateChannelAvatarRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateChannelAvatarRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateChannelAvatarRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
