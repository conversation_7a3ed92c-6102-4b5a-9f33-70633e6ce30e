//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'common_public_key_credential_descriptor.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class CommonPublicKeyCredentialDescriptor {
  /// Returns a new [CommonPublicKeyCredentialDescriptor] instance.
  CommonPublicKeyCredentialDescriptor({
    this.type,
    this.id,
    this.transports,
  });

  /// The type of the public key credential.
  @Json<PERSON>ey(
    name: r'type',
    required: false,
    includeIfNull: false,
  )
  final String? type;

  /// The identifier associated with the public key credential.
  @JsonKey(
    name: r'id',
    required: false,
    includeIfNull: false,
  )
  final String? id;

  /// The transports used during the attestation process.
  @JsonKey(
    name: r'transports',
    required: false,
    includeIfNull: false,
  )
  final List<String>? transports;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonPublicKeyCredentialDescriptor &&
          other.type == type &&
          other.id == id &&
          other.transports == transports;

  @override
  int get hashCode => type.hashCode + id.hashCode + transports.hashCode;

  factory CommonPublicKeyCredentialDescriptor.fromJson(
          Map<String, dynamic> json) =>
      _$CommonPublicKeyCredentialDescriptorFromJson(json);

  Map<String, dynamic> toJson() =>
      _$CommonPublicKeyCredentialDescriptorToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
