//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/user_setting_session_expiration_setting.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_session_expiration_setting_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateSessionExpirationSettingRequest {
  /// Returns a new [V3UpdateSessionExpirationSettingRequest] instance.
  V3UpdateSessionExpirationSettingRequest({
    this.sessionExpiration,
  });

  @JsonKey(
    name: r'sessionExpiration',
    required: false,
    includeIfNull: false,
  )
  final UserSettingSessionExpirationSetting? sessionExpiration;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateSessionExpirationSettingRequest &&
          other.sessionExpiration == sessionExpiration;

  @override
  int get hashCode => sessionExpiration.hashCode;

  factory V3UpdateSessionExpirationSettingRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateSessionExpirationSettingRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateSessionExpirationSettingRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
