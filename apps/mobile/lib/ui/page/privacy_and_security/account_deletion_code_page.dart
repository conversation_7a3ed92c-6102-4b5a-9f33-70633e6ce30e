import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' as core;
import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import 'package:data_router/data_router.dart' as data;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:passkeys/authenticator.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../navigation/routes/app_router.dart';

@RoutePage()
class AccountDeletionCodePage extends StatefulWidget {
  const AccountDeletionCodePage({super.key, this.securityKey});

  final String? securityKey;

  @override
  State<AccountDeletionCodePage> createState() =>
      _AccountDeletionCodeScreenState();
}

class _AccountDeletionCodeScreenState
    extends core.BasePageState<AccountDeletionCodePage, SecurityKeyBloc> {
  String _securityKey = '';
  final AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();
  Timer? _debounceTimer;

  double alterPaddingBottom(BuildContext context) =>
      MediaQuery.of(context).padding.bottom + 100.h;

  @override
  void initState() {
    super.initState();
    bloc.add(const ResetSecurityKeyRequested());
    _securityKey = widget.securityKey ?? '';
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<SecurityKeyBloc, SecurityKeyState>(
      buildWhen: (prev, current) => current != prev,
      builder: (context, state) {
        state.maybeWhen(
          generateSecurityKeySuccess: _onGenerateSuccess,
          generateSecurityKeyFailure: _onError,
          orElse: () {},
        );
        return ui.AccountDeletionCodePage(
          code: _securityKey,
          onTapGenerate: _onTapGenerate,
          onTapCopy: _onTapCopy,
          onTapDownload: _onTapDownload,
          onClickBack: _onClickBack,
        );
      },
    );
  }

  void _onTapGenerate() async {
    if (_debounceTimer?.isActive ?? false) return;

    _debounceTimer = Timer(Duration(milliseconds: 500), () {});
    bool noConnect = getIt<core.NetworkManager>().noConnection();
    if (noConnect) {
      SnackBarOverlayHelper().showSnackBar(
        widgetBuilder: (T) {
          return ui.SnackBarUtilV2.showFloatingSnackBar(
            context: context,
            content: appLocalizations.noNetworkConnection,
            snackBarType: ui.SnackBarType.warning,
          );
        },
      );
      return;
    }

    final passkeyMigrated =
        getIt<data.SessionDataOperation>().getActiveSession()!.passkeyMigrated;
    if (!(await canAuthenticate()) ||
        !(await canAuthenticateWithWebAPI(passkeyMigrated))) {
      await _showAuthenticateNoSupportDialog();
      return;
    }

    bloc.add(const GenerateSecurityKeyRequested());
    FocusScope.of(context).unfocus();
    return;
  }

  void _onTapCopy() {
    if (_securityKey.isEmpty) {
      return;
    }
    Clipboard.setData(ClipboardData(text: _securityKey));
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showCopied(
          context,
          appLocalizations: appLocalizations,
        );
      },
    );
  }

  void _onTapDownload() async {
    final isGranted =
        await PermissionUtils.requestImageAndVideoPermission(context);
    if (!isGranted) return;
    try {
      var dateString = TimeUtils.format(DateTime.now(), 'yyyyMMdd_hhmmssSSS');
      var fileName = 'ZiiChat-account-deletion-code_${dateString}.txt';
      final fileContent = _securityKey;
      Directory? directory;
      if (Platform.isAndroid) {
        directory = Directory('/storage/emulated/0/Download');
      } else if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      }
      if (directory == null || !(await directory.exists())) {
        return;
      }
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);
      await file.writeAsString(fileContent);
      SnackBarOverlayHelper().showSnackBar(
        widgetBuilder: (T) {
          return ui.SnackBarUtilV2.showDownloadedSuccess(
            context,
            appLocalizations: appLocalizations,
          );
        },
      );
    } catch (e) {
      SnackBarOverlayHelper().showSnackBar(
        widgetBuilder: (T) {
          return ui.SnackBarUtilV2.showFloatingSnackBar(
            content: GetIt.I<AppLocalizations>().anErrorOccurredPleaseTryAgain,
            context: context,
            snackBarType: ui.SnackBarType.danger,
          );
        },
      );

      Log.e('Error downloading file: $e');
    }
  }

  void _onClickBack() {
    context.router.maybePop();
  }

  void _onGenerateSuccess(String securityKey) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _securityKey = securityKey;
      });
      bloc.add(const ResetSecurityKeyRequested());
    });
  }

  void _onError(String? error) {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showFloatingSnackBar(
          content: GetIt.I<AppLocalizations>().anErrorOccurredPleaseTryAgain,
          context: context,
          snackBarType: ui.SnackBarType.danger,
        );
      },
    );

    bloc.add(const ResetSecurityKeyRequested());
  }

  Future<void> _showAuthenticateNoSupportDialog() async {
    ui.DialogUtils.showAuthenticationNotSupportDialog(
      context,
      onOpenSettings: () {
        PasskeyAuthenticator().goToSettings();
      },
      onCancel: () {
        GetIt.I.get<AppRouter>().maybePop();
      },
      isIos: Platform.isIOS,
      hasFaceId: await hasFaceId(),
    );
  }
}
