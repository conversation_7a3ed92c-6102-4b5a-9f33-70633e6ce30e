import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

// Import enums
import '../../../../data_router.dart';
// Import base model
import '../../../core/models/base_model.dart';

part 'channel.g.dart';

/// Channel entity for storing communication channel information
/// UID range: 3001-3030 (expanded for new fields)
@Entity()
@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
  includeIfNull: false,
)
class Channel extends BaseModel {
  /// ObjectBox internal ID
  @Id(assignable: true)
  int id = 0;

  @Unique(onConflict: ConflictStrategy.replace)
  @Property(uid: 3031)
  @JsonKey(includeFromJson: false, includeToJson: false)
  @Index()
  String uniqueId = '';

  /// Workspace ID (indexed) - matches V3Channel.workspaceId
  @Index()
  @Property(uid: 3001)
  String workspaceId = '';

  /// Unique channel ID (indexed) - matches V3Channel.channelId
  @Index()
  @Property(uid: 3002)
  String channelId = '';

  /// Session key reference (indexed) - kept for backward compatibility
  @Index()
  @Property(uid: 3003)
  String sessionKeyField = '';

  /// Channel creator/owner user ID (indexed) - matches V3Channel.userId
  @Index()
  @Property(uid: 3004)
  String userId = '';

  /// Channel name - matches V3Channel.name
  @Property(uid: 3007)
  String name = '';

  /// Channel avatar URL - matches V3Channel.avatar
  @Property(uid: 3008)
  String avatar = '';

  /// Original avatar URL - matches V3Channel.originalAvatar
  @Property(uid: 3009)
  String originalAvatar = '';

  /// Channel type (raw integer) - matches V3Channel.type
  @Property(uid: 3010)
  int channelTypeRaw = 1; // Default to channel type

  /// Channel topic/description - matches V3Channel description concept
  @Property(uid: 3011)
  String topic = '';

  /// Channel description - additional field for extended description
  @Property(uid: 3012)
  String description = '';

  /// Channel creation time as ISO8601 string for database storage
  @Property(uid: 3013)
  String _createTimeString = '';

  /// Channel last update time as ISO8601 string for database storage
  @Property(uid: 3014)
  String _updateTimeString = '';

  /// Channel creation time as DateTime - matches V3Channel.createTime
  @override
  DateTime? get createTime {
    if (_createTimeString.isEmpty) return null;
    try {
      return TimeUtils.parseUTCStringToDateTime(_createTimeString);
    } catch (e) {
      return null;
    }
  }

  @override
  set createTime(DateTime? value) {
    _createTimeString = value?.toIso8601String() ?? '';
  }

  /// Channel last update time as DateTime - matches V3Channel.updateTime
  @override
  DateTime? get updateTime {
    if (_updateTimeString.isEmpty) return null;
    try {
      return TimeUtils.parseUTCStringToDateTime(_updateTimeString);
    } catch (e) {
      return null;
    }
  }

  @override
  set updateTime(DateTime? value) {
    _updateTimeString = value?.toIso8601String() ?? '';
  }

  /// Get createTime as String for serialization
  String get createTimeString => _createTimeString;

  /// Set createTime as String for serialization
  set createTimeString(String value) => _createTimeString = value;

  /// Get updateTime as String for serialization
  String get updateTimeString => _updateTimeString;

  /// Set updateTime as String for serialization
  set updateTimeString(String value) => _updateTimeString = value;

  /// Whether channel is archived - internal field
  @Property(uid: 3015)
  bool isArchived = false;

  /// Whether this is a partial channel record - internal field
  @Property(uid: 3016)
  bool isPartial = false;

  /// Whether channel is private - matches V3Channel.isPrivate
  @Property(uid: 3017)
  bool isPrivate = false;

  /// Invitation link - matches V3Channel.invitationLink
  @Property(uid: 3018)
  String invitationLink = '';

  /// Total members count - matches V3Channel.totalMembers
  @Property(uid: 3019)
  int totalMembers = 0;

  /// Direct message status (raw integer) - matches V3Channel.dmStatus
  @Property(uid: 3020)
  int dmStatusRaw = 0;

  /// Participant IDs as comma-separated string - matches V3Channel.participantIds
  @Property(uid: 3021)
  String participantIdsString = '';

  /// Reject time as ISO8601 string - matches V3Channel.rejectTime
  @Property(uid: 3022)
  String rejectTime = '';

  /// Accept time as ISO8601 string - matches V3Channel.acceptTime
  @Property(uid: 3023)
  String acceptTime = '';

  /// Legacy fields for backward compatibility
  /// Channel owner user ID (indexed, FIXED) - kept for backward compatibility
  @Index()
  @Property(uid: 3024)
  String channelOwnerUserIdField = '';

  /// DM channel ID - kept for backward compatibility
  @Property(uid: 3025)
  String dmChannelId = '';

  /// Recipient ID (indexed, FIXED) - kept for backward compatibility
  @Index()
  @Property(uid: 3026)
  String recipientIdField = '';

  @Property(uid: 3027, type: PropertyType.date)
  @JsonKey(includeFromJson: false, includeToJson: false)
  DateTime? lastMessageCreateTime;

  @Property(uid: 3028)
  String participantId = '';

  @Property(uid: 3029)
  bool pinned = false;

  @Property(uid: 3030)
  int sort = -1;

  /// Alias name for the recipient user (denormalized from UserPrivateData)
  @Property(uid: 3031)
  String aliasName = '';

  // RELATIONSHIPS

  /// ToOne relationship to User (recipient)
  /// Links this channel to its recipient user (for DM channels)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final recipient = ToOne<User>();

  /// ToOne relationship to ChannelMetadata
  /// Links this channel to its metadata (unread count, last message, etc.)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final metadata = ToOne<ChannelMetadata>();

  @JsonKey(includeFromJson: false, includeToJson: false)
  bool? get notificationStatus => metadata.target?.notificationStatus;

  /// Reverse relationship: ChannelPrivateData entities that reference this channel
  /// Links to all private data records for this channel
  @JsonKey(includeFromJson: false, includeToJson: false)
  final privateData = ToOne<ChannelPrivateData>();

  /// Reverse relationship: ChannelPrivateData entities that reference this channel
  /// Links to all private data records for this channel
  @JsonKey(includeFromJson: false, includeToJson: false)
  final localMetadata = ToOne<ChannelLocalMetadata>();

  /// Reverse relationship: Member entities that reference this channel
  /// Links to all members of this channel
  @Backlink('channel')
  @JsonKey(includeFromJson: false, includeToJson: false)
  final members = ToMany<Member>();

  /// Reverse relationship: Message entities that reference this channel
  /// Links to all messages in this channel
  @Backlink('channel')
  @JsonKey(includeFromJson: false, includeToJson: false)
  final messages = ToMany<Message>();

  /// Get sessionKey from relationship or field (backward compatibility)
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  String get sessionKey => sessionKeyField;

  /// Set sessionKey (updates the field for backward compatibility)
  @override
  set sessionKey(String value) => sessionKeyField = value;

  /// Get channelOwnerUserId from relationship or field (backward compatibility)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String get channelOwnerUserId => channelOwnerUserIdField;

  /// Get recipientId from relationship or field (backward compatibility)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String get recipientId => recipient.target?.userId ?? recipientIdField;

  /// Composite index field: sessionChannelId
  @JsonKey(includeFromJson: false, includeToJson: false)
  String get sessionChannelId => '${sessionKey}_$channelId';

  /// Composite index field: workspaceSessionChannel
  @JsonKey(includeFromJson: false, includeToJson: false)
  String get workspaceSessionChannel =>
      '${workspaceId}_${sessionKey}_$channelId';

  /// Default constructor
  Channel();

  /// Constructor with required fields
  Channel.create({
    required this.workspaceId,
    required this.channelId,
    required String
        sessionKey, // sessionKey parameter for backward compatibility
    required String
        channelOwnerUserId, // channelOwnerUserId parameter for backward compatibility
    required String
        recipientId, // recipientId parameter for backward compatibility
    this.dmChannelId = '',
    this.participantId = '',
    this.name = '',
    this.avatar = '',
    this.originalAvatar = '',
    this.channelTypeRaw = 1, // Default to channel type
    this.topic = '',
    this.description = '',
    DateTime? createTime,
    DateTime? updateTime,
    this.isArchived = false,
    this.isPartial = false,
    this.isPrivate = false,
    this.invitationLink = '',
    this.totalMembers = 0,
    this.dmStatusRaw = 0,
    this.participantIdsString = '',
    this.rejectTime = '',
    this.acceptTime = '',
    this.userId = '',
    this.pinned = false,
    this.sort = 0,
  })  : sessionKeyField = sessionKey,
        uniqueId = sessionKey + '_' + workspaceId + '_' + channelId,
        channelOwnerUserIdField = channelOwnerUserId,
        recipientIdField = recipientId {
    final now = DateTime.now();
    this.createTime = createTime ?? now;
    this.updateTime = updateTime ?? now;
    if (this.userId.isEmpty) this.userId = channelOwnerUserId;
  }

  /// Copy constructor for updates
  Channel copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? sessionKey, // sessionKey parameter for backward compatibility
    String?
        channelOwnerUserId, // channelOwnerUserId parameter for backward compatibility
    String? participantId,
    String? dmChannelId,
    String? recipientId, // recipientId parameter for backward compatibility
    String? name,
    String? avatar,
    String? originalAvatar,
    int? channelTypeRaw,
    String? topic,
    String? description,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isArchived,
    bool? isPartial,
    bool? isPrivate,
    String? invitationLink,
    int? totalMembers,
    int? dmStatusRaw,
    String? participantIdsString,
    String? rejectTime,
    String? acceptTime,
    String? userId,
    bool? pinned,
    int? sort,
    String? aliasName,
  }) {
    final newChannel = Channel()
      ..id = id ?? this.id
      ..workspaceId = workspaceId ?? this.workspaceId
      ..channelId = channelId ?? this.channelId
      ..sessionKeyField = sessionKey ?? this.sessionKeyField
      ..channelOwnerUserIdField =
          channelOwnerUserId ?? this.channelOwnerUserIdField
      ..participantId = participantId ?? this.participantId
      ..dmChannelId = dmChannelId ?? this.dmChannelId
      ..recipientIdField = recipientId ?? this.recipientIdField
      ..name = name ?? this.name
      ..avatar = avatar ?? this.avatar
      ..originalAvatar = originalAvatar ?? this.originalAvatar
      ..channelTypeRaw = channelTypeRaw ?? this.channelTypeRaw
      ..topic = topic ?? this.topic
      ..description = description ?? this.description
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..isArchived = isArchived ?? this.isArchived
      ..isPartial = isPartial ?? this.isPartial
      ..isPrivate = isPrivate ?? this.isPrivate
      ..invitationLink = invitationLink ?? this.invitationLink
      ..totalMembers = totalMembers ?? this.totalMembers
      ..dmStatusRaw = dmStatusRaw ?? this.dmStatusRaw
      ..participantIdsString = participantIdsString ?? this.participantIdsString
      ..rejectTime = rejectTime ?? this.rejectTime
      ..acceptTime = acceptTime ?? this.acceptTime
      ..pinned = pinned ?? this.pinned
      ..sort = sort ?? this.sort
      ..userId = userId ?? this.userId
      ..aliasName = aliasName ?? this.aliasName;

    // Copy relationships
    newChannel.recipient.target = recipient.target;
    newChannel.metadata.target = metadata.target;
    newChannel.localMetadata.target = localMetadata.target;
    return newChannel;
  }

  /// Archive the channel
  void archive() {
    isArchived = true;
    updateTime = DateTime.now();
  }

  /// Unarchive the channel
  void unarchive() {
    isArchived = false;
    updateTime = DateTime.now();
  }

  /// Update channel information
  void updateInfo({
    String? name,
    String? avatar,
    String? originalAvatar,
    String? topic,
    String? description,
    int? channelTypeRaw,
  }) {
    if (name != null) this.name = name;
    if (avatar != null) this.avatar = avatar;
    if (originalAvatar != null) this.originalAvatar = originalAvatar;
    if (topic != null) this.topic = topic;
    if (description != null) this.description = description;
    if (channelTypeRaw != null) this.channelTypeRaw = channelTypeRaw;
    updateTime = DateTime.now();
  }

  /// Mark as complete channel record
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Check if channel has avatar
  bool get hasAvatar => avatar.isNotEmpty;

  /// Check if channel is DM
  bool get isDM => channelType.isDM || dmChannelId.isNotEmpty;

  // RELATIONSHIP HELPER METHODS

  /// Get channel owner member (if exists)
  Member? get ownerMember {
    return members
        .where((member) => member.role.toLowerCase() == 'owner')
        .firstOrNull;
  }

  /// Get admin members
  List<Member> get adminMembers {
    return members
        .where((member) => member.role.toLowerCase() == 'admin')
        .toList();
  }

  /// Get moderator members
  List<Member> get moderatorMembers {
    return members
        .where((member) => member.role.toLowerCase() == 'moderator')
        .toList();
  }

  /// Get regular members (excluding admins, moderators, owner)
  List<Member> get regularMembers {
    return members.where((member) {
      final role = member.role.toLowerCase();
      return role != 'owner' && role != 'admin' && role != 'moderator';
    }).toList();
  }

  /// Get online members
  List<Member> get onlineMembers {
    return members.where((member) => member.isRelatedUserOnline).toList();
  }

  /// Get offline members
  List<Member> get offlineMembers {
    return members.where((member) => !member.isRelatedUserOnline).toList();
  }

  /// Get members with nicknames
  List<Member> get membersWithNicknames {
    return members.where((member) => member.hasNickname).toList();
  }

  /// Get members with special permissions (admin, moderator, owner)
  List<Member> get membersWithSpecialPermissions {
    return members.where((member) => member.hasSpecialPermissions).toList();
  }

  /// Get online members count
  int get onlineMembersCount => onlineMembers.length;

  /// Get admin members count
  int get adminMembersCount => adminMembers.length;

  /// Check if channel has admin members
  bool get hasAdminMembers => adminMembersCount > 0;

  /// Check if channel has online members
  bool get hasOnlineMembers => onlineMembersCount > 0;

  /// Get member by user ID
  Member? getMemberByUserId(String userId) {
    return members.where((member) => member.userId == userId).firstOrNull;
  }

  /// Check if user is member of this channel
  bool isUserMember(String userId) {
    return getMemberByUserId(userId) != null;
  }

  /// Check if user is admin or owner of this channel
  bool isUserAdminOrOwner(String userId) {
    final member = getMemberByUserId(userId);
    return member?.isAdminOrOwner ?? false;
  }

  // MESSAGE HELPER METHODS

  /// Get messages count
  int get messagesCount => messages.length;

  /// Check if channel has messages
  bool get hasMessages => messages.isNotEmpty;

  /// Get latest message
  Message? get latestMessage {
    if (messages.isEmpty) return null;
    return messages.reduce(
      (a, b) => (a.createTime?.millisecondsSinceEpoch ?? 0) >
              (b.createTime?.millisecondsSinceEpoch ?? 0)
          ? a
          : b,
    );
  }

  /// Get first message
  Message? get firstMessage {
    if (messages.isEmpty) return null;
    return messages.reduce(
      (a, b) => (a.createTime?.millisecondsSinceEpoch ?? 0) <
              (b.createTime?.millisecondsSinceEpoch ?? 0)
          ? a
          : b,
    );
  }

  /// Get pinned messages
  List<Message> get pinnedMessages {
    return messages.where((message) => message.isPinned).toList();
  }

  /// Get unread messages (pending or failed)
  List<Message> get unreadMessages {
    return messages
        .where((message) => message.isPending || message.isFailed)
        .toList();
  }

  /// Get successful messages
  List<Message> get successfulMessages {
    return messages.where((message) => message.isSuccessful).toList();
  }

  /// Get failed messages
  List<Message> get failedMessages {
    return messages.where((message) => message.isFailed).toList();
  }

  /// Get messages with attachments
  List<Message> get messagesWithAttachments {
    return messages.where((message) => message.hasAttachments).toList();
  }

  /// Get messages with reactions
  List<Message> get messagesWithReactions {
    return messages.where((message) => message.hasReactions).toList();
  }

  /// Get messages with mentions
  List<Message> get messagesWithMentions {
    return messages.where((message) => message.hasMentions).toList();
  }

  /// Get recent messages (last 50, sorted by create time)
  List<Message> get recentMessages {
    final sortedMessages = messages.toList()
      ..sort(
        (a, b) => (b.createTime ?? DateTime(0))
            .compareTo(a.createTime ?? DateTime(0)),
      );
    return sortedMessages.take(50).toList();
  }

  /// Get today's messages
  List<Message> get todayMessages {
    return messages.where((message) => message.isToday).toList();
  }

  /// Get pinned messages count
  int get pinnedMessagesCount => pinnedMessages.length;

  /// Get unread messages count
  int get unreadMessagesCount => unreadMessages.length;

  /// Get failed messages count
  int get failedMessagesCount => failedMessages.length;

  /// Get messages with attachments count
  int get messagesWithAttachmentsCount => messagesWithAttachments.length;

  /// Check if channel has pinned messages
  bool get hasPinnedMessages => pinnedMessagesCount > 0;

  /// Check if channel has unread messages
  bool get hasUnreadMessages => unreadMessagesCount > 0;

  /// Check if channel has failed messages
  bool get hasFailedMessages => failedMessagesCount > 0;

  /// Get message by message ID
  Message? getMessageById(String messageId) {
    return messages
        .where((message) => message.messageId == messageId)
        .firstOrNull;
  }

  /// Get messages by sender
  List<Message> getMessagesBySender(String userId) {
    return messages.where((message) => message.userId == userId).toList();
  }

  /// Get messages by type
  List<Message> getMessagesByType(int messageType) {
    return messages
        .where((message) => message.messageTypeRaw == messageType)
        .toList();
  }

  /// Get messages by status
  List<Message> getMessagesByStatus(int messageStatus) {
    return messages
        .where((message) => message.messageStatusRaw == messageStatus)
        .toList();
  }

  /// Get messages in date range
  List<Message> getMessagesInDateRange(DateTime start, DateTime end) {
    return messages.where((message) {
      final createTime = message.createTime;
      if (createTime == null) return false;
      return createTime.isAfter(start) && createTime.isBefore(end);
    }).toList();
  }

  /// Archive this channel
  void archiveChannel() {
    isArchived = true;
    updateTime = DateTime.now();
  }

  /// Unarchive this channel
  void unarchiveChannel() {
    isArchived = false;
    updateTime = DateTime.now();
  }

  /// Get the primary identifier for this channel entity
  @override
  String getPrimaryId() => channelId;

  // ENUM PROPERTIES AND HELPERS

  /// Get channel type as enum
  ChannelTypeEnum get channelType => ChannelTypeEnum.fromValue(channelTypeRaw);

  /// Set channel type from enum
  set channelType(ChannelTypeEnum type) => channelTypeRaw = type.value;

  /// Get DM status as enum
  DirectMessageStatusEnum get dmStatus =>
      DirectMessageStatusEnum.fromValue(dmStatusRaw);

  /// Set DM status from enum
  set dmStatus(DirectMessageStatusEnum status) => dmStatusRaw = status.value;

  /// Get participant IDs as list
  List<String> get participantIds {
    if (participantIdsString.isEmpty) return [];
    return participantIdsString.split(',').map((id) => id.trim()).toList();
  }

  /// Set participant IDs from list
  set participantIds(List<String> ids) {
    participantIdsString = ids.join(',');
  }

  /// Remove participant ID
  void removeParticipantId(String participantId) {
    final currentIds = participantIds;
    currentIds.remove(participantId);
    participantIds = currentIds;
  }

  /// Check if user is participant
  bool isParticipant(String userId) {
    return participantIds.contains(userId);
  }

  /// Get reject time as DateTime
  DateTime? get rejectTimeAsDateTime {
    if (rejectTime.isEmpty) return null;
    try {
      return TimeUtils.parseUTCStringToDateTime(rejectTime);
    } catch (e) {
      return null;
    }
  }

  /// Set reject time from DateTime
  set rejectTimeAsDateTime(DateTime? dateTime) {
    rejectTime = dateTime?.toIso8601String() ?? '';
  }

  /// Get accept time as DateTime
  DateTime? get acceptTimeAsDateTime {
    if (acceptTime.isEmpty) return null;
    try {
      return TimeUtils.parseUTCStringToDateTime(acceptTime);
    } catch (e) {
      return null;
    }
  }

  /// Set accept time from DateTime
  set acceptTimeAsDateTime(DateTime? dateTime) {
    acceptTime = dateTime?.toIso8601String() ?? '';
  }

  /// Check if channel is DM using enum
  bool get isDMChannel => channelType.isDM;

  /// Check if channel is regular channel using enum
  bool get isRegularChannel => channelType.isChannel;

  /// Check if channel is broadcast using enum
  bool get isBroadcastChannel => channelType.isBroadcast;

  /// Check if DM status is pending
  bool get isDMPending => dmStatus.isPending;

  /// Check if DM status is contacted
  bool get isDMContacted => dmStatus.isContacted;

  /// Accept DM request
  void acceptDMRequest() {
    dmStatus = DirectMessageStatusEnum.contacted;
    acceptTimeAsDateTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Reject DM request
  void rejectDMRequest() {
    dmStatus = DirectMessageStatusEnum.pending;
    rejectTimeAsDateTime = DateTime.now();
    updateTime = DateTime.now();
  }

  /// Returns the resolved name for the channel:
  /// - For DMs, prefer the user’s alias if set
  /// - Otherwise use the user’s display name
  /// - Fallback to the general channel name
  String getResolvedChannelName() {
    if (channelType.isChannel || channelType.isBroadcast) return name;

    // Try to use the denormalized alias name first
    if (aliasName.isNotEmpty) {
      return aliasName;
    }

    if (StringUtils.isNotNullOrEmpty(recipient.target?.aliasName)) {
      return recipient.target!.aliasName!;
    }

    // If no alias, use the recipient's display name
    final displayName = recipient.target?.profile.target?.displayName;
    if (displayName != null && displayName.isNotEmpty) {
      return displayName;
    }

    return recipient.target?.username ?? name;
  }

  @override
  String toString() {
    return 'Channel{'
        'id: $id, '
        'workspaceId: $workspaceId, '
        'channelId: $channelId, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'name: $name, '
        'avatar: $avatar, '
        'channelType: ${channelType.displayName}, '
        'dmStatus: ${dmStatus.displayName}, '
        'isPrivate: $isPrivate, '
        'totalMembers: $totalMembers, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'isArchived: $isArchived, '
        'uniqueId: $uniqueId'
        '}';
  }
}
