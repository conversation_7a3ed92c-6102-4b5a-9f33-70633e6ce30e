//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

/// - MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK: ALWAYS_ASK (DEFAULT): ask everytime user received new media message  - MEDIA_PERMISSION_SETTING_ENUM_ALLOW: ALLOW: accept incoming media messages from every body  - MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW: NOT_ALLOW: au-to denied incoming media messages
enum V3MediaPermissionSettingEnum {
  /// - MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK: ALWAYS_ASK (DEFAULT): ask everytime user received new media message  - MEDIA_PERMISSION_SETTING_ENUM_ALLOW: ALLOW: accept incoming media messages from every body  - MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW: NOT_ALLOW: au-to denied incoming media messages
  @<PERSON>sonValue(0)
  MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK('0'),

  /// - MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK: ALWAYS_ASK (DEFAULT): ask everytime user received new media message  - MEDIA_PERMISSION_SETTING_ENUM_ALLOW: ALLOW: accept incoming media messages from every body  - MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW: NOT_ALLOW: au-to denied incoming media messages
  @JsonValue(1)
  MEDIA_PERMISSION_SETTING_ENUM_ALLOW('1'),

  /// - MEDIA_PERMISSION_SETTING_ENUM_ALWAYS_ASK: ALWAYS_ASK (DEFAULT): ask everytime user received new media message  - MEDIA_PERMISSION_SETTING_ENUM_ALLOW: ALLOW: accept incoming media messages from every body  - MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW: NOT_ALLOW: au-to denied incoming media messages
  @JsonValue(2)
  MEDIA_PERMISSION_SETTING_ENUM_NOT_ALLOW('2');

  const V3MediaPermissionSettingEnum(this.value);

  final String value;

  @override
  String toString() => value;
}
