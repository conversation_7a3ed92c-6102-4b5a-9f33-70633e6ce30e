import 'package:data_router/data_router.dart' as dr;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_sync.dart';
import '../../../data/interface/sync_key_enum.dart';

part 'create_alias_name_data_for_update_use_case.freezed.dart';

@Injectable()
class CreateAliasNameForUpdateUseCase extends BaseFutureUseCase<
    CreateAliasNameForUpdateInput, CreateAliasNameForUpdateOutput> {
  const CreateAliasNameForUpdateUseCase(
    this._privateDataDataOperation,
  );

  final dr.PrivateDataDataOperation _privateDataDataOperation;

  @protected
  @override
  Future<CreateAliasNameForUpdateOutput> buildUseCase(
    CreateAliasNameForUpdateInput input,
  ) async {
    try {
      // Validate userId is not empty to prevent ObjectBox crash
      if (input.userId.trim().isEmpty) {
        return CreateAliasNameForUpdateOutput(cloudEvent: null);
      }

      // Check activeSessionKey first
      final activeSessionKey = Config.getInstance().activeSessionKey;

      if (activeSessionKey == null || activeSessionKey.isEmpty) {
        return CreateAliasNameForUpdateOutput(cloudEvent: null);
      }

      // Ensure parent PrivateData exists
      var dPrivateData = await _privateDataDataOperation.localSource
          .get('${activeSessionKey}_${input.userId}');
      if (dPrivateData == null) {
        final newPrivateData = _privateDataDataOperation.createPrivateData(
          sessionKey: activeSessionKey,
          userId: input.userId,
        );
        await _privateDataDataOperation.localSource.insert(newPrivateData);
      }

      var existingUserPrivateData =
          _privateDataDataOperation.getUserPrivateData(input.userId);
      if (existingUserPrivateData != null) {
        // Update existing data
        var updatedUserPrivateData = existingUserPrivateData.copyWith(
          aliasName: input.newAliasName,
          version: existingUserPrivateData.version + 1,
        );
        _privateDataDataOperation.insertUserPrivateData(updatedUserPrivateData);
      } else {
        // Create new data with temporary values
        var newUserPrivateData = dr.UserPrivateData.create(
          userId: input.userId,
          aliasName: input.newAliasName,
          blocked: false,
          source: '',
          version: 0,
        );
        _privateDataDataOperation.insertUserPrivateData(newUserPrivateData);
      }

      // Get the updated/created UserPrivateData for CloudEvent
      var finalUserPrivateData =
          _privateDataDataOperation.getUserPrivateData(input.userId);
      if (finalUserPrivateData == null) {
        return CreateAliasNameForUpdateOutput(cloudEvent: null);
      }

      var cloudEvent = CloudEvent.createPrivateDataSync(
        PrivateDataSync(
          key: SyncKeyEnum.users.name,
          value: finalUserPrivateData.toSyncData(),
        ).toJson(),
      );
      return CreateAliasNameForUpdateOutput(cloudEvent: cloudEvent.toJson());
    } on Exception catch (e) {
      return CreateAliasNameForUpdateOutput(cloudEvent: null);
    } catch (e) {
      return CreateAliasNameForUpdateOutput(cloudEvent: null);
    }
  }
}

@freezed
sealed class CreateAliasNameForUpdateInput extends BaseInput
    with _$CreateAliasNameForUpdateInput {
  const CreateAliasNameForUpdateInput._();

  factory CreateAliasNameForUpdateInput({
    required String userId,
    required String newAliasName,
  }) = _CreateAliasNameForUpdateInput;
}

@freezed
sealed class CreateAliasNameForUpdateOutput extends BaseOutput
    with _$CreateAliasNameForUpdateOutput {
  const CreateAliasNameForUpdateOutput._();

  factory CreateAliasNameForUpdateOutput({
    required Map<String, dynamic>? cloudEvent,
  }) = _CreateAliasNameForUpdateOutput;
}
