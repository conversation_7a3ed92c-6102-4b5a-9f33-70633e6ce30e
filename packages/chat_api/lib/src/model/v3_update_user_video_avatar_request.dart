//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_video_avatar_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserVideoAvatarRequest {
  /// Returns a new [V3UpdateUserVideoAvatarRequest] instance.
  V3UpdateUserVideoAvatarRequest({
    this.avatarPath,
  });

  @JsonKey(
    name: r'avatarPath',
    required: false,
    includeIfNull: false,
  )
  final String? avatarPath;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserVideoAvatarRequest && other.avatarPath == avatarPath;

  @override
  int get hashCode => avatarPath.hashCode;

  factory V3UpdateUserVideoAvatarRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserVideoAvatarRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserVideoAvatarRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
