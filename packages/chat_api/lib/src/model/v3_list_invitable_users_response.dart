//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_paging.dart';
import 'package:chat_api/src/model/v3_user.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_invitable_users_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListInvitableUsersResponse {
  /// Returns a new [V3ListInvitableUsersResponse] instance.
  V3ListInvitableUsersResponse({
    this.ok,
    this.data,
    this.error,
    this.paging,
  });

  @J<PERSON><PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<V3User>? data;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'paging',
    required: false,
    includeIfNull: false,
  )
  final V3Paging? paging;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListInvitableUsersResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error &&
          other.paging == paging;

  @override
  int get hashCode =>
      ok.hashCode + data.hashCode + error.hashCode + paging.hashCode;

  factory V3ListInvitableUsersResponse.fromJson(Map<String, dynamic> json) =>
      _$V3ListInvitableUsersResponseFromJson(json);

  Map<String, dynamic> toJson() => _$V3ListInvitableUsersResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
