import 'dart:async';
import 'dart:convert';

import 'package:injectable/injectable.dart' hide Order;
import 'package:rxdart/rxdart.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../core/cache/cache.dart';
import '../../core/enums/translated_status_enum.dart';
import '../../core/metrics/metrics.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/message_repository.dart';
import '../database/enums/attachment_status_enum.dart';
import '../database/generated/objectbox.g.dart';
import '../events/private_data_events.dart';
import '../helpers/relationship_linker.dart';
import '../manager/relation_manager.dart';

@LazySingleton(as: MessageRepository)
class MessageRepositoryImpl extends BaseRepository<Message>
    implements MessageRepository {
  final SessionBox<TranslatedResult> _translatedResultSessionBox;
  final SessionBox<Attachment> _attachmentSessionBox;
  final SessionBox<Channel> _channelSessionBox;
  final SessionBox<Message> messageBox;
  final RelationManager _relationManager;
  final SessionBox<User> _userSessionBox;
  final SessionBox<ChannelMetadata> _metadataBox;
  final SessionBox<ChannelLocalMetadata> _channelMetadataSessionBox;
  StreamSubscription<PrivateDataSyncCompleteEvent>?
      _privateDataSyncSubscription;

  final AppEventBus _eventBus;

  MessageRepositoryImpl(
    this.messageBox,
    this._translatedResultSessionBox,
    this._attachmentSessionBox,
    this._channelSessionBox,
    this._metadataBox,
    this._relationManager,
    this._userSessionBox,
    this._channelMetadataSessionBox,
    this._eventBus,
  ) : super(messageBox) {
    _relationManager.registerUserListener(_onUserCacheUpdated);

    // [RACE CONDITION FIX] Listen for private data sync complete events
    _privateDataSyncSubscription = PrivateDataEventBus()
        .onPrivateDataSyncComplete
        .listen(_onPrivateDataSyncComplete);

    _eventBus.on<UpdateChannelIdForTempDMMessage>().listen((event) {
      _updateChannelIdForDMTempMessage(event);
    });
  }

  /// Keep the latest snapshot of the user cache.
  Map<String, User> _userCache = {};

  String get _activeSessionKey => Config.getInstance().activeSessionKey!;

  // Enhanced Cache Management with timestamp validation
  final EnhancedCacheManager<Message> _messageCacheManager =
      EnhancedCacheManager<Message>(1000);
  final EnhancedCacheManager<Attachment> _attachmentCacheManager =
      EnhancedCacheManager<Attachment>(500);
  final EnhancedCacheManager<TranslatedResult> _translatedResultCacheManager =
      EnhancedCacheManager<TranslatedResult>(200);

  // Legacy cache for message lists (keeping for backward compatibility)
  final Map<String, List<Message>> _messageListCache = {};
  final List<String> _messageListCacheOrder = [];
  final Map<String, DateTime> _messageListCacheTimestamps = {};
  static const Duration _cacheExpiration = Duration(minutes: 30);

  // Performance tracking
  final MessageRepositoryMetrics _metrics = MessageRepositoryMetrics();

  /// Callback executed whenever the User cache changes in RelationManager.
  /// This ensures that message sender information is always up-to-date.
  /// This reactive logic is kept as it serves a different purpose than RelationshipLinker.
  void _onUserCacheUpdated(String sessionKey, CacheUpdate<User> event) {
    _userCache = event.cache;

    final updatedUserIds = [
      ...event.updated.keys.toList(),
      ...event.added.keys.toList(),
    ];

    messageBox.store.runInTransaction(TxMode.write, () {
      final messagesToUpdate = <Message>[];
      if (updatedUserIds.isNotEmpty) {
        final messagesOfUpdatedUsers = _getMessagesByUserIds(updatedUserIds);
        for (final msg in messagesOfUpdatedUsers) {
          final user = _userCache[msg.userId];
          if (user != null &&
              (msg.sender.target == null || msg.sender.target!.id == 0)) {
            msg.sender.target = user;
            messagesToUpdate.add(msg);
          }
        }
      }

      final deletedUserIds = event.deleted.keys.toList();
      if (deletedUserIds.isNotEmpty) {
        final messagesOfDeletedUsers = _getMessagesByUserIds(deletedUserIds);
        for (final msg in messagesOfDeletedUsers) {
          if (msg.sender.target == null) continue; // Already null, skip

          msg.sender.target = null;
          messagesToUpdate.add(msg);
        }
      }

      if (messagesToUpdate.isEmpty) {
        return; // No messages to update
      }
      super.putMany(messagesToUpdate);
    });
  }

  /// [RACE CONDITION FIX] Handle private data sync complete event
  /// This triggers relationship linking after UserPrivateData is available
  void _onPrivateDataSyncComplete(PrivateDataSyncCompleteEvent event) {
    try {
      // Trigger user cache refresh for the affected users
      // This will cause _onUserCacheUpdated to be called with proper UserPrivateData
      _relationManager.refreshUserCache(event.sessionKey, event.userIds);
    } catch (e) {
      Log.e('Error in _onPrivateDataSyncComplete: $e');
    }
  }

  List<Message> _getMessagesByUserIds(List<String> userIds) {
    if (userIds.isEmpty) return [];
    final messages = messageBox.query(Message_.userIdField.oneOf(userIds));
    return messages;
  }

  /// [OPTIMIZATION] Batch sender linking to reduce redundant operations
  final Set<String> _linkedSenders = {};

  // Enhanced cache management methods
  /// Update message cache with timestamp validation
  void _updateMessageCache(String messageId, Message message) {
    final cached = _messageCacheManager.putIfNewer(messageId, message);
    if (!cached) {
      // Log when we skip caching due to stale data
      _metrics.recordCacheSkip('message', messageId);
    }
  }

  /// Update attachment cache with timestamp validation
  void _updateAttachmentCache(String attachmentId, Attachment attachment) {
    final cached = _attachmentCacheManager.putIfNewer(attachmentId, attachment);
    if (!cached) {
      // Log when we skip caching due to stale data
      _metrics.recordCacheSkip('attachment', attachmentId);
    }
  }

  void _removeFromMessageCache(String messageId) {
    _messageCacheManager.remove(messageId);
  }

  void _trackTransaction() {
    _metrics.transactionCount++;
  }

  /// Get comprehensive cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'metrics': _metrics.toMap(),
      'messageCache': _messageCacheManager.getStats(),
      'attachmentCache': _attachmentCacheManager.getStats(),
      'translatedResultCache': _translatedResultCacheManager.getStats(),
      'legacyMessageListCache': {
        'size': _messageListCache.length,
        'maxSize': 1000, // Assuming max size
      },
    };
  }

  /// Clear all caches (useful for testing or memory management)
  void clearAllCaches() {
    _messageCacheManager.clear();
    _attachmentCacheManager.clear();
    _translatedResultCacheManager.clear();
    _messageListCache.clear();
    _messageListCacheOrder.clear();
    _messageListCacheTimestamps.clear();
  }

  // Override BaseRepository methods to integrate with EnhancedCacheManager
  @override
  int put(Message message) {
    _trackTransaction();
    final result = super.put(message);
    if (result > 0) {
      _updateMessageCache(message.messageId, message);
      // Update attachment cache if message has attachments
      for (final attachment in message.mediaAttachments) {
        _updateAttachmentCache(attachment.attachmentId, attachment);
      }
    }
    return result;
  }

  @override
  List<int> putMany(List<Message> messages) {
    _trackTransaction();
    final results = super.putMany(messages);
    // Update cache for successfully saved messages
    for (int i = 0; i < messages.length && i < results.length; i++) {
      if (results[i] > 0) {
        final message = messages[i];
        _updateMessageCache(message.messageId, message);
        // Update attachment cache if message has attachments
        for (final attachment in message.mediaAttachments) {
          _updateAttachmentCache(attachment.attachmentId, attachment);
        }
      }
    }
    return results;
  }

  /// Triggers the asynchronous linking of a message to its sender.
  /// [OPTIMIZATION] Uses caching to avoid redundant linking for same sender
  void _triggerSenderLinking(Message message) {
    // [OPTIMIZATION] Skip if sender already linked in this session
    if (_linkedSenders.contains(message.userId)) {
      final cachedUser = _userCache[message.userId];
      if (cachedUser != null && message.sender.target == null) {
        message.sender.target = cachedUser;
      }
      return;
    }

    RelationshipLinker.linkToOneSync<Message, User>(
      parent: message,
      toOne: message.sender,
      // Fetcher first tries the hot cache, then falls back to the User SessionBox.
      fetcher: () =>
          _userCache[message.userId] ??
          _userSessionBox.getFirstWith(User_.userId.equals(message.userId)),
    );

    // [OPTIMIZATION] Mark sender as linked to avoid redundant operations
    _linkedSenders.add(message.userId);
  }

  @override
  bool insert(Message message) {
    try {
      if (message.id > 0) {
        if (message.mediaAttachments.isNotEmpty) {
          _attachmentSessionBox.putMany(message.mediaAttachments);
        }
        final success = put(message) > 0;
        if (success) {
          _updateChannelMetadata(
            workspaceId: message.workspaceId,
            channelId: message.channelId,
            message: message,
          );
        }
        return success;
      }

      if (!_validateMessage(message)) {
        Log.e('Invalid message data: ${message.messageId}');
        return false;
      }

      var success = false;
      messageBox.store.runInTransaction(TxMode.write, () {
        _trackTransaction();

        // MODIFIED: Trigger the robust async linker instead of the simple sync handler.
        // This is a "fire-and-forget" call.
        _triggerSenderLinking(message);

        final ids = _insertOrUpdateMessages([message], isInsertOne: true);
        success = ids.isNotEmpty;

        // [OPTIMIZATION] Update cache after successful insert
        if (success) {
          _updateMessageCache(message.messageId, message);
          // Update attachment cache if message has attachments
          for (final attachment in message.mediaAttachments) {
            _updateAttachmentCache(attachment.attachmentId, attachment);
          }
          // Invalidate list caches as they may be outdated
          _messageListCache.clear();
          _messageListCacheOrder.clear();
          _messageListCacheTimestamps.clear();
        }
      });

      return success;
    } catch (e) {
      Log.e('Failed to insert message ${message.messageId}: $e');
      return false;
    }
  }

  @override
  bool insertAll(List<Message> messages) {
    try {
      final validMessages = messages.where(_validateMessage).toList();
      if (validMessages.length != messages.length) {
        Log.e(
          'Warning: ${messages.length - validMessages.length} invalid messages skipped',
        );
      }

      if (validMessages.isEmpty) {
        Log.e('No valid messages to insert');
        return false;
      }

      bool success = false;
      messageBox.store.runInTransaction(TxMode.write, () {
        _trackTransaction();

        // MODIFIED: Trigger linking for each message.
        for (final message in validMessages) {
          _triggerSenderLinking(message);
        }

        final ids = _insertOrUpdateMessages(validMessages, isInsertOne: false);
        success = ids.isNotEmpty;

        // [OPTIMIZATION] Update cache after successful batch insert
        if (success) {
          for (final message in validMessages) {
            _updateMessageCache(message.messageId, message);
            // Update attachment cache if message has attachments
            for (final attachment in message.mediaAttachments) {
              _updateAttachmentCache(attachment.attachmentId, attachment);
            }
          }
          // Invalidate list caches as they may be outdated
          _messageListCache.clear();
          _messageListCacheOrder.clear();
          _messageListCacheTimestamps.clear();
        }
      });

      return success;
    } catch (e) {
      Log.e('Failed to insert messages: $e');
      return false;
    }
  }

  @override
  Message? getMessage(String messageId) {
    try {
      if (messageId.trim().isEmpty) {
        print('[MessageRepository] getMessage: messageId is empty');
        return null;
      }

      final cachedMessage = _messageCacheManager.get(messageId);
      if (cachedMessage != null) {
        _metrics.cacheHits++;
        return cachedMessage;
      }

      // Cache miss - query database
      _metrics.cacheMisses++;
      final message = getFirst(Message_.messageId.equals(messageId));

      // Update cache with result using timestamp validation
      if (message != null) {
        _updateMessageCache(messageId, message);
      }

      return message;
    } catch (e) {
      Log.e('Error getting message $messageId: $e');
      return null;
    }
  }

  @override
  Future<Message?> getMessageAsync(String messageId) async {
    return getMessage(messageId);
  }

  @override
  List<Message> getMessages() {
    try {
      return getAll();
    } catch (e) {
      Log.e('Error getting all messages: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesAsync() async {
    return getMessages();
  }

  /// [OPTIMIZATION] Database-level pagination instead of loading all then filtering
  @override
  List<Message> getMessagesWithPaginationFromChat({
    required String workspaceId,
    required String channelId,
    int limit = 100,
    String? nextPageToken,
  }) {
    try {
      // [OPTIMIZATION] Build query with proper database-level pagination
      final queryBuilder = messageBox
          .queryWith(
            Message_.workspaceId
                .equals(workspaceId)
                .and(Message_.channelIdField.equals(channelId))
                .and(
                  nextPageToken != null && nextPageToken.isNotEmpty
                      ? Message_.messageId.lessThan(nextPageToken)
                      : Message_.id.greaterThan(0),
                ), // Always true condition when no token
          )
          .order(Message_.createTime, flags: Order.descending);

      // [OPTIMIZATION] Apply limit at database level, not in memory
      final query = queryBuilder.build()..limit = limit;
      final messages = query.find();
      query.close();
      return messages;
    } catch (e) {
      Log.e('Error getting messages with pagination from chat: $e');
      return [];
    }
  }

  @override
  List<Message> getMessagesForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    try {
      if (workspaceId.trim().isEmpty || channelId.trim().isEmpty) {
        print(
          '[MessageRepository] getMessagesForChannel: workspaceId or channelId is empty',
        );
        return [];
      }

      // [OPTIMIZATION] Create cache key for this channel
      final cacheKey = '${workspaceId}_${channelId}_all';

      // Check cache first
      if (_messageListCache.containsKey(cacheKey)) {
        final cachedTimestamp = _messageListCacheTimestamps[cacheKey]!;
        if (!_isCacheExpired(cachedTimestamp)) {
          // Update LRU order
          _messageListCacheOrder.remove(cacheKey);
          _messageListCacheOrder.add(cacheKey);
          _metrics.cacheHits++;
          return List.from(_messageListCache[cacheKey]!);
        } else {
          // Remove expired cache
          _messageListCache.remove(cacheKey);
          _messageListCacheOrder.remove(cacheKey);
          _messageListCacheTimestamps.remove(cacheKey);
        }
      }

      // Cache miss - query database
      _metrics.cacheMisses++;
      final query = messageBox
          .queryWith(
            Message_.workspaceId
                .equals(workspaceId)
                .and(Message_.channelIdField.equals(channelId)),
          )
          .order(Message_.createTime, flags: Order.descending)
          .build();
      final messages = query.find();
      query.close();

      // Update cache with result
      if (messages.isNotEmpty) {
        _updateMessageListCache(cacheKey, messages);
      }

      return messages;
    } catch (e) {
      Log.e('Error getting messages for channel $workspaceId:$channelId: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesForChannelAsync({
    required String workspaceId,
    required String channelId,
  }) async {
    return getMessagesForChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  @override
  List<Message> getDMMessagesForUser(String userId) {
    try {
      if (userId.trim().isEmpty) {
        print('[MessageRepository] getDMMessagesForUser: userId is empty');
        return [];
      }

      final query = messageBox
          .queryWith(Message_.userIdField.equals(userId))
          .order(Message_.createTime, flags: Order.descending)
          .build();
      final messages = query.find();
      query.close();
      return messages;
    } catch (e) {
      Log.e('Error getting DM messages for user $userId: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getDMMessagesForUserAsync(String userId) async {
    return getDMMessagesForUser(userId);
  }

  @override
  List<Message> getMessagesBySession(String sessionKey) {
    try {
      final query = messageBox
          .queryWith(Message_.sessionKeyField.equals(sessionKey))
          .order(Message_.createTime, flags: Order.descending)
          .build();
      final messages = query.find();
      query.close();
      return messages;
    } catch (e) {
      Log.e('Error getting messages by session $sessionKey: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesBySessionAsync(String sessionKey) async {
    return getMessagesBySession(sessionKey);
  }

  @override
  List<Message> getRecentMessages({int limit = 50}) {
    try {
      final query = messageBox
          .queryBuilder()
          .order(Message_.createTime, flags: Order.descending)
          .build()
        ..limit = limit;
      final messages = query.find();
      query.close();
      return messages;
    } catch (e) {
      Log.e('Error getting recent messages: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getRecentMessagesAsync({int limit = 50}) async {
    return getRecentMessages(limit: limit);
  }

  @override
  List<Message> getPinnedMessagesForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    try {
      final query = messageBox
          .queryWith(
            Message_.workspaceId
                .equals(workspaceId)
                .and(Message_.channelIdField.equals(channelId))
                .and(Message_.isPinned.equals(true)),
          )
          .order(Message_.pinTime, flags: Order.descending)
          .build();
      final messages = query.find();
      query.close();
      return messages;
    } catch (e) {
      Log.e(
        'Error getting pinned messages for channel $workspaceId:$channelId: $e',
      );
      return [];
    }
  }

  @override
  Future<List<Message>> getPinnedMessagesForChannelAsync({
    required String workspaceId,
    required String channelId,
  }) async {
    return getPinnedMessagesForChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  @override
  List<Message> getFailedMessages() {
    try {
      return query(Message_.messageStatusRaw.equals(4)); // 4 = Failed
    } catch (e) {
      Log.e('Error getting failed messages: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getFailedMessagesAsync() async {
    return getFailedMessages();
  }

  @override
  List<Message> getPendingMessages() {
    try {
      return query(Message_.messageStatusRaw.equals(0)); // 0 = Pending
    } catch (e) {
      Log.e('Error getting pending messages: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getPendingMessagesAsync() async {
    return getPendingMessages();
  }

  /// Validate message data
  bool _validateMessage(Message message) {
    return message.messageId.isNotEmpty &&
        message.workspaceId.isNotEmpty &&
        message.channelId.isNotEmpty &&
        message.userId.isNotEmpty;
  }

  @override
  bool deleteMessage(String messageId) {
    try {
      final message = getFirst(Message_.messageId.equals(messageId));

      if (message != null) {
        final workspaceId = message.workspaceId;
        final channelId = message.channelId;

        final success = remove(message.id);

        // Delete attachments associated with these messages
        deleteAttachmentsByMessageIds([message.messageId]);

        if (success) {
          // After deleting, update the channel metadata
          _updateChannelMetadata(
            workspaceId: workspaceId,
            channelId: channelId,
          );
          return true;
        }
      }
      return false;
    } catch (e) {
      Log.e('Error deleting message $messageId: $e');
      return false;
    }
  }

  @override
  int deleteMessagesByIds(List<String> messageIds) {
    if (messageIds.isEmpty) {
      return 0;
    }
    try {
      final messages = query(Message_.messageId.oneOf(messageIds));
      if (messages.isEmpty) {
        return 0;
      }

      final affectedChannels =
          messages.map((m) => '${m.workspaceId}|${m.channelId}').toSet();

      final idsToRemove = messages.map((m) => m.id).toList();
      final deletedCount = removeMany(idsToRemove);

      // Delete attachments associated with these messages
      deleteAttachmentsByMessageIds(messageIds);

      if (deletedCount > 0) {
        // [OPTIMIZATION] Remove from cache after successful delete
        for (final messageId in messageIds) {
          _removeFromMessageCache(messageId);
        }
        // Invalidate list caches as they may be outdated
        _messageListCache.clear();
        _messageListCacheOrder.clear();
        _messageListCacheTimestamps.clear();

        for (final channelKey in affectedChannels) {
          final parts = channelKey.split('|');
          _updateChannelMetadata(workspaceId: parts[0], channelId: parts[1]);
        }
      }

      return deletedCount;
    } catch (e) {
      Log.e('Error deleting messages by IDs: $e');
      return 0;
    }
  }

  @override
  int deleteAllMessages() {
    try {
      // Get all channels for the session before deleting messages
      final allChannels = _channelSessionBox.getAll();

      final deletedCount = removeAll();

      if (deletedCount > 0) {
        // [OPTIMIZATION] Clear all caches after successful delete
        clearAllCaches();

        for (final channel in allChannels) {
          _updateChannelMetadata(
            workspaceId: channel.workspaceId,
            channelId: channel.channelId,
          );
        }
      }
      return deletedCount;
    } catch (e) {
      Log.e('Error deleting all messages: $e');
      return 0;
    }
  }

  @override
  bool deleteAllMessageOnChannel({
    required String workspaceId,
    required String channelId,
  }) {
    try {
      final channelMessages = query(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId)),
      );

      if (channelMessages.isEmpty) return true;

      final ids = channelMessages.map((m) => m.id).toList();
      final deletedCount = removeMany(ids);

      // Should trigger even if we delete the last message (count becomes 0)
      _updateChannelMetadata(workspaceId: workspaceId, channelId: channelId);

      return deletedCount >= 0;
    } catch (e) {
      Log.e('Error deleting all messages on channel: $e');
      return false;
    }
  }

  @override
  bool deleteMessageByIds({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    try {
      final message = getMessageByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );

      if (message != null) {
        final success = remove(message.id);
        if (success) {
          _updateChannelMetadata(
            workspaceId: workspaceId,
            channelId: channelId,
          );
          return true;
        }
      }
      return false;
    } catch (e) {
      Log.e('Error deleting message by IDs: $e');
      return false;
    }
  }

  @override
  int deleteMessagesByIdsInChannel({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) {
    try {
      final messages = getMessagesByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        messageIds: messageIds,
      );

      if (messages == null || messages.isEmpty) return 0;

      final ids = messages.map((m) => m.id).toList();
      final deletedCount = removeMany(ids);

      if (deletedCount > 0) {
        _updateChannelMetadata(workspaceId: workspaceId, channelId: channelId);
      }

      return deletedCount;
    } catch (e) {
      Log.e('Error deleting messages by IDs in channel: $e');
      return 0;
    }
  }

  @override
  bool messageExists(String messageId) {
    return getMessage(messageId) != null;
  }

  @override
  Future<bool> messageExistsAsync(String messageId) async {
    return messageExists(messageId);
  }

  @override
  int getMessageCount() {
    try {
      return count();
    } catch (e) {
      Log.e('Error getting message count: $e');
      return 0;
    }
  }

  @override
  Future<int> getMessageCountAsync() async {
    return getMessageCount();
  }

  @override
  Stream<List<Message>> watchMessagesForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    // Use ObjectBox native watch for messages in specific channel
    return watch(
      Message_.workspaceId
          .equals(workspaceId)
          .and(Message_.channelIdField.equals(channelId)),
    )
        .map((query) {
          final messages = query.find();
          // Sort by createTime descending (same as getMessagesForChannel)
          messages.sort(
            (a, b) => (b.createTime ?? DateTime.now())
                .compareTo(a.createTime ?? DateTime.now()),
          );
          return messages;
        })
        .distinct() // [OPTIMIZATION] Prevent duplicate events
        .handleError((error) {
          Log.e(
            '[MessageRepository] watchMessagesForChannel error for $workspaceId:$channelId: $error',
          );
          return <Message>[];
        });
  }

  @override
  Stream<Message?> watchMessage(String messageId) {
    // Use ObjectBox native watch for specific message
    return watch(Message_.messageId.equals(messageId))
        .map((query) => query.findFirst())
        .distinct() // [OPTIMIZATION] Prevent duplicate events
        .handleError((error) {
      Log.e('[MessageRepository] watchMessage error for $messageId: $error');
      return null;
    });
  }

  @override
  Stream<List<Message>> watchAllMessages() {
    // Use ObjectBox native watch for all messages in current session
    return watchAll()
        .map((query) => query.find())
        .distinct() // [OPTIMIZATION] Prevent duplicate events
        .handleError((error) {
      Log.e('[MessageRepository] watchAllMessages error: $error');
      return <Message>[];
    });
  }

  @override
  Stream<List<Message>> watchDMMessagesForUser(String userId) {
    // Use ObjectBox native watch for DM messages for specific user
    return watch(Message_.userIdField.equals(userId)).map((query) {
      final messages = query.find();
      // Sort by createTime descending (same as getDMMessagesForUser)
      messages.sort(
        (a, b) => (b.createTime ?? DateTime.now())
            .compareTo(a.createTime ?? DateTime.now()),
      );
      return messages;
    });
  }

  @override
  Stream<List<Message>> watchFailedMessages() {
    // Use ObjectBox native watch for failed messages
    return watch(Message_.messageStatusRaw.equals(4)) // 4 = Failed
        .map((query) => query.find());
  }

  @override
  Stream<List<Message>> watchPendingMessages() {
    // Use ObjectBox native watch for pending messages
    return watch(Message_.messageStatusRaw.equals(0)) // 0 = Pending
        .map((query) => query.find());
  }

  @override
  Future<List<int>> forceUpdateMessageAll(List<Message> messages) async {
    try {
      final validMessages = messages.where(_validateMessage).toList();
      if (validMessages.length != messages.length) {
        Log.e(
          'Warning: ${messages.length - validMessages.length} invalid messages skipped',
        );
      }

      _existsAndUpdate(validMessages);

      final ids = putMany(validMessages);

      return ids;
    } catch (e) {
      throw Exception('Failed to force update messages: $e');
    }
  }

  @override
  Message? getMessageByIds({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    try {
      return getFirst(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId))
            .and(Message_.messageId.equals(messageId)),
      );
    } catch (e) {
      Log.e('Error getting message by IDs: $e');
      return null;
    }
  }

  @override
  Future<Message?> getMessageByIdsAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) async {
    return getMessageByIds(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );
  }

  @override
  List<Message>? getMessagesByIds({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) {
    try {
      return query(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId))
            .and(Message_.messageId.oneOf(messageIds)),
      );
    } catch (e) {
      Log.e('Error getting messages by IDs: $e');
      return null;
    }
  }

  @override
  Future<List<Message>?> getMessagesByIdsAsync({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) async {
    return getMessagesByIds(
      workspaceId: workspaceId,
      channelId: channelId,
      messageIds: messageIds,
    );
  }

  @override
  List<Message> getMessagesByOriginMessageIds({
    required String workspaceId,
    required String channelId,
    required String originalMessageId,
  }) {
    try {
      return query(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId))
            .and(Message_.originalMessageRaw.contains(originalMessageId)),
      );
    } catch (e) {
      Log.e('Error getting messages by origin message IDs: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesByOriginMessageIdsAsync({
    required String workspaceId,
    required String channelId,
    required String originalMessageId,
  }) async {
    return getMessagesByOriginMessageIds(
      workspaceId: workspaceId,
      channelId: channelId,
      originalMessageId: originalMessageId,
    );
  }

  @override
  Message? getLastTextMessage({
    required String workspaceId,
    required String channelId,
  }) {
    try {
      final textMessageType = MessageViewTypeEnum.text.rawValue();

      final query = messageBox
          .queryWith(
            Message_.workspaceId
                .equals(workspaceId)
                .and(Message_.channelIdField.equals(channelId))
                .and(
                  Message_.messageViewTypeRaw.equals(textMessageType),
                )
                .and(
                  Message_.isTemp.equals(false),
                ),
          )
          .order(Message_.createTime, flags: Order.descending)
          .build();
      final message = query.findFirst();
      query.close();
      return message;
    } catch (e) {
      Log.e('Error getting last text message: $e');
      return null;
    }
  }

  @override
  Future<Message?> getLastTextMessageAsync({
    required String workspaceId,
    required String channelId,
  }) async {
    return getLastTextMessage(workspaceId: workspaceId, channelId: channelId);
  }

  @override
  DateTime? getLastMessageCreated() {
    try {
      final query = messageBox
          .queryBuilder()
          .order(Message_.createTime, flags: Order.descending)
          .build();
      final message = query.findFirst();
      query.close();
      return message?.createTime;
    } catch (e) {
      Log.e('Error getting last message created: $e');
      return null;
    }
  }

  @override
  Future<DateTime?> getLastMessageCreatedAsync() async {
    return getLastMessageCreated();
  }

  @override
  Message? getMessageByIdOrRef({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String ref,
  }) {
    try {
      return getFirst(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId))
            .and(
              Message_.messageId.equals(messageId).or(
                    Message_.ref.equals(ref),
                  ),
            ),
      );
    } catch (e) {
      Log.e('Error getting message by ID or ref: $e');
      return null;
    }
  }

  @override
  Future<Message?> getMessageByIdOrRefAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String ref,
  }) async {
    return getMessageByIdOrRef(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      ref: ref,
    );
  }

  @override
  Message? getMessageByRef({required String ref}) {
    try {
      return getFirst(Message_.ref.equals(ref));
    } catch (e) {
      Log.e('Error getting message by ref: $e');
      return null;
    }
  }

  @override
  Future<Message?> getMessageByRefAsync({required String ref}) async {
    return getMessageByRef(ref: ref);
  }

  @override
  List<Message> getMessagesByRefs({required List<String> refs}) {
    try {
      return query(Message_.ref.oneOf(refs));
    } catch (e) {
      Log.e('Error getting messages by refs: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesByRefsAsync({
    required List<String> refs,
  }) async {
    return getMessagesByRefs(refs: refs);
  }

  @override
  List<Message> getMessagesWithPagination({
    required String workspaceId,
    required String channelId,
    int limit = 100,
    String? nextPageToken,
  }) {
    try {
      var filteredMessages = query(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId)),
      );

      // Sort by createTime descending
      filteredMessages.sort(
        (a, b) => (b.createTime ?? DateTime.now())
            .compareTo(a.createTime ?? DateTime.now()),
      );

      // Apply pagination if nextPageToken is provided
      if (nextPageToken != null && nextPageToken.isNotEmpty) {
        final tokenIndex =
            filteredMessages.indexWhere((m) => m.messageId == nextPageToken);
        if (tokenIndex != -1) {
          filteredMessages = filteredMessages.skip(tokenIndex + 1).toList();
        }
      }

      return filteredMessages.take(limit).toList();
    } catch (e) {
      Log.e('Error getting messages with pagination: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesWithPaginationAsync({
    required String workspaceId,
    required String channelId,
    int limit = 100,
    String? nextPageToken,
  }) async {
    return getMessagesWithPagination(
      workspaceId: workspaceId,
      channelId: channelId,
      limit: limit,
      nextPageToken: nextPageToken,
    );
  }

  @override
  Stream<List<Message>> getStreamMessagesFullScreen({
    required String workspaceId,
    required String channelId,
  }) {
    final filterMessageTypeRaw = [
      MessageViewTypeEnum.system.rawValue(),
    ];

    return watch(
      Message_.workspaceId
          .equals(workspaceId)
          .and(Message_.channelIdField.equals(channelId))
          .and(
            Message_.messageViewTypeRaw.notOneOf(filterMessageTypeRaw),
          ),
    ).map((query) {
      final messages = query.find();
      // Sort by createTime descending
      messages.sort(
        (a, b) => (b.createTime ?? DateTime.now())
            .compareTo(a.createTime ?? DateTime.now()),
      );
      return messages;
    });
  }

  @override
  Future<int> checkAndInsertOrUpdate(Message message) async {
    try {
      if (!_validateMessage(message)) {
        throw Exception('Invalid message data: ${message.messageId}');
      }

      _existsAndUpdate([message]);

      return put(message);
    } catch (e) {
      throw Exception(
        'Failed to check and insert/update message ${message.messageId}: $e',
      );
    }
  }

  @override
  bool isEmpty() {
    try {
      return count() == 0;
    } catch (e) {
      Log.e('Error checking if repository is empty: $e');
      return true;
    }
  }

  @override
  Future<bool> isEmptyAsync() async {
    return isEmpty();
  }

  @override
  Future<bool> deleteMessageByIdsAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) async {
    return deleteMessageByIds(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );
  }

  @override
  Future<int> deleteMessagesByIdsInChannelAsync({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) async {
    return deleteMessagesByIdsInChannel(
      workspaceId: workspaceId,
      channelId: channelId,
      messageIds: messageIds,
    );
  }

  @override
  Future<bool> deleteAllMessageOnChannelAsync({
    required String workspaceId,
    required String channelId,
  }) async {
    return deleteAllMessageOnChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );
  }

  @override
  void tagFirstMessage({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    try {
      final message = getMessageByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );

      if (message != null) {
        message.isFirstMessage = true;
        put(message);
      }
    } catch (e) {
      Log.e('Error tagging first message: $e');
    }
  }

  @override
  void tagFirstDMMessage({
    required String recipientId,
    required String messageId,
  }) {
    try {
      final channelQuery = _channelSessionBox
          .queryWith(Channel_.recipientIdField.equals(recipientId))
          .build();
      final dmChannel = channelQuery.findFirst();
      channelQuery.close();
      if (dmChannel == null) return;

      final workspaceId = dmChannel.workspaceId;
      final channelId = dmChannel.channelId;
      final message = getMessageByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );

      if (message != null) {
        message.isFirstMessage = true;
        put(message);
      }
    } catch (e) {
      Log.e('Error tagging first message: $e');
    }
  }

  @override
  Future<void> tagFirstMessageAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) async {
    tagFirstMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );
  }

  @override
  Message? updateReactions({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String? reactionsRaw,
  }) {
    try {
      final message = getMessageByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );

      if (message != null) {
        message.reactionsRaw = reactionsRaw ?? '';
        put(message);
        return message;
      }
      return null;
    } catch (e) {
      Log.e('Error updating reactions: $e');
      return null;
    }
  }

  @override
  Future<Message?> updateLocalReaction({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required List<Map<String, dynamic>> reactions,
  }) async {
    try {
      logRepository(
        'Updating local reaction',
        '$workspaceId:$channelId:$messageId',
      );

      final messageExist = getMessageByIds(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );

      if (messageExist == null) {
        logRepository(
          'Message not found for reaction update',
          '$workspaceId:$channelId:$messageId',
        );
        return null;
      }

      final reactionsExisted = messageExist.reactions ?? {};
      final Map<String, dynamic> mapReactions = {};

      for (final map in reactions) {
        final emoji = map['emoji'] as String;
        final total = map['total'] as int;
        final isReacted = reactionsExisted[emoji]?.isReacted ?? false;

        mapReactions[emoji] = {
          'isReacted': isReacted,
          'total': total,
        };
      }

      messageExist.reactionsRaw = jsonEncode(mapReactions);
      put(messageExist);

      logRepository(
        'Local reaction updated successfully',
        '$messageId with ${reactions.length} reactions',
      );
      return messageExist;
    } catch (e) {
      logRepository(
        'Error updating local reaction',
        '$workspaceId:$channelId:$messageId: $e',
      );
      return null;
    }
  }

  @override
  Future<Message?> updateLocalUserReaction({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String reactionsRaw,
  }) async {
    try {
      logRepository(
        'Updating local user reaction',
        '$workspaceId:$channelId:$messageId',
      );

      final updatedMessage = updateReactions(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
        reactionsRaw: reactionsRaw,
      );

      if (updatedMessage != null) {
        logRepository(
          'Local user reaction updated successfully',
          '$messageId',
        );
        return updatedMessage;
      } else {
        logRepository(
          'Failed to update local user reaction',
          '$workspaceId:$channelId:$messageId',
        );
        return null;
      }
    } catch (e) {
      logRepository(
        'Error updating local user reaction',
        '$workspaceId:$channelId:$messageId: $e',
      );
      return null;
    }
  }

  @override
  Future<Message?> updateReactionsAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String? reactionsRaw,
  }) async {
    return updateReactions(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      reactionsRaw: reactionsRaw,
    );
  }

  @override
  Future<void> updateMessageStatus({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String ref,
    required int messageStatusRaw,
  }) async {
    try {
      final message = getMessageByIdOrRef(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
        ref: ref,
      );

      if (message != null) {
        message.messageStatusRaw = messageStatusRaw;
        put(message);
      }
    } catch (e) {
      Log.e('Error updating message status: $e');
    }
  }

  @override
  List<Message>? getMessagesMentionOrContentArguments() {
    try {
      return query(
        Message_.mentionsRaw.notEquals('').or(
              Message_.contentArguments.notEquals(''),
            ),
      );
    } catch (e) {
      Log.e('Error getting messages with mentions or content arguments: $e');
      return null;
    }
  }

  @override
  Future<List<Message>?> getMessagesMentionOrContentArgumentsAsync() async {
    return getMessagesMentionOrContentArguments();
  }

  @override
  Stream<List<Message>> streamPinMessages({
    required String workspaceId,
    required String channelId,
  }) {
    return watch(
      Message_.workspaceId
          .equals(workspaceId)
          .and(Message_.channelIdField.equals(channelId))
          .and(Message_.isPinned.equals(true)),
    ).map((query) {
      final messages = query.find();
      messages.sort(
        (a, b) => (b.pinTime ?? DateTime.now())
            .compareTo(a.pinTime ?? DateTime.now()),
      );
      return messages;
    });
  }

  @override
  List<Message> getMessagesByStatus(int messageStatusRaw) {
    try {
      return query(Message_.messageStatusRaw.equals(messageStatusRaw));
    } catch (e) {
      Log.e('Error getting messages by status: $e');
      return [];
    }
  }

  @override
  Future<List<Message>> getMessagesByStatusAsync(int messageStatusRaw) async {
    return getMessagesByStatus(messageStatusRaw);
  }

  @override
  TranslatedResult? getTranslatedResult(String messageId) {
    try {
      final query = _translatedResultSessionBox
          .queryWith(TranslatedResult_.messageIdField.equals(messageId))
          .build();
      final result = query.findFirst();
      query.close();
      return result;
    } catch (e) {
      Log.e('Error getting translation result for message $messageId: $e');
      return null;
    }
  }

  @override
  Future<TranslatedResult?> getTranslatedResultAsync(String messageId) async {
    return getTranslatedResult(messageId);
  }

  @override
  int insertTranslatedResult(TranslatedResult translatedResult) {
    try {
      if (!_validateTranslatedResult(translatedResult)) {
        throw Exception(
          'Invalid translation result data: ${translatedResult.messageId}',
        );
      }

      return _translatedResultSessionBox.put(translatedResult);
    } catch (e) {
      throw Exception(
        'Failed to insert translation result ${translatedResult.messageId}: $e',
      );
    }
  }

  @override
  Future<int> insertTranslatedResultAsync(
    TranslatedResult translatedResult,
  ) async {
    return insertTranslatedResult(translatedResult);
  }

  @override
  bool updateTranslatedResult(TranslatedResult translatedResult) {
    try {
      if (!_validateTranslatedResult(translatedResult)) {
        return false;
      }

      _translatedResultSessionBox.put(translatedResult);
      return true;
    } catch (e) {
      Log.e(
        'Error updating translation result ${translatedResult.messageId}: $e',
      );
      return false;
    }
  }

  @override
  Future<bool> updateTranslatedResultAsync(
    TranslatedResult translatedResult,
  ) async {
    return updateTranslatedResult(translatedResult);
  }

  @override
  bool deleteTranslatedResult(String messageId) {
    try {
      final translatedResult = getTranslatedResult(messageId);
      if (translatedResult != null) {
        return _translatedResultSessionBox.remove(translatedResult.id);
      }
      return false;
    } catch (e) {
      Log.e('Error deleting translation result for message $messageId: $e');
      return false;
    }
  }

  @override
  Future<bool> deleteTranslatedResultAsync(String messageId) async {
    return deleteTranslatedResult(messageId);
  }

  @override
  bool hasTranslatedResult(String messageId) {
    return getTranslatedResult(messageId) != null;
  }

  @override
  Future<bool> hasTranslatedResultAsync(String messageId) async {
    return hasTranslatedResult(messageId);
  }

  @override
  Stream<TranslatedResult?> watchTranslatedResult(String messageId) {
    return _translatedResultSessionBox
        .watch(TranslatedResult_.messageIdField.equals(messageId))
        .map((query) => query.findFirst());
  }

  @override
  List<TranslatedResult> getTranslatedResultsByStatus(int statusRaw) {
    try {
      return _translatedResultSessionBox
          .query(TranslatedResult_.statusRaw.equals(statusRaw));
    } catch (e) {
      Log.e('Error getting translation results by status $statusRaw: $e');
      return [];
    }
  }

  @override
  Future<List<TranslatedResult>> getTranslatedResultsByStatusAsync(
    int statusRaw,
  ) async {
    return getTranslatedResultsByStatus(statusRaw);
  }

  @override
  List<TranslatedResult> getCompletedTranslatedResults() {
    return getTranslatedResultsByStatus(1); // 1 = Completed
  }

  @override
  Future<List<TranslatedResult>> getCompletedTranslatedResultsAsync() async {
    return getCompletedTranslatedResults();
  }

  @override
  List<TranslatedResult> getFailedTranslatedResults() {
    return getTranslatedResultsByStatus(-1); // -1 = Failed
  }

  @override
  Future<List<TranslatedResult>> getFailedTranslatedResultsAsync() async {
    return getFailedTranslatedResults();
  }

  @override
  List<TranslatedResult> getInProgressTranslatedResults() {
    return getTranslatedResultsByStatus(0); // 0 = In Progress
  }

  @override
  Future<List<TranslatedResult>> getInProgressTranslatedResultsAsync() async {
    return getInProgressTranslatedResults();
  }

  @override
  Future<bool> createTranslationResult({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String sessionKey,
    required String originalContent,
    required String originalLanguage,
    required String targetLanguage,
    String translatedContent = '',
    int statusRaw = 0,
    bool isShowTranslateResult = false,
  }) async {
    try {
      final translatedResult = TranslatedResult.create(
        workspaceId: workspaceId,
        channelId: channelId,
        sessionKey: sessionKey,
        messageId: messageId,
        originalContent: originalContent,
        translatedContent: translatedContent,
        originalLanguage: originalLanguage,
        targetLanguage: targetLanguage,
        statusRaw: statusRaw,
        isShowTranslateResult: isShowTranslateResult,
      );

      insertTranslatedResult(translatedResult);
      return true;
    } catch (e) {
      Log.e('Error creating translation result for message $messageId: $e');
      return false;
    }
  }

  @override
  Future<bool> markTranslationAsCompleted({
    required String messageId,
    required String translatedContent,
    double qualityScore = 0.0,
    double confidenceScore = 0.0,
    int processingTimeMs = 0,
  }) async {
    try {
      final existing = getTranslatedResult(messageId);
      if (existing == null) {
        Log.e('Translation result not found for message $messageId');
        return false;
      }

      final updated = existing.copyWith(
        statusRaw: 1,
        // Completed
        translatedContent: translatedContent,
        qualityScore: qualityScore,
        confidenceScore: confidenceScore,
        processingTimeMs: processingTimeMs,
        updateTime: DateTime.now(),
      );

      return updateTranslatedResult(updated);
    } catch (e) {
      Log.e(
        'Error marking translation as completed for message $messageId: $e',
      );
      return false;
    }
  }

  @override
  Future<bool> markTranslationAsFailed({
    required String messageId,
    required String errorMessage,
    int processingTimeMs = 0,
  }) async {
    try {
      final existing = getTranslatedResult(messageId);
      if (existing == null) {
        Log.e('Translation result not found for message $messageId');
        return false;
      }

      final updated = existing.copyWith(
        statusRaw: -1,
        // Failed
        errorMessage: errorMessage,
        processingTimeMs: processingTimeMs,
        updateTime: DateTime.now(),
      );

      return updateTranslatedResult(updated);
    } catch (e) {
      Log.e('Error marking translation as failed for message $messageId: $e');
      return false;
    }
  }

  @override
  Future<bool> toggleTranslationVisibility(String messageId) async {
    try {
      final existing = getTranslatedResult(messageId);
      if (existing == null) {
        Log.e('Translation result not found for message $messageId');
        return false;
      }

      final updated = existing.copyWith(
        isShowTranslateResult: !existing.isShowTranslateResult,
        updateTime: DateTime.now(),
      );

      return updateTranslatedResult(updated);
    } catch (e) {
      Log.e('Error toggling translation visibility for message $messageId: $e');
      return false;
    }
  }

  bool _validateTranslatedResult(TranslatedResult translatedResult) {
    return translatedResult.messageId.isNotEmpty &&
        translatedResult.workspaceId.isNotEmpty &&
        translatedResult.channelId.isNotEmpty;
  }

  /// [OPTIMIZATION] Reduced transaction scope and improved batch processing
  List<int> _insertOrUpdateMessages(
    List<Message> messages, {
    bool isInsertOne = false,
  }) {
    try {
      final messagesToUpdate = messages.where(_validateMessage).toList();

      if (messagesToUpdate.isEmpty) {
        return messages.map((m) => m.id).toList();
      }

      _existsAndUpdate(messagesToUpdate);

      // [OPTIMIZATION] Prepare data outside transaction to minimize lock time
      final allAttachmentIds = messagesToUpdate
          .expand(
            (message) => message.mediaAttachments.map((a) => a.attachmentId),
          )
          .where((id) => id.isNotEmpty)
          .toSet()
          .toList();

      final allAttachmentRefs = messages
          .expand((message) => message.mediaAttachments.map((a) => a.ref))
          .where((ref) => ref.isNotEmpty)
          .toSet()
          .toList();

      // [OPTIMIZATION] Query existing attachments outside transaction
      final existingAttachments =
          (allAttachmentIds.isNotEmpty || allAttachmentRefs.isNotEmpty)
              ? _attachmentSessionBox.query(
                  allAttachmentIds.isNotEmpty && allAttachmentRefs.isNotEmpty
                      ? Attachment_.attachmentId
                          .oneOf(allAttachmentIds)
                          .or(Attachment_.ref.oneOf(allAttachmentRefs))
                      : allAttachmentIds.isNotEmpty
                          ? Attachment_.attachmentId.oneOf(allAttachmentIds)
                          : Attachment_.ref.oneOf(allAttachmentRefs),
                )
              : <Attachment>[];

      final attachmentIdToExisting = {
        for (var att in existingAttachments) att.attachmentId: att,
      };
      final attachmentRefToExisting = {
        for (var att in existingAttachments) att.ref: att,
      };

      final newMessageCount = isInsertOne
          ? messagesToUpdate
              .where(
                (msg) =>
                    msg.id <= 0 &&
                    !msg.messageViewType.isSystemOrLocalType &&
                    msg.userId != Config.getInstance().activeSessionKey,
              )
              .length
          : 0;

      // [OPTIMIZATION] Prepare all attachments outside transaction
      final allNewAttachments = <Attachment>[];
      final channelsToUpdate = <String, Message>{};

      for (var message in messagesToUpdate) {
        final newAttachments = List<Attachment>.from(message.mediaAttachments)
            .where((a) => a.validate())
            .toList();
        message.mediaAttachments.clear();

        for (var attachment in newAttachments) {
          _updateAttachmentId(
            attachmentIdToExisting,
            attachment,
            attachmentRefToExisting,
            message,
          );
          attachment.message.target = message;
          message.mediaAttachments.add(attachment);
          allNewAttachments.add(attachment);
        }

        // [OPTIMIZATION] Update message status outside transaction
        final isPhoto =
            message.attachmentTypeRaw == AttachmentTypeEnum.PHOTO.rawValue();
        final isMessageFailed =
            message.messageStatusRaw == MessageStatusEnum.FAILURE.rawValue();
        final isMessageSending =
            message.mediaAttachments.any((attachment) => attachment.isPending);
        if (isPhoto && !isMessageFailed) {
          message.messageStatusRaw = isMessageSending
              ? MessageStatusEnum.PENDING.rawValue()
              : MessageStatusEnum.UNRECOGNIZED.rawValue();
        }

        // [OPTIMIZATION] Prepare channel updates outside transaction
        final channelKey = '${message.workspaceId}|${message.channelId}';
        final currentLatest = channelsToUpdate[channelKey];
        if (currentLatest == null ||
            (message.createTime != null &&
                currentLatest.createTime != null &&
                message.createTime!.isAfter(currentLatest.createTime!))) {
          channelsToUpdate[channelKey] = message;
        }
      }

      // [OPTIMIZATION] Minimal transaction scope - only database writes
      messageBox.store.runInTransaction(TxMode.write, () {
        // [OPTIMIZATION] Batch remove existing attachments
        if (existingAttachments.isNotEmpty) {
          _attachmentSessionBox
              .removeMany(existingAttachments.map((att) => att.id).toList());
        }

        // [OPTIMIZATION] Batch insert new attachments
        if (allNewAttachments.isNotEmpty) {
          _attachmentSessionBox.putMany(allNewAttachments);
        }

        // [OPTIMIZATION] Batch insert/update messages
        super.putMany(messagesToUpdate);
      });

      // [OPTIMIZATION] Update channel metadata outside transaction to reduce lock time
      for (final entry in channelsToUpdate.entries) {
        final parts = entry.key.split('|');
        final workspaceId = parts[0];
        final channelId = parts[1];
        final latestMessageForChannel = entry.value;

        _updateChannelMetadata(
          workspaceId: workspaceId,
          channelId: channelId,
          message: latestMessageForChannel,
          newMessageCount: newMessageCount,
        );
      }

      return messagesToUpdate.map((m) => m.id).toList();
    } catch (e) {
      logRepository('Error in _insertOrUpdateMessages', '$e');
      return [];
    }
  }

  void _updateAttachmentId(
    Map<String, Attachment> attachmentIdToExisting,
    Attachment attachment,
    Map<String, Attachment> attachmentRefToExisting,
    Message message,
  ) {
    if (attachmentIdToExisting.containsKey(attachment.attachmentId)) {
      attachment.id = attachmentIdToExisting[attachment.attachmentId]!.id;
    } else if (attachmentRefToExisting.containsKey(attachment.ref) &&
        (attachmentRefToExisting[attachment.ref]!.isUploading ||
            attachmentRefToExisting[attachment.ref]!.messageIdField ==
                message.messageId)) {
      attachment.id = attachmentRefToExisting[attachment.ref]!.id;
    }
  }

  void _handleKeepLocalMetadata(Message newMessage, Message oldMessage) {
    //=== Keep local metadata ===
    newMessage.id = oldMessage.id;
    newMessage.isFirstMessage = oldMessage.isFirstMessage;

    //=== Handle invitation messages ===
    if (newMessage.isInvitation && oldMessage.isInvitation) {
      final oldEmbedData = oldMessage.embed?.first;
      final newEmbedData = newMessage.embed?.first;
      if (oldEmbedData != null &&
          newEmbedData != null &&
          oldEmbedData['invitationData']['isExpired'] == true) {
        newEmbedData['invitationData']['isExpired'] = true;
      }
      newMessage.embedRaw = jsonEncode([newEmbedData]);
      return;
    }

    //=== Handle media attachments ===
    // If the old message has media attachments, we need to ensure they are
    if (oldMessage.mediaAttachments.isEmpty) return;

    final oldAttachments = List<Attachment>.from(oldMessage.mediaAttachments)
        .where((a) => a.validate())
        .toList();
    final newAttachmentRefs = {
      for (var att in newMessage.mediaAttachments) att.ref: att,
    };

    for (int index = 0; index < oldAttachments.length; index++) {
      // Skip if the attachment.ref is empty
      if (StringUtils.isNullOrEmpty(oldAttachments[index].ref)) continue;

      final existingAttachment = oldAttachments[index];
      final updatedAttachment = newAttachmentRefs[oldAttachments[index].ref];
      if (updatedAttachment == null) {
        existingAttachment.message.target = newMessage;
        newMessage.mediaAttachments.insert(index, existingAttachment);
      } else {
        updatedAttachment.id = existingAttachment.id;
      }
    }
  }

  void _existsAndUpdate(List<Message> messages) {
    if (messages.isEmpty) return;

    try {
      final messageIds = messages.map((m) => m.messageId).toSet().toList();
      final refs = messages
          .map((m) => m.ref)
          .where((ref) => ref.isNotEmpty)
          .toSet()
          .toList();

      if (messageIds.isEmpty && refs.isEmpty) {
        return;
      }

      Condition<Message>? queryCondition;
      if (messageIds.isNotEmpty) {
        queryCondition = Message_.messageId.oneOf(messageIds);
      }
      if (refs.isNotEmpty) {
        final refCondition = Message_.ref.oneOf(refs);
        queryCondition = (queryCondition == null)
            ? refCondition
            : queryCondition.or(refCondition);
      }

      final existingMessages = query(queryCondition!);

      if (existingMessages.isEmpty) {
        return;
      }

      final messageIdToExisting = {
        for (var msg in existingMessages) msg.messageId: msg,
      };
      final refToExisting = {
        for (var msg in existingMessages)
          if (msg.ref.isNotEmpty) msg.ref: msg,
      };

      for (final message in messages) {
        Message? existingMessage;

        existingMessage = messageIdToExisting[message.messageId];

        if (existingMessage == null && message.ref.isNotEmpty) {
          existingMessage = refToExisting[message.ref];
        }

        if (existingMessage != null) {
          _handleKeepLocalMetadata(message, existingMessage);
        }
      }
    } catch (e) {
      Log.e('Error in _existsAndUpdate: $e');
    }
  }

  @override
  bool insertOrUpdateAttachment({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required Attachment attachment,
  }) {
    try {
      final message = getFirst(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId))
            .and(Message_.messageId.equals(messageId)),
      );

      if (message == null) {
        logRepository(
          'Message not found',
          '$workspaceId:$channelId:$messageId',
        );
        return false;
      }

      Attachment? existingAttachment;
      try {
        existingAttachment = message.mediaAttachments.firstWhere(
          (att) =>
              (attachment.attachmentId.isNotEmpty &&
                  att.attachmentId == attachment.attachmentId) ||
              (attachment.ref.isNotEmpty && att.ref == attachment.ref),
        );
      } catch (e) {
        existingAttachment = null;
      }

      if (existingAttachment != null) {
        attachment.id = existingAttachment.id;
        existingAttachment.updateWith(attachment);
      }

      attachment.message.target = message;

      final success = _attachmentSessionBox.put(attachment) > 0;

      put(message);

      return success;
    } catch (e) {
      logRepository('Error inserting/updating attachment', '$e');
      return false;
    }
  }

  @override
  Future<bool> insertOrUpdateAttachmentAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required Attachment attachment,
  }) async {
    return insertOrUpdateAttachment(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      attachment: attachment,
    );
  }

  @override
  Future<bool> updateAttachment({
    required Attachment attachment,
  }) async {
    try {
      final existingAttachment = _attachmentSessionBox.getFirstWith(
        Attachment_.attachmentId
            .equals(attachment.attachmentId)
            .or(Attachment_.ref.equals(attachment.ref)),
      );

      if (existingAttachment != null) {
        attachment.id = existingAttachment.id;
      }

      final success = _attachmentSessionBox.put(attachment) > 0;
      return success;
    } catch (e) {
      logRepository('Error updating attachment', '$e');
      return false;
    }
  }

  @override
  Future<bool> updateAttachmentAsync({
    required Attachment attachment,
  }) async {
    return updateAttachment(attachment: attachment);
  }

  @override
  Message? updateAttachmentAndGetMessage({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required Attachment attachment,
  }) {
    try {
      insertOrUpdateAttachment(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
        attachment: attachment,
      );

      return getFirst(
        Message_.workspaceId
            .equals(workspaceId)
            .and(Message_.channelIdField.equals(channelId))
            .and(Message_.messageId.equals(messageId)),
      );
    } catch (e) {
      logRepository('Error updating attachment and getting message', '$e');
      return null;
    }
  }

  @override
  Future<Message?> updateAttachmentAndGetMessageAsync({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required Attachment attachment,
  }) async {
    return updateAttachmentAndGetMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      attachment: attachment,
    );
  }

  @override
  bool deleteAttachmentByDBId(int dbId) {
    try {
      final attachment = _attachmentSessionBox.get(dbId);
      if (attachment == null) {
        logRepository('Attachment not found', 'DB ID: $dbId');
        return false;
      }

      return _attachmentSessionBox.remove(dbId);
    } catch (e) {
      logRepository('Error deleting attachmen', 'DB ID $dbId: $e');
      return false;
    }
  }

  @override
  Future<bool> deleteAttachmentByDBIdAsync(int dbId) async {
    return deleteAttachmentByDBId(dbId);
  }

  @override
  Message? updateAttachmentStatusAndGetMessage({
    required String messageRef,
    required String attachmentRef,
    required int attachmentStatusRaw,
  }) {
    try {
      final message = getMessageByRef(ref: messageRef);
      if (message == null) {
        logRepository('Message not found', 'ref: $messageRef');
        return null;
      }

      final attachmentIndex = message.mediaAttachments.indexWhere(
        (attachment) => attachment.ref == attachmentRef,
      );

      if (attachmentIndex == -1) {
        logRepository(
          'Attachment not found in message',
          'messageRef: $messageRef, attachmentRef: $attachmentRef',
        );
        return null;
      }

      final attachment = message.mediaAttachments[attachmentIndex];

      attachment.attachmentStatusRaw = attachmentStatusRaw;
      _attachmentSessionBox.put(attachment);

      final hasUploading = message.mediaAttachments.any(
        (att) =>
            att.attachmentStatusRaw ==
            AttachmentStatusEnum.UPLOADING.rawValue(),
      );

      if (!hasUploading) {
        final hasSuccessOrUnspecified = message.mediaAttachments.any(
          (att) =>
              att.attachmentStatusRaw ==
                  AttachmentStatusEnum.SUCCESS.rawValue() ||
              att.attachmentStatusRaw ==
                  AttachmentStatusEnum.UNSPECIFIED.rawValue(),
        );

        message.messageStatusRaw = hasSuccessOrUnspecified
            ? MessageStatusEnum.UNRECOGNIZED.rawValue()
            : MessageStatusEnum.FAILURE.rawValue();
      }

      put(message);

      logRepository(
        'Updated attachment status and message',
        'messageRef: $messageRef, attachmentRef: $attachmentRef, status: $attachmentStatusRaw',
      );

      return message;
    } catch (e) {
      logRepository(
        'Error updating attachment status and getting message',
        'messageRef: $messageRef, attachmentRef: $attachmentRef: $e',
      );
      return null;
    }
  }

  @override
  Future<Message?> updateAttachmentStatusAndGetMessageAsync({
    required String messageRef,
    required String attachmentRef,
    required int attachmentStatusRaw,
  }) async {
    return updateAttachmentStatusAndGetMessage(
      messageRef: messageRef,
      attachmentRef: attachmentRef,
      attachmentStatusRaw: attachmentStatusRaw,
    );
  }

  Future<Channel?> _ensureChannelExists({
    required String workspaceId,
    required String channelId,
    int retries = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    for (var i = 0; i < retries; i++) {
      final channel = _channelSessionBox.getFirstWith(
        Channel_.workspaceId
            .equals(workspaceId)
            .and(Channel_.channelId.equals(channelId)),
      );

      if (channel != null) {
        return channel;
      }

      if (i < retries - 1) {
        await Future.delayed(delay);
      }
    }
    return null;
  }

  Future<void> _updateChannelMetadata({
    required String workspaceId,
    required String channelId,
    Message? message,
    String? lastSeenMessageId,
    int newMessageCount = 0,
  }) async {
    // Step 1: Ensure the channel exists before proceeding.
    final channelExists = await _ensureChannelExists(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    // If channel doesn't exist after retries, abort the metadata update.
    if (channelExists == null) {
      return;
    }

    // Step 2: Get or create a metadata object. This is now safe from race conditions.
    final metadata = _getOrCreateChannelMetadata(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    // Step 3: Update lastSeenMessageId if provided.
    if (lastSeenMessageId != null && lastSeenMessageId.isNotEmpty) {
      if (metadata.lastSeenMessageId.isEmpty ||
          lastSeenMessageId.compareTo(metadata.lastSeenMessageId) > 0) {
        metadata.lastSeenMessageId = lastSeenMessageId;
      }
    }

    // Step 4: Fetch the absolute latest message for the channel.
    final lastMessage = _getLastMessageOfChannel(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    // Step 5: Update metadata based on the latest message.
    if (lastMessage != null) {
      // Avoid unnecessary updates if the last message hasn't changed.
      if (metadata.lastMessageId == lastMessage.messageId &&
          metadata.lastMessageContent == lastMessage.content) {
        // Still need to update unread count if lastSeen has changed
        if (lastSeenMessageId == null) {
          return;
        }
      }
      _updateMetadataFromMessage(metadata: metadata, lastMessage: lastMessage);
    } else {
      _clearMetadataForEmptyChannel(metadata: metadata);
    }

    // Step 6: Calculate unread count.
    if (metadata.lastSeenMessageId.isNotEmpty) {
      metadata.unreadCount = _countUnreadMessages(
        workspaceId: workspaceId,
        channelId: channelId,
        lastSeenMessageId: metadata.lastSeenMessageId,
      );
    } else {
      metadata.unreadCount += newMessageCount;
    }

    _saveMetadata(metadata);
  }

  ChannelMetadata _getOrCreateChannelMetadata({
    required String workspaceId,
    required String channelId,
  }) {
    final metadata = _metadataBox.getFirstWith(
      ChannelMetadata_.workspaceId
          .equals(workspaceId)
          .and(ChannelMetadata_.channelId.equals(channelId)),
    );

    return metadata ??
        ChannelMetadata.create(
          workspaceId: workspaceId,
          channelId: channelId,
          sessionKey: _activeSessionKey,
        );
  }

  void _updateMetadataFromMessage({
    required ChannelMetadata metadata,
    required Message lastMessage,
  }) {
    metadata.lastMessageId = lastMessage.messageId;
    metadata.lastMessageContent = lastMessage.content;
    metadata.lastMessageContentArgumentsString = lastMessage.contentArguments;
    metadata.lastMessageMentions = lastMessage.mentions ?? [];
    metadata.lastMessageViewTypeRaw = lastMessage.messageViewTypeRaw;
    metadata.createTime = lastMessage.createTime;

    if (lastMessage.userId == _activeSessionKey &&
        lastMessage.isTemp == false &&
        lastMessage.messageStatus != MessageStatusEnum.PENDING &&
        lastMessage.messageStatus != MessageStatusEnum.FAILURE &&
        lastMessage.messageId.compareTo(metadata.lastSeenMessageId) > 0) {
      metadata.lastSeenMessageId = lastMessage.messageId;
    }
  }

  void _clearMetadataForEmptyChannel({required ChannelMetadata metadata}) {
    metadata.lastMessageId = '';
    metadata.lastMessageContent = '';
    metadata.lastMessageContentArguments = [];
    metadata.lastMessageMentions = [];
    metadata.lastMessageViewTypeRaw = 0;
    metadata.createTime = null;
  }

  void _saveMetadata(ChannelMetadata metadata) {
    metadata.updateTime = DateTime.now();
    _metadataBox.put(metadata);
  }

  Message? _getLastMessageOfChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final query = messageBox
        .queryWith(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelIdField.equals(channelId)),
        )
        .order(Message_.createTime, flags: Order.descending)
        .build()
      ..limit = 1;
    final message = query.findFirst();
    query.close();
    return message;
  }

  int _countUnreadMessages({
    required String workspaceId,
    required String channelId,
    required String? lastSeenMessageId,
  }) {
    var conditions = Message_.workspaceId
        .equals(workspaceId)
        .and(Message_.channelIdField.equals(channelId))
        .and(Message_.isTemp.equals(false))
        .and(
          Message_.messageStatusRaw.notOneOf([
            MessageStatusEnum.FAILURE.rawValue(),
            MessageStatusEnum.PENDING.rawValue(),
          ]),
        )
        .and(
          Message_.messageViewTypeRaw.notOneOf([
            MessageViewTypeEnum.loading.rawValue(),
            MessageViewTypeEnum.systemTime.rawValue(),
            MessageViewTypeEnum.system.rawValue(),
          ]),
        );

    if (lastSeenMessageId != null && lastSeenMessageId.isNotEmpty) {
      conditions =
          conditions.and(Message_.messageId.greaterThan(lastSeenMessageId));
    }

    conditions = conditions.and(
      Message_.userIdField.notEquals(Config.getInstance().activeSessionKey!),
    );

    final query = messageBox.queryWith(conditions).build();
    final count = query.count();
    query.close();
    return count;
  }

  @override
  Future<bool> deleteMessageAsync(String messageId) async {
    return deleteMessage(messageId);
  }

  @override
  Future<int> deleteMessagesByIdsAsync(List<String> messageIds) async {
    return deleteMessagesByIds(messageIds);
  }

  @override
  Future<int> deleteAllMessagesAsync() async {
    return deleteAllMessages();
  }

  @override
  Future<bool> deleteMessagesBySessionAsync(String sessionKey) async {
    return deleteMessagesBySession(sessionKey);
  }

  @override
  bool deleteMessagesBySession(String sessionKey) {
    try {
      final box = messageBox.store.box<Message>();
      final query =
          box.query(Message_.sessionKeyField.equals(sessionKey)).build();
      query.remove();
      query.close();

      return true;
    } catch (e) {
      Log.e('Error deleting messages by session $sessionKey: $e');
      return false;
    }
  }

  @override
  ChannelLocalMetadata? getChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _channelMetadataSessionBox
        .queryWith(
          ChannelLocalMetadata_.workspaceId
              .equals(workspaceId)
              .and(ChannelLocalMetadata_.channelId.equals(channelId)),
        )
        .build();
    final metadata = query.findFirst();
    query.close();
    return metadata;
  }

  @override
  Future<int> insertOrUpdateChannelLocalMetadata(
    ChannelLocalMetadata metadata,
  ) async {
    final existing = getChannelLocalMetadata(
      workspaceId: metadata.workspaceId,
      channelId: metadata.channelId,
    );
    if (existing != null) {
      metadata.id = existing.id;
    }
    return _channelMetadataSessionBox.put(metadata);
  }

  @override
  Future<bool> deleteChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
  }) async {
    final existing = getChannelLocalMetadata(
      workspaceId: workspaceId,
      channelId: channelId,
    );
    if (existing != null) {
      return _channelMetadataSessionBox.remove(existing.id);
    }
    return false;
  }

  @override
  Stream<ChannelLocalMetadata?> watchChannelLocalMetadata({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _channelMetadataSessionBox.queryWith(
      ChannelLocalMetadata_.workspaceId
          .equals(workspaceId)
          .and(ChannelLocalMetadata_.channelId.equals(channelId)),
    );
    return query
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((q) => q.findFirst());
  }

  @override
  Stream<List<TranslatedResult>> watchAllTranslatedResultsInChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _translatedResultSessionBox.queryWith(
      TranslatedResult_.workspaceId
          .equals(workspaceId)
          .and(TranslatedResult_.channelId.equals(channelId)),
    );
    return query
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((q) => q.find());
  }

  @override
  Stream<List<TranslatedResult>> watchTranslatedResultsByMessageIds(
    Set<String> messageIds,
  ) {
    if (messageIds.isEmpty) {
      return Stream.value([]);
    }

    final query = _translatedResultSessionBox.queryWith(
      TranslatedResult_.messageIdField.oneOf(messageIds.toList()),
    );

    return query
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((q) => q.find());
  }

  @override
  int deleteAllLocalMetadataOfSession(String sessionKey) {
    final query = _channelMetadataSessionBox.box
        .query(ChannelLocalMetadata_.sessionKey.equals(sessionKey))
        .build();
    final count = query.remove();
    query.close();
    return count;
  }

  @override
  Future<List<int>> insertOrUpdateAllTranslatedResults(
    List<TranslatedResult> translatedResults,
  ) async {
    if (translatedResults.isEmpty) return [];

    _checkAndUpdateExistingIds(translatedResults);

    return _translatedResultSessionBox.putMany(translatedResults);
  }

  void _checkAndUpdateExistingIds(List<TranslatedResult> results) {
    if (results.isEmpty) return;
    final messageIds = results.map((e) => e.messageId).toList();
    final existingInDb = _translatedResultSessionBox
        .query(TranslatedResult_.messageIdField.oneOf(messageIds));
    final mapExisting = {for (var e in existingInDb) e.messageId: e};

    for (var result in results) {
      if (mapExisting.containsKey(result.messageId)) {
        result.id = mapExisting[result.messageId]!.id;
      }
    }
  }

  @override
  int deleteAllTranslatedResultsInChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _translatedResultSessionBox
        .queryWith(
          TranslatedResult_.workspaceId
              .equals(workspaceId)
              .and(TranslatedResult_.channelId.equals(channelId)),
        )
        .build();
    final count = query.remove();
    query.close();
    return count;
  }

  @override
  int deleteAllTranslatedResultsOfSession(String sessionKey) {
    final query = _translatedResultSessionBox.box
        .query(TranslatedResult_.sessionKey.equals(sessionKey))
        .build();
    final count = query.remove();
    query.close();

    // Clear caches for session
    clearAllCaches();

    return count;
  }

  @override
  bool isEmptyTranslatedResult() {
    return _translatedResultSessionBox.isEmpty();
  }

  @override
  Future<bool> updateTranslationStatus({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required int newStatusValue,
  }) async {
    final existing = await getTranslatedResultAsync(messageId);
    if (existing != null) {
      existing.statusRaw = newStatusValue;
      _translatedResultSessionBox.put(existing);
      return true;
    }
    return false;
  }

  @override
  List<TranslatedResult> getTranslatingMessages() {
    return _translatedResultSessionBox.query(
      TranslatedResult_.statusRaw
          .equals(TranslatedStatusEnum.TRANSLATING.value),
    );
  }

  @override
  bool deleteMessagesForChannel({
    required String workspaceId,
    required String channelId,
  }) {
    try {
      final query = messageBox
          .queryWith(
            Message_.workspaceId
                .equals(workspaceId)
                .and(Message_.channelIdField.equals(channelId)),
          )
          .build();
      final numberOfMessages = query.remove();
      query.close();

      _updateChannelMetadata(
        workspaceId: workspaceId,
        channelId: channelId,
      );
      Log.e(
        name: 'MessageRepositoryImpl.deleteMessagesForChannel',
        numberOfMessages,
      );
      return numberOfMessages > 0;
    } catch (e) {
      Log.e('Error deleting messages for channel $workspaceId:$channelId: $e');
      return false;
    }
  }

  @override
  Message? upsertPinUnpinMessage({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required bool status,
    DateTime? pinTime,
  }) {
    unpinAllMessages(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    final pinningMessage = getMessageByIds(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );

    if (pinningMessage == null) {
      return null;
    }

    pinningMessage.isPinned = status;
    pinningMessage.pinTime = pinTime ?? TimeUtils.now();

    forceUpdateMessageAll([pinningMessage]);

    return pinningMessage;
  }

  List<Message> getPinMessages({
    required String workspaceId,
    required String channelId,
  }) {
    final filterMessageTypeRaw = [
      MessageViewTypeEnum.system.rawValue(),
    ];
    final query = messageBox
        .queryWith(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelIdField.equals(channelId))
              .and(Message_.messageViewTypeRaw.notOneOf(filterMessageTypeRaw))
              .and(Message_.isPinned.equals(true)),
        )
        .build();
    final pinnedMessages = query.find();
    query.close();
    return pinnedMessages;
  }

  @override
  Stream<List<Message>> watchPinnedMessages({
    required String workspaceId,
    required String channelId,
  }) {
    final filterMessageTypeRaw = [
      MessageViewTypeEnum.system.rawValue(),
    ];
    final query = messageBox
        .queryWith(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelIdField.equals(channelId))
              .and(Message_.messageViewTypeRaw.notOneOf(filterMessageTypeRaw))
              .and(Message_.isPinned.equals(true)),
        )
        .order(Message_.createTime, flags: Order.descending);

    return query
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((q) => q.find());
  }

  void unpinAllMessages({
    required String workspaceId,
    required String channelId,
  }) {
    final pinnedMessages = getPinMessages(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    List<Message> changePinnedMessage = pinnedMessages.map((item) {
      item.isPinned = false;
      item.pinTime = null;
      return item;
    }).toList();

    forceUpdateMessageAll(changePinnedMessage);
  }

  void updateLastSeenMessageId({
    required String workspaceId,
    required String channelId,
    required String lastSeenMessageId,
  }) {
    _updateChannelMetadata(
      workspaceId: workspaceId,
      channelId: channelId,
      lastSeenMessageId: lastSeenMessageId,
    );
  }

  @override
  Message? deleteLocalAttachmentByRefOrId({
    String? attachmentId,
    String? attachmentRef,
  }) {
    try {
      if (attachmentId == null && attachmentRef == null) {
        logRepository('Both attachmentId and attachmentRef are null', '');
        return null;
      }
      // Find attachment by id or ref
      final attachment = _attachmentSessionBox.getFirstWith(
        (attachmentId != null)
            ? Attachment_.attachmentId.equals(attachmentId)
            : Attachment_.ref.equals(attachmentRef!),
      );
      if (attachment == null) {
        logRepository(
          'Attachment not found',
          'attachmentId: \$attachmentId, attachmentRef: \$attachmentRef',
        );
        return null;
      }
      // Find message containing this attachment by iterating all messages
      final messages = messageBox.getAll();
      Message? message;
      try {
        message = messages.firstWhere(
          (msg) => msg.mediaAttachments.any(
            (att) =>
                (attachmentId != null && att.attachmentId == attachmentId) ||
                (attachmentRef != null && att.ref == attachmentRef),
          ),
          orElse: null,
        );
      } catch (_) {
        message = null;
      }
      if (message == null) {
        return null;
      }
      // Remove attachment from the list
      message.mediaAttachments.removeWhere(
        (att) =>
            (attachmentId != null && att.attachmentId == attachmentId) ||
            (attachmentRef != null && att.ref == attachmentRef),
      );
      // Remove attachment from the box
      _attachmentSessionBox.remove(attachment.id);
      // Update the message in the box
      super.put(message);
      logRepository(
        'Deleted local attachment and updated message',
        'messageId: \${message.messageId}, attachmentId: \$attachmentId, attachmentRef: \$attachmentRef',
      );
      return message;
    } catch (e) {
      logRepository('Error deleting local attachment', '\$e');
      return null;
    }
  }

  int deleteAttachmentsByMessageIds(List<String> messageIds) {
    if (messageIds.isEmpty) return 0;

    final query = _attachmentSessionBox
        .queryWith(
          Attachment_.messageIdField.oneOf(messageIds),
        )
        .build();
    final count = query.remove();
    query.close();
    return count;
  }

  // Legacy cache management methods for message lists
  void _updateMessageListCache(String key, List<Message> messages) {
    _messageListCache[key] = List.from(messages);
    _messageListCacheTimestamps[key] = DateTime.now();

    // Update LRU order
    _messageListCacheOrder.remove(key);
    _messageListCacheOrder.add(key);

    // Enforce cache size limit (keeping legacy behavior)
    while (_messageListCache.length > 1000) {
      final oldestKey = _messageListCacheOrder.removeAt(0);
      _messageListCache.remove(oldestKey);
      _messageListCacheTimestamps.remove(oldestKey);
    }
  }

  bool _isCacheExpired(DateTime timestamp) {
    return DateTime.now().difference(timestamp) > _cacheExpiration;
  }

  void dispose() {
    _privateDataSyncSubscription?.cancel();
  }

  void _updateChannelIdForDMTempMessage(UpdateChannelIdForTempDMMessage event) {
    final query = messageBox
        .queryWith(
          Message_.channelIdField.equals(event.oldChannelId) &
              Message_.workspaceId.equals(event.oldWorkspaceId),
        )
        .build();
    final messages = query.find();
    query.close();

    if (messages.isEmpty) return;

    messages.forEach((message) {
      message.channelIdField = event.newChannelId;
      message.workspaceId = event.newWorkspaceId;
    });
    messageBox.putMany(messages);
  }
}
