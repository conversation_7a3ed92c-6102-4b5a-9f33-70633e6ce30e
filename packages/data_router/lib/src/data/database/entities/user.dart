import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

// Import base model
import '../../../core/models/base_model.dart';
import 'profile.dart';
import 'user_presence.dart';
import 'user_status.dart';

part 'user.g.dart';

/// User entity for storing user information
/// UID range: 2001-2013
@Entity()
@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
  includeIfNull: false,
)
class User extends BaseModel {
  /// ObjectBox internal ID
  @Id(assignable: true)
  @JsonKey(includeFromJson: false, includeToJson: false)
  int id = 0;

  @Unique(onConflict: ConflictStrategy.replace)
  @Property(uid: 2016)
  @JsonKey(includeFromJson: false, includeToJson: false)
  @Index()
  String uniqueId = '';

  /// Session key reference (indexed, NEW FIELD)
  @Index()
  @Property(uid: 2013)
  @JsonKey(name: 'sessionKey')
  String sessionKey = '';

  /// Unique user ID (indexed)
  @Index()
  @Property(uid: 2001)
  @JsonKey(name: 'userId')
  String userId = '';

  /// Username
  @Property(uid: 2002)
  @JsonKey(name: 'username')
  String username = '';

  /// User creation time
  @Property(uid: 2003)
  @JsonKey(name: 'createTime')
  DateTime? createTime;

  /// User last update time
  @Property(uid: 2004)
  @JsonKey(name: 'updateTime')
  DateTime? updateTime;

  /// User type (integer enum)
  @Property(uid: 2005)
  @JsonKey(name: 'userType')
  int userType = 0;

  /// User connect link
  @Property(uid: 2006)
  @JsonKey(name: 'userConnectLink')
  String userConnectLink = '';

  /// Media permission setting
  @Property(uid: 2007)
  @JsonKey(name: 'mediaPermissionSetting')
  int mediaPermissionSetting = 0;

  /// Global notification status
  @Property(uid: 2009)
  @JsonKey(name: 'globalNotificationStatus')
  bool globalNotificationStatus = true;

  /// Single notification status
  @Property(uid: 2017)
  @JsonKey(name: 'singleNotificationStatus')
  bool singleNotificationStatus = true;

  /// Group notification status
  @Property(uid: 2018)
  @JsonKey(name: 'groupNotificationStatus')
  bool groupNotificationStatus = true;

  /// SIP credentials
  @Property(uid: 2010)
  @JsonKey(name: 'sipCredentials')
  String sipCredentials = '';

  /// SIP address
  @Property(uid: 2011)
  @JsonKey(name: 'sipAddress')
  String sipAddress = '';

  /// Whether this is a partial user record
  @Property(uid: 2012)
  @JsonKey(name: 'isPartial')
  bool isPartial = true;

  /// Composite index field: sessionUserId
  String get sessionUserId => '${sessionKey}_$userId';

  // RELATIONSHIPS

  /// ToOne relationship to Profile
  /// Links this user to its profile
  @JsonKey(includeFromJson: false, includeToJson: false)
  final profile = ToOne<Profile>();

  /// ToOne relationship to UserPresence
  /// Links this user to its current presence
  @JsonKey(includeFromJson: false, includeToJson: false)
  final presence = ToOne<UserPresence>();

  /// ToOne relationship to UserStatus
  /// Links this user to its current status
  @JsonKey(includeFromJson: false, includeToJson: false)
  final status = ToOne<UserStatus>();

  /// Raw JSON string representing friend data
  @Property(uid: 2014)
  @JsonKey(name: 'chatFriendDataRaw')
  String? chatFriendDataRaw;

  @Property(uid: 2015)
  @JsonKey(name: 'setting')
  String? setting;

  /// Alias name for this user (denormalized from UserPrivateData)
  @Property(uid: 2021)
  @JsonKey(name: 'aliasName')
  String? aliasName;

  /// Whether this user is blocked
  @Property(uid: 2019)
  @JsonKey(name: 'blocked')
  bool blocked = false;

  @Property(uid: 2020)
  @JsonKey(name: 'callToken')
  String? callToken;

  /// Default constructor
  User();

  /// Constructor with required fields
  User.create({
    required this.sessionKey,
    required this.userId,
    this.username = '',
    this.createTime,
    this.updateTime,
    this.userType = 0,
    this.userConnectLink = '',
    this.mediaPermissionSetting = 0,
    this.globalNotificationStatus = true,
    this.singleNotificationStatus = true,
    this.groupNotificationStatus = true,
    this.sipCredentials = '',
    this.sipAddress = '',
    this.isPartial = true,
    this.blocked = false,
    this.callToken = '',
  }) {
    this.uniqueId = '${sessionKey}_$userId';
    this.createTime ??= DateTime.now();
    this.updateTime ??= DateTime.now();
  }

  /// Update user information
  void updateInfo({
    String? username,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    bool? singleNotificationStatus,
    bool? groupNotificationStatus,
    String? sipCredentials,
    String? callToken,
    String? sipAddress,
    bool? blocked,
  }) {
    if (username != null) this.username = username;
    if (userType != null) this.userType = userType;
    if (userConnectLink != null) this.userConnectLink = userConnectLink;
    if (mediaPermissionSetting != null)
      this.mediaPermissionSetting = mediaPermissionSetting;
    if (globalNotificationStatus != null)
      this.globalNotificationStatus = globalNotificationStatus;
    if (singleNotificationStatus != null)
      this.singleNotificationStatus = singleNotificationStatus;
    if (groupNotificationStatus != null)
      this.groupNotificationStatus = groupNotificationStatus;
    if (sipCredentials != null) this.sipCredentials = sipCredentials;
    if (callToken != null) this.callToken = callToken;
    if (sipAddress != null) this.sipAddress = sipAddress;
    if (blocked != null) this.blocked = blocked;
    updateTime = DateTime.now();
  }

  bool needsUpdate(User other) => other.updateTime != updateTime;

  /// Mark as complete user record
  @override
  void markAsComplete() {
    isPartial = false;
    updateTime = DateTime.now();
  }

  /// Get the primary identifier for this user entity
  @override
  String getPrimaryId() => userId;

  /// Create a copy of this user with updated fields
  @override
  User copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? username,
    DateTime? createTime,
    DateTime? updateTime,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    bool? globalNotificationStatus,
    bool? singleNotificationStatus,
    bool? groupNotificationStatus,
    String? sipCredentials,
    String? sipAddress,
    bool? blocked,
    String? callToken,
    bool? isPartial,
    String? aliasName,
    UserStatus? status,
    UserPresence? presence,
    Profile? profile,
  }) {
    return User()
      ..uniqueId = this.uniqueId
      ..id = id ?? this.id
      ..sessionKey = sessionKey ?? this.sessionKey
      ..userId = userId ?? this.userId
      ..username = username ?? this.username
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..userType = userType ?? this.userType
      ..userConnectLink = userConnectLink ?? this.userConnectLink
      ..mediaPermissionSetting =
          mediaPermissionSetting ?? this.mediaPermissionSetting
      ..globalNotificationStatus =
          globalNotificationStatus ?? this.globalNotificationStatus
      ..singleNotificationStatus =
          singleNotificationStatus ?? this.singleNotificationStatus
      ..groupNotificationStatus =
          groupNotificationStatus ?? this.groupNotificationStatus
      ..sipCredentials = sipCredentials ?? this.sipCredentials
      ..sipAddress = sipAddress ?? this.sipAddress
      ..isPartial = isPartial ?? this.isPartial
      ..callToken = callToken ?? this.callToken
      ..aliasName = aliasName ?? this.aliasName
      ..blocked = blocked ?? this.blocked
      ..status.target = status ?? this.status.target
      ..presence.target = presence ?? this.presence.target
      ..profile.target = profile ?? this.profile.target;
  }

  // RELATIONSHIP HELPER METHODS

  /// Get current user status
  UserStatus? get currentStatus {
    return status.target;
  }

  /// Get current user presence
  UserPresence? get currentPresence {
    return presence.target;
  }

  /// Check if this user is blocked
  bool get isBlocked => blocked;

  /// Block or unblock this user
  void setBlocked(bool value) {
    blocked = value;
    updateTime = DateTime.now();
  }

  @override
  String toString() {
    return 'User{'
        'id: $id, '
        'sessionKey: $sessionKey, '
        'userId: $userId, '
        'username: $username, '
        'createTime: $createTime, '
        'updateTime: $updateTime, '
        'userType: $userType, '
        'userConnectLink: $userConnectLink, '
        'mediaPermissionSetting: $mediaPermissionSetting, '
        'globalNotificationStatus: $globalNotificationStatus, '
        'singleNotificationStatus: $singleNotificationStatus, '
        'groupNotificationStatus: $groupNotificationStatus, '
        'sipCredentials: $sipCredentials, '
        'callToken: $callToken, '
        'sipAddress: $sipAddress, '
        'isPartial: $isPartial'
        'blocked: $blocked'
        '}';
  }

  /// Create User from JSON map
  factory User.fromJson(Map<String, dynamic> json) {
    final user = _$UserFromJson(json);
    user.uniqueId = '${user.sessionKey}_${user.userId}';
    return user;
  }

  /// Convert User to JSON map
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String getName() {
    final pf = profile.target;

    // Use direct aliasName field first (denormalized data)
    if (aliasName != null && aliasName!.isNotEmpty) {
      return aliasName!;
    }

    if (pf != null && pf.displayName.isNotEmpty) {
      return pf.displayName;
    }

    return username;
  }
}
