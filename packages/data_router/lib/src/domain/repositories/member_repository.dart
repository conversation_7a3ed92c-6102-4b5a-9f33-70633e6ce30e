import '../../data/database/entities/member.dart';
import '../../data/models/resource.dart';

/// Repository interface for Member entity operations
/// Provides a clean abstraction for member-related data access
/// Follows the Repository pattern from Clean Architecture
abstract class MemberRepository {
  /// Watch members for a specific channel
  /// Returns a stream of member lists that updates in real-time
  Stream<List<Member>> watchMembersForChannel({
    required String workspaceId,
    required String channelId,
  });

  /// Watch a specific member
  /// Returns a stream that updates when the member changes
  Stream<Member?> watchMember({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  /// Get members for a specific channel
  /// Returns cached data if available, otherwise empty list
  Future<List<Member>> getMembersForChannel({
    required String workspaceId,
    required String channelId,
  });

  Future<List<Member>> getMembersWithRole({
    required String workspaceId,
    required String channelId,
  });

  List<Member> getAllMembers({
    required String workspaceId,
    required String channelId,
  });

  /// Get a specific member by IDs
  /// Returns null if member not found
  Future<Member?> getMember({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  /// Check if member exists locally
  Future<bool> memberExists({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  /// Update member nickname
  /// Performs optimistic update for better UX
  Future<Resource<bool>> updateMemberNickname({
    required String workspaceId,
    required String channelId,
    required String userId,
    required String nickname,
  });

  /// Assign member as admin
  /// Performs optimistic update for better UX
  Future<Resource<Member>> assignMemberAsAdmin({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  /// Leave channel
  /// Removes the current user from the channel
  Future<Resource<bool>> leaveChannel({
    required String workspaceId,
    required String channelId,
  });

  /// Load members for channel from remote
  /// Optionally force refresh from server
  Future<Resource<List<Member>>> loadMembersForChannel({
    required String workspaceId,
    required String channelId,
    bool forceRefresh = false,
  });

  /// Sync members with server
  /// Optionally filter by update time and workspace/channel
  Future<Resource<List<Member>>> syncMembers({
    String? updateTimeAfter,
    String? workspaceId,
    String? channelId,
  });

  /// Insert a single member
  Future<void> insertMember(Member member);

  /// Insert multiple members
  Future<void> insertMembers(List<Member> members);

  /// Delete a specific member
  Future<bool> deleteMember({
    required String workspaceId,
    required String channelId,
    required String userId,
  });

  /// Clear all members from local storage
  Future<void> clearAllMembers();

  /// Clear members for a specific channel
  Future<void> clearMembersForChannel({
    required String workspaceId,
    required String channelId,
  });

  /// Delete all members for a specific channel
  bool deleteMembersForChannel({
    required String workspaceId,
    required String channelId,
  });

  void deleteAllBySessionKey({required String sessionKey});
}
