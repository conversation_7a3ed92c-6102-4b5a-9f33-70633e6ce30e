//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_deletion_confirmation_payload.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_account_deletion_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateAccountDeletionFlowResponse {
  /// Returns a new [V3InitiateAccountDeletionFlowResponse] instance.
  V3InitiateAccountDeletionFlowResponse({
    this.ok,
    this.error,
    this.confirmationPayload,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'confirmationPayload',
    required: false,
    includeIfNull: false,
  )
  final V3DeletionConfirmationPayload? confirmationPayload;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateAccountDeletionFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.confirmationPayload == confirmationPayload;

  @override
  int get hashCode =>
      ok.hashCode + error.hashCode + confirmationPayload.hashCode;

  factory V3InitiateAccountDeletionFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateAccountDeletionFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateAccountDeletionFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
