import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:data_router/data_router.dart' as data;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../domain/usecase/message_request/accept_message_request_use_case.dart';
import '../../../domain/usecase/message_request/load_message_request_use_case.dart';
import '../../../domain/usecase/message_request/reject_message_request_use_case.dart';

part 'message_request_bloc.freezed.dart';
part 'message_request_event.dart';
part 'message_request_state.dart';

@LazySingleton()
class MessageRequestBloc
    extends BaseBloc<MessageRequestEvent, MessageRequestState> {
  MessageRequestBloc(
    this._loadMessageRequestUseCase,
    this._acceptMessageRequestUseCase,
    this._rejectMessageRequestUseCase,
    // data router
    this._channelDataOperation,
    this._sessionDataOperation,
  ) : super(MessageRequestState()) {
    on<InitMessageRequestEvent>(_onInit);
    on<LoadMessageRequestEvent>(_onLoadMessageRequest);
    on<LoadMoreMessageRequestEvent>(_onLoadMoreMessageRequest);
    on<UpdateListMessageRequestEvent>(_onUpdateListMessageRequest);
    on<AcceptMessageRequestEvent>(_onAcceptMessageRequest);
    on<RejectMessageRequestEvent>(_onRejectMessageRequest);
    on<UpdatedClosedWarningStatusEvent>(_onUpdatedClosedWarningStatus);
    on<UpdateTranslatedMessagesEventV2>(_onUpdateTranslatedMessages);
  }

  final data.ChannelDataOperation _channelDataOperation;
  final data.SessionDataOperation _sessionDataOperation;

  final LoadMessageRequestUseCase _loadMessageRequestUseCase;
  final AcceptMessageRequestUseCase _acceptMessageRequestUseCase;
  final RejectMessageRequestUseCase _rejectMessageRequestUseCase;

  StreamSubscription? _messageRequestSubscription;

  bool isFirstTrigger = true;
  bool _noMoreItems = false;
  static const _messageRequestRemotePageSize = 500;

  @override
  Future<void> close() {
    _messageRequestSubscription?.cancel();
    return super.close();
  }

  FutureOr<void> _onInit(
    InitMessageRequestEvent event,
    Emitter<MessageRequestState> emit,
  ) {
    isFirstTrigger = true;
    _noMoreItems = _sessionDataOperation.getLoadedAllMessageRequests() ?? false;

    emit(
      state.copyWith(
        closedWarning:
            _sessionDataOperation.getClosedMessageRequestWarning() ?? false,
        noMoreItems: _noMoreItems,
      ),
    );
    _messageRequestSubscription?.cancel();
    _messageRequestSubscription = _channelDataOperation
        .watchMessageRequests()
        .listen(_onMessageRequestChanged);
  }

  void _onMessageRequestChanged(List<data.Channel> channels) {
    if (isFirstTrigger) {
      _loadMessageRequest();
    }
    isFirstTrigger = false;
    add(UpdateListMessageRequestEvent(channels: channels));
  }

  FutureOr<void> _onUpdateListMessageRequest(
    UpdateListMessageRequestEvent event,
    Emitter<MessageRequestState> emit,
  ) {
    emit(state.copyWith(channels: event.channels, noMoreItems: _noMoreItems));
  }

  Future<void> _loadMessageRequest({String? nextPageToken}) async {
    final output = await _loadMessageRequestUseCase.execute(
      LoadMessageRequestInput(nextPageToken: nextPageToken),
    );
    if (output.hasNext && output.nextPageToken != null) {
      _loadMessageRequest(nextPageToken: output.nextPageToken);
    }
  }

  Future<void> _onAcceptMessageRequest(
    AcceptMessageRequestEvent event,
    Emitter<MessageRequestState> emit,
  ) async {
    try {
      await _acceptMessageRequestUseCase.execute(
        AcceptMessageRequestInput(
          channelId: event.channelId,
          workspaceId: event.workspaceId,
        ),
      );
    } catch (e) {
      Log.e(name: 'MessageRequestBloc._onAcceptMessageRequest', e);
    }
  }

  Future<void> _onRejectMessageRequest(
    RejectMessageRequestEvent event,
    Emitter<MessageRequestState> emit,
  ) async {
    try {
      await _rejectMessageRequestUseCase.execute(
        RejectMessageRequestInput(
          channelId: event.channelId,
          workspaceId: event.workspaceId,
        ),
      );
    } catch (e) {
      Log.e(name: 'MessageRequestBloc._onRejectMessageRequest', e);
    }
  }

  FutureOr<void> _onUpdatedClosedWarningStatus(
    UpdatedClosedWarningStatusEvent event,
    Emitter<MessageRequestState> emit,
  ) {
    _sessionDataOperation
        .updateClosedMessageRequestWarning(event.closedWarningStatus);
    emit(state.copyWith(closedWarning: event.closedWarningStatus));
  }

  FutureOr<void> _onLoadMessageRequest(
    LoadMessageRequestEvent event,
    Emitter<MessageRequestState> emit,
  ) {
    _loadMessageRequest();
  }

  Future<void> _onLoadMoreMessageRequest(
    LoadMoreMessageRequestEvent event,
    Emitter<MessageRequestState> emit,
  ) async {
    try {
      final output = await _loadMessageRequestUseCase.execute(
        LoadMessageRequestInput(
          limit: _messageRequestRemotePageSize,
          nextPageToken:
              event.nextPageToken.isEmpty ? null : event.nextPageToken,
        ),
      );

      _noMoreItems = !output.hasNext;
      if (_noMoreItems) {
        await _sessionDataOperation.updateLoadedAllMessageRequests(true);
      }

      if (output.channels.isEmpty) {
        _noMoreItems = true;
        emit(
          state.copyWith(
            channels: [],
            noMoreItems: true,
          ),
        );
      }
    } catch (e) {
      Log.e(name: 'MessageRequestBloc._onLoadMoreMessageRequest', e);
    }
  }

  FutureOr<void> _onUpdateTranslatedMessages(
    UpdateTranslatedMessagesEventV2 event,
    Emitter<MessageRequestState> emit,
  ) {
    emit(state.copyWith(translatedMessages: event.translatedMessages));
  }
}
