//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_user_display_name_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateUserDisplayNameRequest {
  /// Returns a new [V3UpdateUserDisplayNameRequest] instance.
  V3UpdateUserDisplayNameRequest({
    this.displayName,
  });

  @JsonKey(
    name: r'displayName',
    required: false,
    includeIfNull: false,
  )
  final String? displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateUserDisplayNameRequest &&
          other.displayName == displayName;

  @override
  int get hashCode => displayName.hashCode;

  factory V3UpdateUserDisplayNameRequest.fromJson(Map<String, dynamic> json) =>
      _$V3UpdateUserDisplayNameRequestFromJson(json);

  Map<String, dynamic> toJson() => _$V3UpdateUserDisplayNameRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
