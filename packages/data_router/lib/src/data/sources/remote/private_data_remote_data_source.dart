import 'package:injectable/injectable.dart';

import '../../../core/enums/entity_enum.dart';
import '../../../domain/services/user_service.dart';
import '../../database/entities/private_data.dart';
import 'base_remote_data_source.dart';

/// PrivateData Remote Data Source
/// Handles PrivateData entity remote API operations
///
/// Note: According to the architectural decision, PrivateData entities
/// should NOT implement remote data source operations.
/// This class provides stub implementations to satisfy the interface.
@LazySingleton()
class PrivateDataRemoteDataSource extends BaseRemoteDataSource<PrivateData> {
  PrivateDataRemoteDataSource(this._userService);

  final UserService _userService;

  @override
  EntityEnum get entity => EntityEnum.privateData;

  @override
  Future<PrivateData?> load(String id) async {
    // No remote operations for PrivateData entities
    // This method is not implemented as it's not actively used
    logOperation('PrivateData remote load not implemented', id);
    return null;
  }

  @override
  Future<List<PrivateData>> loadAll() async {
    try {
      logOperation('PrivateData remote loadAll not implemented');

      // Note: According to the architectural decision, PrivateData entities
      // should NOT implement remote data source operations.
      // All operations are local-only.

      logOperation(
        'PrivateData remote operations not supported',
        'Returning empty list',
      );
      return [];
    } catch (e) {
      logOperation('Error in PrivateData remote loadAll', e.toString());
      handleError('loadAll', e);
      return [];
    }
  }

  /// Get private data by session (not implemented)
  Future<List<PrivateData>> getPrivateDataBySession(String sessionKey) async {
    try {
      logOperation(
        'PrivateData remote getPrivateDataBySession not implemented',
        sessionKey,
      );

      // PrivateData entities don't support remote operations
      return [];
    } catch (e) {
      logOperation(
        'Error getting private data by session from remote',
        '$sessionKey: $e',
      );
      handleError('getPrivateDataBySession', e);
      return [];
    }
  }

  /// Get private data by user (not implemented)
  Future<PrivateData?> loadPrivateData() async {
    try {
      return _userService.loadPrivateData();
    } catch (e) {
      logOperation(
        'Error getting private data by user from remote',
        ': $e',
      );
      rethrow;
    }
  }

  /// Sync private data (not implemented)
  Future<List<PrivateData>> syncPrivateData() async {
    try {
      logOperation('PrivateData remote syncPrivateData not implemented');

      // PrivateData entities don't support remote operations
      return [];
    } catch (e) {
      logOperation('Error syncing private data from remote', e.toString());
      handleError('syncPrivateData', e);
      return [];
    }
  }

  /// Search private data (not implemented)
  Future<List<PrivateData>> searchPrivateData(String query) async {
    try {
      logOperation(
        'PrivateData remote searchPrivateData not implemented',
        query,
      );

      // PrivateData entities don't support remote operations
      return [];
    } catch (e) {
      logOperation('Error searching private data on remote', '$query: $e');
      handleError('searchPrivateData', e);
      return [];
    }
  }
}
