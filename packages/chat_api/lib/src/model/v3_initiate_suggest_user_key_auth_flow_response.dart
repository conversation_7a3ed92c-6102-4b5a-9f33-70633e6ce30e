//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:chat_api/src/model/v3_login_request_user_key.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_suggest_user_key_auth_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateSuggestUserKeyAuthFlowResponse {
  /// Returns a new [V3InitiateSuggestUserKeyAuthFlowResponse] instance.
  V3InitiateSuggestUserKeyAuthFlowResponse({
    this.ok,
    this.error,
    this.loginRequest,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'loginRequest',
    required: false,
    includeIfNull: false,
  )
  final V3LoginRequestUserKey? loginRequest;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateSuggestUserKeyAuthFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.loginRequest == loginRequest;

  @override
  int get hashCode => ok.hashCode + error.hashCode + loginRequest.hashCode;

  factory V3InitiateSuggestUserKeyAuthFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateSuggestUserKeyAuthFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateSuggestUserKeyAuthFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
