import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' as core;
import 'package:app_core/core.dart';
import 'package:auth/auth.dart' as auth;
import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import 'package:data_router/data_router.dart' as data;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/localization_client.dart';
import 'package:passkeys/authenticator.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../navigation/routes/app_router.gr.dart';
import '../../../utils/config_data_utils.dart';

@RoutePage()
class DeleteAccountPage extends StatefulWidget {
  const DeleteAccountPage({super.key});

  @override
  State<DeleteAccountPage> createState() => _DeleteAccountPageState();
}

class _DeleteAccountPageState
    extends BasePageState<DeleteAccountPage, DeleteAccountBloc>
    implements ui.DeleteAccountPageInterface {
  User? _user;
  late DeleteAccountBloc _deleteAccountBloc;

  void resetState() {
    _deleteAccountBloc.add(ResetDeleteAccountStateEvent());
  }

  @override
  void initState() {
    _deleteAccountBloc = getIt<DeleteAccountBloc>();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocProvider<DeleteAccountBloc>(
      create: (context) => _deleteAccountBloc,
      child: BlocListener<DeleteAccountBloc, DeleteAccountState>(
        listener: (context, state) {
          state.maybeWhen(
            deleteAccountSuccess: _onDeleteSuccess,
            deleteAccountFailure: _onError,
            orElse: () {},
          );
        },
        child: BlocBuilder<DeleteAccountBloc, DeleteAccountState>(
          buildWhen: (prev, current) => current != prev,
          builder: (context, state) {
            return ui.DeleteAccountPage(
              interface: this,
            );
          },
        ),
      ),
    );
  }

  @override
  void onClickBack() {
    context.router.maybePop();
  }

  @override
  void onClickCancel() {
    context.router.maybePop();
  }

  @override
  void onClickDelete(String username, String feedback) async {
    bool noConnect = getIt<NetworkManager>().noConnection();

    if (noConnect) {
      return ui.DialogUtils.showLostConnectionShareToDialog(
        context,
        barrierDismissible: false,
        onOkClicked: (BuildContext dialogContext) {
          Navigator.pop(context);
        },
      );
    }

    if (username != _user!.username) return;
    final passkeyMigrated =
        getIt<data.SessionDataOperation>().getActiveSession()!.passkeyMigrated;
    if (!(await canAuthenticate()) ||
        !(await canAuthenticateWithWebAPI(passkeyMigrated))) {
      await _showAuthenticateNoSupportDialog();
      return;
    }

    FocusScope.of(context).requestFocus(FocusNode());
    _deleteAccountBloc.add(DeleteAccountRequestedEvent(username));
  }

  Future<void> _showAuthenticateNoSupportDialog() async {
    ui.DialogUtils.showAuthenticationNotSupportDialog(
      context,
      onOpenSettings: () {
        PasskeyAuthenticator().goToSettings();
      },
      onCancel: () {
        context.router.maybePop();
      },
      isIos: Platform.isIOS,
      hasFaceId: await hasFaceId(),
    );
  }

  @override
  String username() {
    if (_user != null) {
      return _user!.username!;
    }

    final output = GetIt.I.get<GetMeUseCase>().execute(GetMeInput());

    _user = output.user;

    return _user!.username!;
  }

  @override
  String errorRestrictedUsernames() {
    return '';
  }

  void _onError() {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showFloatingSnackBar(
          snackBarType: ui.SnackBarType.danger,
          context: context,
          content: GetIt.I<AppLocalizations>().anErrorOccurredPleaseTryAgain,
        );
      },
    );

    resetState();
  }

  void _onDeleteSuccess() async {
    resetState();
    getIt<core.WebSocketManager>().disconnect();

    unawaited(core.IsolateTaskService().clean());

    ConfigDataUtils.clearConfigData();

    GetIt.I<auth.DeleteSessionUseCase>().execute(
      auth.DeleteAccountSelectionInput(
        core.Config.getInstance().activeSessionKey ?? '',
      ),
    );

    await context.router.replaceAll(
      [WelcomeLastRoute(invalidToken: false), AccountDeletedRoute()],
      updateExistingRoutes: false,
    );
    AppEventBus.publish(DeleteUserAccountEvent(id: UniqueKey().toString()));
  }
}
