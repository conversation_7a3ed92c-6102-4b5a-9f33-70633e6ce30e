//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_confirm_account_deletion_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ConfirmAccountDeletionResponse {
  /// Returns a new [V3ConfirmAccountDeletionResponse] instance.
  V3ConfirmAccountDeletionResponse({
    this.ok,
    this.error,
    this.message,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  /// Hasta la vista, baby.
  @J<PERSON><PERSON><PERSON>(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final String? message;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ConfirmAccountDeletionResponse &&
          other.ok == ok &&
          other.error == error &&
          other.message == message;

  @override
  int get hashCode => ok.hashCode + error.hashCode + message.hashCode;

  factory V3ConfirmAccountDeletionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ConfirmAccountDeletionResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ConfirmAccountDeletionResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
