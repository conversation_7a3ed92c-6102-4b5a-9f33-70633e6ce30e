import 'dart:async';
import 'dart:convert';

import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../../core.dart';
import '../../resilient_isolate.dart';
import 'shared_preferences_objectbox_store.dart';
import 'package:shared/shared.dart';

class SharedPreferencesStore {
  static const bool _enableLog = false;
  static const String _taskPrefix = 'ri_task_';
  static const String _resultPrefix = 'ri_result_';
  static const String _tokenKey = 'ri_token';
  static const String _metadataKey = 'ri_metadata';

  static void _log(String method, String message, {bool isError = false}) {
    if (kDebugMode && _enableLog) {
      final logPrefix = isError ? '[ERROR]' : '[INFO]';
      Log.d('$logPrefix SharedPreferencesStore.$method: $message');
    }
  }

  static String _taskKey(String taskId) => '$_taskPrefix$taskId';

  static String _resultKey(String resultId) => '$_resultPrefix$resultId';

  static final _key = Key.fromBase16(
    '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2bfbf7419d1714eaeeecf43d',
  );
  static final _iv = IV.fromBase16('a3bf4f1b2bfbf7419d1714eaeeecf43d');
  static final _encrypter = Encrypter(AES(_key));

  static String? _cachedToken;
  static WorkerMetadata? _cachedMetadata;

  static Future<bool> saveTask(TaskModel task) {
    return SharedPreferencesObjectBoxStore.saveTask(
      task,
      shouldDeleteResult: true,
    );
  }

  static Future<bool> taskExists(String taskId) {
    return SharedPreferencesObjectBoxStore.taskExists(taskId);
  }

  static Future<TaskModel?> loadTask(String taskId) {
    return SharedPreferencesObjectBoxStore.loadTask(taskId);
  }

  static Future<List<TaskModel>> loadAllTasks() {
    return SharedPreferencesObjectBoxStore.loadAllTasks();
  }

  static Future<bool> updateTask(TaskModel task) {
    return SharedPreferencesObjectBoxStore.updateTask(task);
  }

  static Future<bool> updateTaskStatus(String taskId, TaskStatus status) {
    return SharedPreferencesObjectBoxStore.updateTaskStatus(taskId, status);
  }

  static Future<bool> isUnrecoverable(String taskId) {
    return SharedPreferencesObjectBoxStore.isUnrecoverable(taskId);
  }

  static Future<bool> isSent(String taskId) {
    return SharedPreferencesObjectBoxStore.isSent(taskId);
  }

  static Future<bool> deleteTask(String taskId) {
    return SharedPreferencesObjectBoxStore.deleteTask(taskId);
  }

  static Future<bool> clearAllTasks() {
    return SharedPreferencesObjectBoxStore.clearAllTasks();
  }

  static Future<bool> saveTaskResult(String resultId, String resultJson) {
    return SharedPreferencesObjectBoxStore.saveTaskResult(resultId, resultJson);
  }

  static Future<String?> loadTaskResult(String resultId) {
    return SharedPreferencesObjectBoxStore.loadTaskResult(resultId);
  }

  static Future<bool> deleteTaskResult(String resultId) {
    return SharedPreferencesObjectBoxStore.deleteTaskResult(resultId);
  }

  static Future<bool> clearAllMessageResults() {
    return SharedPreferencesObjectBoxStore.clearAllMessageResults();
  }

  static Future<bool> deleteAllMessageResults() {
    return SharedPreferencesObjectBoxStore.deleteAllMessageResults();
  }

  static Future<ExecutorType> getExecutorType() {
    return SharedPreferencesObjectBoxStore.getExecutorType();
  }

  static Future<bool> setExecutorType(ExecutorType executorType) {
    return SharedPreferencesObjectBoxStore.setExecutorType(executorType);
  }

  static Future<bool> encryptToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    try {
      final encrypted = _encrypter.encrypt(token, iv: _iv);
      final result = await prefs.setString(_tokenKey, encrypted.base64);
      _cachedToken = token;
      _log('encryptToken', 'Token encrypted and saved');
      return result;
    } catch (e) {
      _log('encryptToken', 'Error encrypting token: $e', isError: true);
      return false;
    }
  }

  static Future<String?> getDecryptToken() async {
    if (_cachedToken != null) return _cachedToken;
    final prefs = await SharedPreferences.getInstance();
    try {
      final encryptedBase64 = prefs.getString(_tokenKey);
      if (encryptedBase64 == null) return null;
      final decrypted = _encrypter.decrypt64(encryptedBase64, iv: _iv);
      _cachedToken = decrypted;
      _log('getDecryptToken', 'Token decrypted and returned');
      return decrypted;
    } catch (e) {
      _log('getDecryptToken', 'Error decrypting token: $e', isError: true);
      return null;
    }
  }

  static Future<bool> encryptMetadata(WorkerMetadata metadata) async {
    final prefs = await SharedPreferences.getInstance();
    try {
      final jsonStr = jsonEncode(metadata.toJson());
      final encrypted = _encrypter.encrypt(jsonStr, iv: _iv);
      final result = await prefs.setString(_metadataKey, encrypted.base64);
      _cachedMetadata = metadata;
      _log('encryptMetadata', 'Metadata encrypted and saved');
      return result;
    } catch (e) {
      _log('encryptMetadata', 'Error encrypting metadata: $e', isError: true);
      return false;
    }
  }

  static Future<WorkerMetadata?> getDecryptMetadata() async {
    if (_cachedMetadata != null) return _cachedMetadata;
    final prefs = await SharedPreferences.getInstance();
    try {
      final encryptedBase64 = prefs.getString(_metadataKey);
      if (encryptedBase64 == null) return null;
      final decryptedJson = _encrypter.decrypt64(encryptedBase64, iv: _iv);
      final map = jsonDecode(decryptedJson) as Map<String, dynamic>;
      _cachedMetadata = WorkerMetadata.fromJson(map);
      _log('getDecryptMetadata', 'Metadata decrypted and returned');
      return _cachedMetadata;
    } catch (e) {
      _log(
        'getDecryptMetadata',
        'Error decrypting metadata: $e',
        isError: true,
      );
      return null;
    }
  }

  static Future<void> ensureInitialized() {
    return SharedPreferencesObjectBoxStore.ensureInitialized();
  }

  static Future<void> dispose() {
    return SharedPreferencesObjectBoxStore.dispose();
  }
}
