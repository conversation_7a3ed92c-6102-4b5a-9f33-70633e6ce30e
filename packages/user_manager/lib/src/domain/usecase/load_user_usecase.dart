import 'package:data_router/data_router.dart' as d;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@Injectable()
class LoadUserUseCase extends BaseFutureUseCase<LoadUserInput, LoadUserOutput> {
  const LoadUserUseCase(this._userDataOperation);

  final d.UserDataOperation _userDataOperation;

  @protected
  @override
  Future<LoadUserOutput> buildUseCase(
    LoadUserInput input,
  ) async {
    try {
      final result = await _userDataOperation.loadUser(input.userId);
      if (result.data != null) {
        final user = result.data;
        return LoadUserOutput(user: user);
      }
      return LoadUserOutput(user: null);
    } on Exception catch (_) {
      return LoadUserOutput(user: null);
    }
  }
}

class LoadUserInput extends BaseInput {
  final String userId;
  const LoadUserInput({
    required this.userId,
  });
}

class LoadUserOutput extends BaseOutput {
  final d.User? user;

  const LoadUserOutput({
    this.user,
  });
}
