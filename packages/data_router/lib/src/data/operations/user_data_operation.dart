import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data_router.dart';
import '../models/deleted_user.dart';
import '../serializers/user_presence_serializer.dart';
import '../sources/local/user_local_data_source.dart';
import '../sources/remote/user_remote_data_source.dart';
import '../sources/ws/user_websocket_data_source.dart';

/// User-specific data operation implementation
/// Provides reactive data access with automatic synchronization for User entities
///
/// This follows the Single Responsibility Principle by handling only User entities
/// and implements the local-first strategy with entity-specific optimizations
@Singleton()
class UserDataOperation extends BaseDataOperation<User> {
  final UserLocalDataSource localSource;
  final UserRemoteDataSource remoteSource;
  final UserWebSocketDataSource webSocketSource;

  UserDataOperation(
    this.localSource,
    this.remoteSource,
    this.webSocketSource,
  ) : super(
          localSource: localSource,
          remoteSource: remoteSource,
          webSocketSource: webSocketSource,
        );

  //region WebSocket Event Handling

  @override
  Future<void> handleRealtimeEvent(DataRouterCloudEvent event) async {
    try {
      // Handle each event type with specific logic
      switch (event.type) {
        // User Avatar events
        case EventType.AVATAR_UPDATED:
          await _handleAvatarUpdatedEvent(event);
          break;
        case EventType.AVATAR_DELETED:
          await _handleAvatarDeletedEvent(event);
          break;

        // User Status events
        case EventType.USER_STATUS_CREATED || EventType.USER_STATUS_UPDATED:
          await _handleUserStatusUpdatedEvent(event);
          break;
        case EventType.USER_STATUS_DELETED:
          await _handleUserStatusDeletedEvent(event);
          break;
        // User Profile events
        case EventType.DISPLAY_NAME_UPDATED:
          await _handleDisplayNameUpdatedEvent(event);
          break;

        // User Cover events
        case EventType.USER_COVER_CREATED:
          await _handleUserCoverCreatedEvent(event);
          break;
        case EventType.USER_COVER_UPDATED:
          await _handleUserCoverUpdatedEvent(event);
          break;
        case EventType.USER_COVER_DELETED:
          await _handleUserCoverDeletedEvent(event);
          break;

        // User Action events
        case EventType.DELETED_USER:
        case EventType.BLOCK_USER:
        case EventType.UNBLOCK_USER:
          await _handleUserActionEvent(event);
          break;

        default:
          logOperation('Unhandled User event type', event.type.toString());
          break;
      }
    } catch (error) {
      logOperation(
        'Error handling realtime event',
        '${event.type} - ${event.id}: $error',
      );
    }
  }

  /// Handle avatar updated event
  Future<void> _handleAvatarUpdatedEvent(DataRouterCloudEvent event) async {
    final avatarData = event.parse<UserAvatarUpdatedEventData>();
    if (avatarData?.userId == null) {
      logOperation(
        'Invalid avatar update event - missing userId',
        event.id,
      );
      return;
    }

    final userId = avatarData!.userId!;

    // Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for avatar update',
        userId,
      );
      return;
    }

    // Get or create profile
    Profile profile = currentUser.profile.target ??
        Profile.create(
          sessionKey: currentUser.sessionKey,
          userId: userId,
        );

    // Update Profile with new avatar data
    final updatedProfile = profile.copyWith(
      avatar: avatarData.avatar ?? profile.avatar,
      originalAvatar: avatarData.videoAvatar ?? profile.originalAvatar,
    );

    // Save updated profile first (required for ObjectBox relationship)
    await _saveProfile(updatedProfile);

    // Create updated user with new profile relationship
    final updatedUser = currentUser.copyWith(
      updateTime: DateTime.now(),
    );
    updatedUser.profile.target = updatedProfile;

    await localSource.insert(updatedUser);

    logOperation(
      'User avatar updated from WebSocket event',
      userId,
    );
  }

  /// Handle user status updated event
  Future<void> _handleUserStatusUpdatedEvent(DataRouterCloudEvent event) async {
    final statusData = event.parse<UserStatusUpdatedEventData>();

    if (statusData?.userId == null) {
      logOperation(
        'Invalid status update event - missing userId',
        event.id,
      );
      return;
    }

    final userId = statusData!.userId!;

    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for status update',
        userId,
      );
      return;
    }

    await _updateUserStatus(
      userId: userId,
      sessionKey: currentUser.sessionKey,
      statusContent: statusData.statusContent,
      statusEmoji: statusData.statusEmoji,
      endTime: statusData.endTime,
    );

    logOperation(
      'User status updated from WebSocket event',
      userId,
    );
  }

  Future<void> _handleUserStatusDeletedEvent(DataRouterCloudEvent event) async {
    final statusData = event.parse<UserStatusDeletedEventData>();
    if (statusData?.userId == null) {
      logOperation(
        'Invalid status update event - missing userId',
        event.id,
      );
      return;
    }

    final userId = statusData!.userId!;

    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for status update',
        userId,
      );
      return;
    }

    await _updateUserStatus(
      userId: userId,
      sessionKey: currentUser.sessionKey,
      statusContent: null,
      statusEmoji: null,
    );

    logOperation(
      'User status deleted from WebSocket event',
      userId,
    );
  }

  /// Handle display name updated event
  Future<void> _handleDisplayNameUpdatedEvent(
    DataRouterCloudEvent event,
  ) async {
    final displayNameData = event.parse<UserDisplayNameUpdatedEventData>();
    if (displayNameData?.userId == null ||
        displayNameData?.displayName == null) {
      logOperation(
        'Invalid display name update event - missing data',
        event.id,
      );
      return;
    }

    final userId = displayNameData!.userId!;
    final newDisplayName = displayNameData.displayName!;

    // Use existing method to update profile display name
    await _updateUserProfileDisplayName(userId, newDisplayName);

    logOperation(
      'User display name updated from WebSocket event',
      'userId: $userId, newDisplayName: $newDisplayName',
    );
  }

  /// Handle avatar deleted event
  Future<void> _handleAvatarDeletedEvent(DataRouterCloudEvent event) async {
    final avatarData = event.parse<UserAvatarDeletedEventData>();
    if (avatarData?.userId == null) {
      logOperation(
        'Invalid avatar deleted event - missing userId',
        event.id,
      );
      return;
    }

    final userId = avatarData!.userId!;

    // Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for avatar deletion',
        userId,
      );
      return;
    }

    // Get or create profile
    Profile profile = currentUser.profile.target ??
        Profile.create(
          sessionKey: currentUser.sessionKey,
          userId: userId,
        );

    // Clear avatar data from Profile
    final updatedProfile = profile.copyWith(
      avatar: '',
      originalAvatar: '',
    );

    // Save updated profile first (required for ObjectBox relationship)
    await _saveProfile(updatedProfile);

    // Create updated user with new profile relationship
    final updatedUser = currentUser.copyWith(
      updateTime: DateTime.now(),
      isPartial: true,
    );
    updatedUser.profile.target = updatedProfile;

    await localSource.insert(updatedUser);

    logOperation(
      'User avatar deleted from WebSocket event',
      userId,
    );
  }

  /// Handle user action events (delete, block, unblock)
  Future<void> _handleUserActionEvent(DataRouterCloudEvent event) async {
    // For action events, we might need to handle differently based on event type
    switch (event.type) {
      case EventType.DELETED_USER:
        await _handleUserDeletedEvent(event);
        break;
      case EventType.BLOCK_USER:
        await _handleUserBlockedEvent(event);
        break;
      case EventType.UNBLOCK_USER:
        await _handleUserUnblockedEvent(event);
        break;
      default:
        logOperation('Unhandled user action event', event.type.toString());
        break;
    }
  }

  /// Update user status through UserStatus entity
  Future<void> _updateUserStatus({
    required String userId,
    required String sessionKey,
    String? statusContent,
    String? statusEmoji,
    String? endTime,
  }) async {
    try {
      logOperation(
        'Updating user status from WebSocket event',
        'userId: $userId, content: $statusContent, emoji: $statusEmoji',
      );

      if (statusEmoji == null && statusContent == null) {
        await localSource.deleteUserStatus(userId);
        logOperation('✅ User status deleted', 'userId: $userId');
      } else {
        final user = await localSource.get(userId);
        final existingStatus = user?.status.target;

        UserStatus updatedStatus;
        if (existingStatus == null) {
          // No status exists, create new
          updatedStatus = UserStatus.create(
            userId: userId,
            sessionKey: sessionKey,
            content: statusContent ?? '',
            status: statusEmoji ?? '',
            endTime: endTime,
          );
        } else {
          // Update existing status with new data
          updatedStatus = existingStatus.copyWith(
            content: statusContent ?? '',
            status: statusEmoji ?? '',
            endTime: endTime,
          );
          await localSource.updateUserStatus(userId, updatedStatus);
        }
        if (user != null) {
          user.status.target = updatedStatus;
          await localSource.insert(user);
        }

        logOperation('✅ User status update processed', 'userId: $userId');
      }
    } catch (e) {
      logOperation(
        '❌ Error updating user status',
        'userId: $userId, error: $e',
      );
    }
  }

  /// Handle user deleted event
  Future<void> _handleUserDeletedEvent(DataRouterCloudEvent event) async {
    try {
      if (!ValidTokenInterceptor.isTokenInvalidProcessed(
        Config.getInstance().apiAuthToken,
      )) {
        AppEventBus.publish(OnTokenInvalid());
      }
    } catch (e) {
      logOperation(
        '❌ Error handling user deleted event',
        'event: ${event.id}, error: $e',
      );
    }
  }

  /// Handle user blocked event
  Future<void> _handleUserBlockedEvent(DataRouterCloudEvent event) async {
    try {
      final blockedData = event.parse<UserBlockedEventData>();
      if (blockedData?.blockerUserId == null ||
          blockedData?.blockedUserId == null) {
        logOperation(
          'Invalid user blocked event - missing user IDs',
          event.id,
        );
        return;
      }

      final blockerUserId = blockedData!.blockerUserId!;
      final blockedUserId = blockedData.blockedUserId!;
      final currentUserId = Config.getInstance().activeSessionKey ?? '';

      // ✅ Chỉ xử lý nếu tôi là người chặn
      if (blockerUserId == currentUserId) {
        final userBlocked = await remoteSource.load(blockedUserId);
        if (userBlocked != null) {
          userBlocked.blocked = true;

          await localSource.insert(userBlocked);
          AppEventBus.publish(
            BlockEvent(
              user: userBlocked.toJson(),
              actorUserId: blockerUserId,
            ),
          );
          logOperation(
            '✅ Updated blocked user (I blocked them)',
            blockedUserId,
          );
        }
      } else {
        final userBlocker = await remoteSource.load(blockerUserId);
        if (userBlocker != null) {
          userBlocker.blocked = true;

          await localSource.insert(userBlocker);
          AppEventBus.publish(BlockEvent(user: userBlocker.toJson()));
          logOperation(
            '✅ Updated blocked user (I blocked them)',
            blockedUserId,
          );
        }
      }

      logOperation('✅ User blocked event processed', event.id);
    } catch (e, stackTrace) {
      logOperation(
        '❌ Error handling user blocked event',
        'event: ${event.id}, error: $e\n$stackTrace',
      );
    }
  }

  /// Handle user unblocked event
  Future<void> _handleUserUnblockedEvent(DataRouterCloudEvent event) async {
    try {
      final unblockedData = event.parse<UserUnblockedEventData>();
      if (unblockedData?.unblockerUserId == null ||
          unblockedData?.unblockedUserId == null) {
        logOperation(
          'Invalid user unblocked event - missing user IDs',
          event.id,
        );
        return;
      }

      final unblockerUserId = unblockedData!.unblockerUserId!;
      final unblockedUserId = unblockedData.unblockedUserId!;
      final currentUserId = Config.getInstance().activeSessionKey ?? '';

      logOperation(
        'Processing user unblocked event',
        'unblocker: $unblockerUserId, unblocked: $unblockedUserId, current: $currentUserId',
      );

      // ✅ Chỉ xử lý nếu tôi là người chủ động gỡ block
      if (unblockerUserId == currentUserId) {
        final userUnblocked = await remoteSource.load(unblockedUserId);
        if (userUnblocked != null) {
          userUnblocked.blocked = false;
          await localSource.insert(userUnblocked);
          AppEventBus.publish(UnBlockEvent(
            userId: userUnblocked.userId,
            actorUserId: unblockerUserId,
          ));
          logOperation(
            '✅ Updated unblocked user (I unblocked them)',
            unblockedUserId,
          );
        }
      } else {
        final userUnblocker = await remoteSource.load(unblockerUserId);

        if (userUnblocker != null) {
          userUnblocker.blocked = false;
          await localSource.insert(userUnblocker);
          AppEventBus.publish(UnBlockEvent(userId: unblockerUserId));
          logOperation(
            '✅ Updated unblocked user (I unblocked them)',
            unblockedUserId,
          );
        }
      }

      logOperation('✅ User unblocked event processed', event.id);
    } catch (e, stackTrace) {
      logOperation(
        '❌ Error handling user unblocked event',
        'event: ${event.id}, error: $e\n$stackTrace',
      );
    }
  }

  /// Handle user cover created event
  Future<void> _handleUserCoverCreatedEvent(DataRouterCloudEvent event) async {
    final coverData = event.parse<UserCoverCreatedEventData>();
    if (coverData?.userId == null || coverData?.coverUrl == null) {
      logOperation(
        'Invalid cover created event - missing data',
        event.id,
      );
      return;
    }

    final userId = coverData!.userId!;
    final coverUrl = coverData.coverUrl!;

    // Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for cover creation',
        userId,
      );
      return;
    }

    // Get or create profile
    Profile profile = currentUser.profile.target ??
        Profile.create(
          sessionKey: currentUser.sessionKey,
          userId: userId,
        );

    // Update Profile with new cover data
    final updatedProfile = profile.copyWith(
      cover: coverUrl,
    );

    // Save updated profile first (required for ObjectBox relationship)
    await _saveProfile(updatedProfile);

    // Create updated user with new profile relationship
    final updatedUser = currentUser.copyWith(
      updateTime: DateTime.now(),
    );
    updatedUser.profile.target = updatedProfile;

    await localSource.insert(updatedUser);

    logOperation(
      'User cover created from WebSocket event',
      'userId: $userId, coverUrl: $coverUrl',
    );
  }

  /// Handle user cover updated event
  Future<void> _handleUserCoverUpdatedEvent(DataRouterCloudEvent event) async {
    final coverData = event.parse<UserCoverUpdatedEventData>();
    if (coverData?.userId == null || coverData?.coverUrl == null) {
      logOperation(
        'Invalid cover updated event - missing data',
        event.id,
      );
      return;
    }

    final userId = coverData!.userId!;
    final coverUrl = coverData.coverUrl!;

    // Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for cover update',
        userId,
      );
      return;
    }

    // Get or create profile
    Profile profile = currentUser.profile.target ??
        Profile.create(
          sessionKey: currentUser.sessionKey,
          userId: userId,
        );

    // Update Profile with new cover data
    final updatedProfile = profile.copyWith(
      cover: coverUrl,
    );

    // Save updated profile first (required for ObjectBox relationship)
    await _saveProfile(updatedProfile);

    // Create updated user with new profile relationship
    final updatedUser = currentUser.copyWith(
      updateTime: DateTime.now(),
    );
    updatedUser.profile.target = updatedProfile;

    await localSource.insert(updatedUser);

    logOperation(
      'User cover updated from WebSocket event',
      'userId: $userId, coverUrl: $coverUrl',
    );
  }

  /// Handle user cover deleted event
  Future<void> _handleUserCoverDeletedEvent(DataRouterCloudEvent event) async {
    final coverData = event.parse<UserCoverDeletedEventData>();
    if (coverData?.userId == null) {
      logOperation(
        'Invalid cover deleted event - missing userId',
        event.id,
      );
      return;
    }

    final userId = coverData!.userId!;

    // Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation(
        'User not found for cover deletion',
        userId,
      );
      return;
    }

    // Get or create profile
    Profile profile = currentUser.profile.target ??
        Profile.create(
          sessionKey: currentUser.sessionKey,
          userId: userId,
        );

    // Clear cover photo from Profile
    final updatedProfile = profile.copyWith(
      cover: '',
    );

    // Save updated profile first (required for ObjectBox relationship)
    await _saveProfile(updatedProfile);

    // Create updated user with new profile relationship
    final updatedUser = currentUser.copyWith(
      updateTime: DateTime.now(),
    );
    updatedUser.profile.target = updatedProfile;

    await localSource.insert(updatedUser);

    logOperation(
      'User cover deleted from WebSocket event',
      userId,
    );
  }

  /// Update user's profile display name from WebSocket event
  /// This is similar to updateUserDisplayName but without API call since it's from server event
  Future<void> _updateUserProfileDisplayName(
    String userId,
    String newDisplayName,
  ) async {
    try {
      logOperation(
        'Updating user profile display name from WebSocket event',
        'userId: $userId, newDisplayName: $newDisplayName',
      );

      // Get current user from local database
      final currentUser = await localSource.get(userId);
      if (currentUser == null) {
        logOperation(
          'User not found in local database for display name update',
          userId,
        );
        return;
      }

      // Get or create profile
      Profile profile = currentUser.profile.target ??
          Profile.create(
            sessionKey: currentUser.sessionKey,
            userId: userId,
          );

      // Update Profile.displayName
      final updatedProfile = profile.copyWith(
        displayName: newDisplayName,
      );

      // Save updated profile first (required for ObjectBox relationship)
      await _saveProfile(updatedProfile);

      // Create updated user with new profile relationship
      final updatedUser = currentUser.copyWith(
        updateTime: DateTime.now(),
        isPartial: true,
      );
      updatedUser.profile.target = updatedProfile;
      await localSource.insert(updatedUser);

      logOperation(
        '✅ Profile display name updated from WebSocket event',
        'userId: $userId, newDisplayName: $newDisplayName',
      );
    } catch (e) {
      logOperation(
        '❌ Error updating profile display name from WebSocket event',
        'userId: $userId, error: $e',
      );
    }
  }

  /// Delete all users by session key
  /// This is a User-specific operation for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting users by session key', sessionKey);
      await localSource.deleteAllBySessionKey(sessionKey);
    } catch (e) {
      logOperation(
        'Error deleting users by session key',
        '$sessionKey: $e',
      );
    }
  }

  //endregion

  //region Local Data Source Operations

  /// Get user from local database
  User? getLocalUser(String userId) {
    return localSource.getSync(userId);
  }

  Future<void> insertUser(User user) async {
    await localSource.insert(user);
  }

  @override
  Stream<User?> watchMe() {
    logOperation('Watching me profile');
    unawaited(_loadAndSaveMe());

    return localSource.watchMe();
  }

  List<User> getManyUser(List<String> userIds) {
    final users = localSource.getManyUserByUserId(userIds);

    return users;
  }

  Future<void> _loadAndSaveMe() async {
    final me = await remoteSource.loadMe();

    if (me == null) {
      return;
    }

    // UserSerializer.fromV3Me() now automatically creates and attaches profile
    localSource.insert(me);
  }

  User? getMe() {
    final me = localSource.getMe();
    if (me == null) {
      logOperation('Me is null in local source');
    }
    return me;
  }

  Future<Resource<User?>> loadAndSaveMe() async {
    try {
      final meLocal = localSource.getMe();

      if (meLocal != null) {
        return Resource.success(meLocal);
      }

      final me = await remoteSource.loadMe();

      if (me == null) {
        return Resource.error('Me is null');
      }
      // UserSerializer.fromV3Me() now automatically creates and attaches profile
      localSource.insert(me);
      return Resource.success(me);
    } catch (e) {
      // Log the error or handle it appropriately
      return Resource.error('Failed to load and save user', e);
    }
  }

  Future<Resource<User?>> loadMe() async {
    try {
      final me = await remoteSource.loadMe();

      if (me == null) {
        return Resource.error('Me is null');
      }
      return Resource.success(me);
    } catch (e) {
      // Log the error or handle it appropriately
      return Resource.error('Failed to load and save user', e);
    }
  }

  Future<Resource<User?>> getMeMigration(String token) async {
    try {
      final me = await remoteSource.loadMe(
        headers: {
          'x-session-token': token,
        },
      );

      if (me == null) {
        return Resource.empty();
      }
      localSource.insert(me);
      return Resource.success(me);
    } catch (e) {
      logOperation('Error loading me', e.toString());
      return Resource.error('Error loading me: $e', e);
    }
  }

  Future<Resource<User?>> loadUser(String id) async {
    try {
      // Step 1: Get from local
      logOperation('Getting user from local', id);

      try {
        final remoteUser = await remoteSource.load(id);
        if (remoteUser != null) {
          logOperation('User loaded from remote', id);
          await localSource.insert(remoteUser);

          return Resource.success(remoteUser);
        } else {
          return Resource.success(null);
        }
      } catch (apiError) {
        logOperation(
          'Error fetching user from remote',
          '$id: ${apiError}',
        );
        return Resource.success(null);
      }
    } catch (e) {
      logOperation('Unexpected error in loadUser', '$id: ${e.toString()}');
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Stream<User?> watchUser(String id) {
    return localSource.watch(id);
  }

  Future<Resource<User>> getUser(String userId) async {
    try {
      final localUser = await localSource.get(userId);

      if (localUser != null) {
        return Resource.success(localUser);
      }

      final remoteUser = await remoteSource.load(userId);
      if (remoteUser != null) {
        await localSource.insert(remoteUser);

        return Resource.success(remoteUser);
      }
      return Resource.error('Unexpected error: remote user is null');
    } catch (e) {
      logOperation('Unexpected error in loadUser', '$userId: ${e.toString()}');
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Future<Resource<User>> getUserByUserName(String username) async {
    try {
      final remoteUser = await remoteSource.getUserByUserName(username);
      if (remoteUser != null) {
        await localSource.insert(remoteUser);

        return Resource.success(remoteUser);
      }
      return Resource.error('Unexpected error: remote user is null');
    } catch (e) {
      logOperation(
        'Unexpected error in loadUser',
        '$username: ${e.toString()}',
      );
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Update user display name with optimistic update pattern
  ///
  /// IMPORTANT: This updates Profile.displayName only, NOT User.username
  /// - User.username is the unique identifier/handle (like @john_doe)
  /// - Profile.displayName is the friendly display name (like "John Doe")
  ///
  /// Flow:
  /// 1. Immediately update Profile.displayName in local database (optimistic update)
  /// 2. User sees change instantly via existing watch streams
  /// 3. Call remote API in background
  /// 4. Update database again based on API result (success/failure)
  /// 5. Return API response/error to UI for error handling only
  Future<Resource<String>> updateUserDisplayName(String newDisplayName) async {
    final sessionKey = Config.getInstance().activeSessionKey ?? '';
    try {
      final currentUser =
          await localSource.getUserBySessionAndUserId(sessionKey);
      if (currentUser == null) return Resource.error('User not found');

      final profile = currentUser.profile.target;
      if (profile == null) return Resource.error('User profile not found');

      final originalDisplayName = profile.displayName;

      // ✅ Step 1: Local-first update
      profile.displayName = newDisplayName;
      final updateTime = DateTime.now();
      profile.updateTime = updateTime;
      currentUser.updateTime = updateTime;
      await localSource.updateUserWithProfile(currentUser, profile);

      // ✅ Step 2: Try remote sync
      try {
        final apiSuccess =
            await remoteSource.updateUserDisplayName(newDisplayName);

        if (apiSuccess) {
          // Optional: mark sync completed (no-op if already updated)
          return Resource.success(newDisplayName);
        } else {
          // ❌ API failed → rollback local
          await _rollbackDisplayName(
            currentUser,
            profile,
            originalDisplayName,
            'API failed',
          );
          return Resource.error('Failed to update display name on server');
        }
      } catch (apiError) {
        // ❌ Exception during API → rollback local
        await _rollbackDisplayName(
          currentUser,
          profile,
          originalDisplayName,
          apiError.toString(),
        );
        return Resource.error('Network error: $apiError');
      }
    } catch (e) {
      logOperation('Unexpected error in updateUserDisplayName', e.toString());
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Future<void> _rollbackDisplayName(
    User currentUser,
    Profile profile,
    String originalDisplayName,
    String reason,
  ) async {
    try {
      logOperation('Rolling back displayName due to $reason');

      profile.displayName = originalDisplayName;
      final rollbackTime = DateTime.now();
      profile.updateTime = rollbackTime;
      currentUser.updateTime = rollbackTime;

      await localSource.updateUserWithProfile(currentUser, profile);
    } catch (rollbackError) {
      logOperation('❌ Emergency rollback failed', rollbackError.toString());
    }
  }

  /// Save profile entity through localDataSource
  /// This follows proper architecture pattern by using data source layer
  Future<void> _saveProfile(Profile profile) async {
    try {
      logOperation(
        'Saving profile through localDataSource',
        profile.displayName,
      );
      await localSource.saveProfile(profile);
      logOperation(
        'Profile saved successfully through localDataSource',
        profile.displayName,
      );
    } catch (e) {
      logOperation(
        'Error saving profile through localDataSource',
        e.toString(),
      );
      throw Exception('Failed to save profile: $e');
    }
  }

  /// Update user avatar with optimistic update pattern
  ///
  /// IMPORTANT: This updates Profile.avatar and Profile.originalAvatar
  /// - Profile.avatar is the current avatar URL/path
  /// - Profile.originalAvatar is the backup/original avatar URL/path
  ///
  /// Flow:
  /// 1. Immediately update Profile.avatar and Profile.originalAvatar in local database (optimistic update)
  /// 2. User sees change instantly via existing watch streams
  /// 3. Call remote API in background
  /// 4. Update database again based on API result (success/failure)
  /// 5. Return API response/error to UI for error handling only
  Future<Resource<String>> updateUserAvatar(
    String newAvatarPath,
  ) async {
    final userId = Config.getInstance().activeSessionKey ?? '';
    try {
      // Step 1: Get current user from local database
      final currentUser = await localSource.get(userId);
      if (currentUser == null) {
        logOperation('User not found in local database', userId);
        return Resource.error('User not found');
      }

      // Store original avatar for rollback if needed
      final profile = currentUser.profile.target;
      if (profile == null) {
        logOperation('No profile found for user', userId);
        return Resource.error('User profile not found');
      }

      final originalAvatar = profile.avatar;
      final originalAvatarBackup = profile.originalAvatar;
      logOperation('Original profile avatar: $originalAvatar');
      logOperation('Original profile originalAvatar: $originalAvatarBackup');

      // Step 2: Optimistic update - immediately update Profile.avatar and Profile.originalAvatar
      logOperation('Performing optimistic update to Profile.avatar');

      // Update Profile.avatar and Profile.originalAvatar
      final updatedProfile = profile.copyWith(
        avatar: newAvatarPath,
        originalAvatar: newAvatarPath,
      );

      // Save updated profile first (required for ObjectBox relationship)
      await _saveProfile(updatedProfile);

      // Create updated user with new profile relationship
      final updatedUser = currentUser.copyWith(
        updateTime: DateTime.now(),
        isPartial: true,
      );
      updatedUser.profile.target = updatedProfile;

      await localSource.insert(updatedUser);

      // Step 3: Call remote API in background
      final apiSuccess = await remoteSource.updateUserAvatar(newAvatarPath);

      // Step 4: Handle API result and update database accordingly
      if (apiSuccess) {
        return Resource.success(newAvatarPath);
      } else {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Rollback: restore original Profile.avatar and Profile.originalAvatar
        final rollbackProfile = profile.copyWith(
          avatar: originalAvatar,
          originalAvatar: originalAvatarBackup,
        );

        // Save rollback profile first
        await _saveProfile(rollbackProfile);

        final rollbackUser = currentUser.copyWith(
          updateTime: DateTime.now(),
        );
        rollbackUser.profile.target = rollbackProfile;

        await localSource.insert(rollbackUser);

        return Resource.error('Failed to update avatar on server');
      }
    } catch (e) {
      // Try to rollback if we have the user data
      try {
        final userId = Config.getInstance().activeSessionKey ?? '';

        final currentUser = await localSource.get(userId);
        if (currentUser != null) {
          // This might not be the original avatar, but it's better than leaving corrupted data
          logOperation('🔄 Attempting emergency rollback');
        }
      } catch (rollbackError) {
        logOperation('❌ Rollback also failed', rollbackError.toString());
      }

      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Future<Resource<String>> removeUserAvatar() async {
    final userId = Config.getInstance().activeSessionKey ?? '';

    // Step 1: Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation('User not found in local database', userId);
      return Resource.error('User not found');
    }

    // Step 2: Check if profile exists
    final profile = currentUser.profile.target;
    if (profile == null) {
      logOperation('No profile found for user', userId);
      return Resource.error('User profile not found');
    }

    // Step 3: Optimistic update
    logOperation('Performing optimistic update to Profile.avatar');

    final updatedProfile = profile.copyWith(avatar: '');
    await _saveProfile(updatedProfile);

    final updatedUser = currentUser.copyWith(
      updateTime: DateTime.now(),
      isPartial: true,
    );
    updatedUser.profile.target = updatedProfile;
    await localSource.insert(updatedUser);

    try {
      // Step 4: Call remote API
      final apiSuccess = await remoteSource.removeUserAvatar();

      if (apiSuccess) {
        return Resource.success('');
      } else {
        // API returned false: rollback
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        final rollbackProfile = profile.copyWith(avatar: profile.avatar);
        await _saveProfile(rollbackProfile);

        final rollbackUser = currentUser.copyWith(updateTime: DateTime.now());
        rollbackUser.profile.target = rollbackProfile;
        await localSource.insert(rollbackUser);

        return Resource.error('Failed to remove avatar on server');
      }
    } catch (e, stackTrace) {
      // Exception occurred: rollback
      logOperation(
        '❌ Exception during API call - rolling back',
        '$e\n$stackTrace',
      );

      final rollbackProfile = profile.copyWith(avatar: profile.avatar);
      await _saveProfile(rollbackProfile);

      final rollbackUser = currentUser.copyWith(updateTime: DateTime.now());
      rollbackUser.profile.target = rollbackProfile;
      await localSource.insert(rollbackUser);

      return Resource.error('An unexpected error occurred: ${e.toString()}', e);
    }
  }

  Future<Resource<String>> addUserCoverPhoto(
    String newCoverPath,
  ) async {
    final userId = Config.getInstance().activeSessionKey ?? '';
    try {
      // Step 1: Get current user from local database
      final currentUser = await localSource.get(userId);
      if (currentUser == null) {
        logOperation('User not found in local database', userId);
        return Resource.error('User not found');
      }

      // Store original avatar for rollback if needed
      final profile = currentUser.profile.target;
      if (profile == null) {
        logOperation('No profile found for user', userId);
        return Resource.error('User profile not found');
      }

      // Step 2: Optimistic update - immediately update Profile.avatar and Profile.originalAvatar
      logOperation('Performing optimistic update to Profile.avatar');

      // Update Profile.avatar and Profile.originalAvatar
      final updatedProfile = profile.copyWith(
        cover: newCoverPath,
      );

      // Save updated profile first (required for ObjectBox relationship)
      await _saveProfile(updatedProfile);

      // Create updated user with new profile relationship
      final updatedUser = currentUser.copyWith(
        updateTime: DateTime.now(),
      );
      updatedUser.profile.target = updatedProfile;

      await localSource.insert(updatedUser);

      // Step 3: Call remote API in background
      final apiSuccess = await remoteSource.addUserCoverPhoto(newCoverPath);

      // Step 4: Handle API result and update database accordingly
      if (apiSuccess) {
        return Resource.success(newCoverPath);
      } else {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Rollback: restore original Profile.avatar and Profile.originalAvatar
        final rollbackProfile = profile.copyWith(
          cover: profile.cover,
        );

        // Save rollback profile first
        await _saveProfile(rollbackProfile);

        final rollbackUser = currentUser.copyWith(
          updateTime: DateTime.now(),
        );
        rollbackUser.profile.target = rollbackProfile;

        await localSource.insert(rollbackUser);

        return Resource.error('Failed to update avatar on server');
      }
    } catch (e) {
      // Try to rollback if we have the user data
      try {
        final userId = Config.getInstance().activeSessionKey ?? '';

        final currentUser = await localSource.get(userId);
        if (currentUser != null) {
          // This might not be the original avatar, but it's better than leaving corrupted data
          logOperation('🔄 Attempting emergency rollback');
        }
      } catch (rollbackError) {
        logOperation('❌ Rollback also failed', rollbackError.toString());
      }

      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Turn on/off global notification setting using local-first strategy
  ///
  /// This method follows the local-first pattern:
  /// 1. Optimistically update local database immediately
  /// 2. Call remote API in background
  /// 3. Rollback local changes if API call fails
  ///
  /// [isTurnOn] - true to enable notifications, false to disable
  Future<Resource<bool>> turnOnOfNotification(bool isTurnOn) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null || sessionKey.isEmpty) {
      logOperation('❌ No active session for notification update');
      return Resource.error('No active session');
    }

    User? currentUser;
    bool? originalNotificationStatus;

    try {
      // Step 1: Get current user from local database
      logOperation(
        '🔄 Getting current user for notification update',
        'sessionKey: $sessionKey, isTurnOn: $isTurnOn',
      );

      currentUser = await localSource.getUserBySessionAndUserId(sessionKey);
      if (currentUser == null) {
        logOperation('❌ User not found for notification update');
        return Resource.error('User not found');
      }

      // Store original notification status for potential rollback
      originalNotificationStatus = currentUser.globalNotificationStatus;
      logOperation(
        'Original notification status: $originalNotificationStatus',
        'userId: ${currentUser.userId}',
      );

      // Step 2: Optimistic update - immediately update local database
      logOperation('🚀 Performing optimistic notification status update');

      final updatedUser = currentUser.copyWith(
        globalNotificationStatus: isTurnOn,
        updateTime: DateTime.now(),
      );

      await localSource.insert(updatedUser);

      logOperation(
        '✅ Local notification status updated successfully',
        'userId: ${currentUser.userId}, newStatus: $isTurnOn',
      );

      // Step 3: Call remote API in background
      logOperation('🌐 Calling remote API for notification update');

      final apiSuccess =
          await remoteSource.turnOnOfGlobalNotification(isTurnOn);

      if (apiSuccess) {
        logOperation(
          '✅ Remote notification update successful',
          'userId: ${currentUser.userId}, status: $isTurnOn',
        );
        return Resource.success(isTurnOn);
      } else {
        // Step 4: API call failed - rollback local changes
        logOperation(
          '❌ Remote notification update failed - rolling back',
          'userId: ${currentUser.userId}',
        );

        final rollbackUser = updatedUser.copyWith(
          globalNotificationStatus: originalNotificationStatus,
          updateTime: DateTime.now(),
        );

        await localSource.insert(rollbackUser);

        logOperation(
          '🔄 Rollback completed',
          'userId: ${currentUser.userId}, restoredStatus: $originalNotificationStatus',
        );

        return Resource.error(
          'Failed to update notification setting on server',
        );
      }
    } catch (e) {
      logOperation(
        '❌ Error during notification update',
        'sessionKey: $sessionKey, error: ${e}',
      );

      // Try to rollback if we have the original data
      if (currentUser != null && originalNotificationStatus != null) {
        try {
          final rollbackUser = currentUser.copyWith(
            globalNotificationStatus: originalNotificationStatus,
            updateTime: DateTime.now(),
          );
          await localSource.insert(rollbackUser);
          logOperation('🔄 Emergency rollback completed');
        } catch (rollbackError) {
          logOperation(
            '❌ Emergency rollback failed',
            rollbackError.toString(),
          );
        }
      }

      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Turn on/off single notification setting using local-first strategy
  Future<Resource<bool>> turnOnOfSingleNotification(bool isTurnOn) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null || sessionKey.isEmpty) {
      logOperation('❌ No active session for single notification update');
      return Resource.error('No active session');
    }

    User? currentUser;
    bool? originalNotificationStatus;

    try {
      logOperation(
        '🔄 Getting current user for single notification update',
        'sessionKey: $sessionKey, isTurnOn: $isTurnOn',
      );

      currentUser = await localSource.getUserBySessionAndUserId(sessionKey);
      if (currentUser == null) {
        logOperation('❌ User not found for single notification update');
        return Resource.error('User not found');
      }

      originalNotificationStatus = currentUser.singleNotificationStatus;
      logOperation(
        'Original single notification status: $originalNotificationStatus',
        'userId: ${currentUser.userId}',
      );

      final updatedUser = currentUser.copyWith(
        singleNotificationStatus: isTurnOn,
        updateTime: DateTime.now(),
      );

      await localSource.insert(updatedUser);

      logOperation(
        '✅ Local single notification status updated successfully',
        'userId: ${currentUser.userId}, newStatus: $isTurnOn',
      );

      final apiSuccess =
          await remoteSource.turnOnOfSingleNotification(isTurnOn);

      if (apiSuccess) {
        logOperation(
          '✅ Remote single notification update successful',
          'userId: ${currentUser.userId}, status: $isTurnOn',
        );
        return Resource.success(isTurnOn);
      } else {
        logOperation(
          '❌ Remote single notification update failed - rolling back',
          'userId: ${currentUser.userId}',
        );

        final rollbackUser = updatedUser.copyWith(
          singleNotificationStatus: originalNotificationStatus,
          updateTime: DateTime.now(),
        );

        await localSource.insert(rollbackUser);

        logOperation(
          '🔄 Rollback completed',
          'userId: ${currentUser.userId}, restoredStatus: $originalNotificationStatus',
        );

        return Resource.error(
          'Failed to update single notification setting on server',
        );
      }
    } catch (e) {
      logOperation(
        '❌ Error during single notification update',
        'sessionKey: $sessionKey, error: $e',
      );

      if (currentUser != null && originalNotificationStatus != null) {
        try {
          final rollbackUser = currentUser.copyWith(
            singleNotificationStatus: originalNotificationStatus,
            updateTime: DateTime.now(),
          );
          await localSource.insert(rollbackUser);
          logOperation('🔄 Emergency rollback completed');
        } catch (rollbackError) {
          logOperation(
            '❌ Emergency rollback failed',
            rollbackError.toString(),
          );
        }
      }

      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Turn on/off group notification setting using local-first strategy
  Future<Resource<bool>> turnOnOfGroupNotification(bool isTurnOn) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null || sessionKey.isEmpty) {
      logOperation('❌ No active session for group notification update');
      return Resource.error('No active session');
    }

    User? currentUser;
    bool? originalNotificationStatus;

    try {
      logOperation(
        '🔄 Getting current user for group notification update',
        'sessionKey: $sessionKey, isTurnOn: $isTurnOn',
      );

      currentUser = await localSource.getUserBySessionAndUserId(sessionKey);
      if (currentUser == null) {
        logOperation('❌ User not found for group notification update');
        return Resource.error('User not found');
      }

      originalNotificationStatus = currentUser.groupNotificationStatus;
      logOperation(
        'Original group notification status: $originalNotificationStatus',
        'userId: ${currentUser.userId}',
      );

      final updatedUser = currentUser.copyWith(
        groupNotificationStatus: isTurnOn,
        updateTime: DateTime.now(),
      );

      await localSource.insert(updatedUser);

      logOperation(
        '✅ Local group notification status updated successfully',
        'userId: ${currentUser.userId}, newStatus: $isTurnOn',
      );

      final apiSuccess = await remoteSource.turnOnOfGroupNotification(isTurnOn);

      if (apiSuccess) {
        logOperation(
          '✅ Remote group notification update successful',
          'userId: ${currentUser.userId}, status: $isTurnOn',
        );
        return Resource.success(isTurnOn);
      } else {
        logOperation(
          '❌ Remote group notification update failed - rolling back',
          'userId: ${currentUser.userId}',
        );

        final rollbackUser = updatedUser.copyWith(
          groupNotificationStatus: originalNotificationStatus,
          updateTime: DateTime.now(),
        );

        await localSource.insert(rollbackUser);

        logOperation(
          '🔄 Rollback completed',
          'userId: ${currentUser.userId}, restoredStatus: $originalNotificationStatus',
        );

        return Resource.error(
          'Failed to update group notification setting on server',
        );
      }
    } catch (e) {
      logOperation(
        '❌ Error during group notification update',
        'sessionKey: $sessionKey, error: $e',
      );

      if (currentUser != null && originalNotificationStatus != null) {
        try {
          final rollbackUser = currentUser.copyWith(
            groupNotificationStatus: originalNotificationStatus,
            updateTime: DateTime.now(),
          );
          await localSource.insert(rollbackUser);
          logOperation('🔄 Emergency rollback completed');
        } catch (rollbackError) {
          logOperation(
            '❌ Emergency rollback failed',
            rollbackError.toString(),
          );
        }
      }

      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Future<Resource<String>> updateUserCoverPhoto(
    String newCoverPath,
  ) async {
    final userId = Config.getInstance().activeSessionKey ?? '';
    try {
      // Step 1: Get current user from local database
      final currentUser = await localSource.get(userId);
      if (currentUser == null) {
        logOperation('User not found in local database', userId);
        return Resource.error('User not found');
      }

      // Store original avatar for rollback if needed
      final profile = currentUser.profile.target;
      if (profile == null) {
        logOperation('No profile found for user', userId);
        return Resource.error('User profile not found');
      }

      // Step 2: Optimistic update - immediately update Profile.avatar and Profile.originalAvatar
      logOperation('Performing optimistic update to Profile.avatar');

      // Update Profile.avatar and Profile.originalAvatar
      final updatedProfile = profile.copyWith(
        cover: newCoverPath,
      );

      // Save updated profile first (required for ObjectBox relationship)
      await _saveProfile(updatedProfile);

      // Create updated user with new profile relationship
      final updatedUser = currentUser.copyWith(
        updateTime: DateTime.now(),
      );
      updatedUser.profile.target = updatedProfile;

      await localSource.insert(updatedUser);

      // Step 3: Call remote API in background
      final apiSuccess = await remoteSource.updateUserCoverPhoto(newCoverPath);

      // Step 4: Handle API result and update database accordingly
      if (apiSuccess) {
        // Update local database with confirmed data from API
        final confirmedProfile = updatedProfile.copyWith(
          cover: newCoverPath,
        );

        // Save confirmed profile first
        await _saveProfile(confirmedProfile);

        final confirmedUser = updatedUser.copyWith(
          updateTime: DateTime.now(),
          // Mark as synced or add any additional fields from API response
        );
        confirmedUser.profile.target = confirmedProfile;

        await localSource.insert(confirmedUser);

        return Resource.success(newCoverPath);
      } else {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Rollback: restore original Profile.avatar and Profile.originalAvatar
        final rollbackProfile = profile.copyWith(
          cover: profile.cover,
        );

        // Save rollback profile first
        await _saveProfile(rollbackProfile);

        final rollbackUser = currentUser.copyWith(
          updateTime: DateTime.now(),
        );
        rollbackUser.profile.target = rollbackProfile;

        await localSource.insert(rollbackUser);

        return Resource.error('Failed to update avatar on server');
      }
    } catch (e) {
      // Try to rollback if we have the user data
      try {
        final userId = Config.getInstance().activeSessionKey ?? '';

        final currentUser = await localSource.get(userId);
        if (currentUser != null) {
          // This might not be the original avatar, but it's better than leaving corrupted data
          logOperation('🔄 Attempting emergency rollback');
        }
      } catch (rollbackError) {
        logOperation('❌ Rollback also failed', rollbackError.toString());
      }

      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Future<Resource<String>> removeUserCover() async {
    final userId = Config.getInstance().activeSessionKey ?? '';

    // Step 1: Get current user from local database
    final currentUser = await localSource.get(userId);
    if (currentUser == null) {
      logOperation('User not found in local database', userId);
      return Resource.error('User not found');
    }

    // Step 2: Retrieve user profile
    final profile = currentUser.profile.target;
    if (profile == null) {
      logOperation('No profile found for user', userId);
      return Resource.error('User profile not found');
    }

    // Step 3: Optimistic update - clear profile.cover
    logOperation('Performing optimistic update to Profile.cover');

    final updatedProfile = profile.copyWith(cover: '');
    await _saveProfile(updatedProfile);

    final updatedUser = currentUser.copyWith(updateTime: DateTime.now());
    updatedUser.profile.target = updatedProfile;
    await localSource.insert(updatedUser);

    try {
      // Step 4: Call remote API
      final apiSuccess = await remoteSource.removeUserCoverPhoto();

      if (apiSuccess) {
        return Resource.success('');
      } else {
        // API responded false — rollback
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        final rollbackProfile = profile.copyWith(cover: profile.cover);
        await _saveProfile(rollbackProfile);

        final rollbackUser = currentUser.copyWith(updateTime: DateTime.now());
        rollbackUser.profile.target = rollbackProfile;
        await localSource.insert(rollbackUser);

        return Resource.error('Failed to remove cover on server');
      }
    } catch (e, stackTrace) {
      // Exception occurred — rollback
      logOperation(
        '❌ Exception during API call - rolling back',
        '$e\n$stackTrace',
      );

      final rollbackProfile = profile.copyWith(cover: profile.cover);
      await _saveProfile(rollbackProfile);

      final rollbackUser = currentUser.copyWith(updateTime: DateTime.now());
      rollbackUser.profile.target = rollbackProfile;
      await localSource.insert(rollbackUser);

      return Resource.error('An unexpected error occurred: ${e.toString()}', e);
    }
  }

  /// Get all users by set of user IDs
  Stream<List<User>> getAllUsersBySetUserIdOnChannelStream(
    Set<String> setUserId,
  ) {
    return localSource.getAllUsersBySetUserIdOnChannelStream(setUserId);
  }

  //endregion

  //region UserStatus Operations

  Future<Resource<User>> getCurrentUserOrError() async {
    final sessionKey = Config.getInstance().activeSessionKey;
    if (sessionKey == null || sessionKey.isEmpty) {
      return Resource.error('No active session key found');
    }

    final currentUser = await localSource.get(sessionKey);
    if (currentUser == null) {
      logOperation('❌ Current user not found');
      return Resource.error('Current user not found');
    }

    return Resource.success(currentUser);
  }

  /// Add user status with optimistic updates
  /// Implements local-first strategy with server synchronization
  Future<Resource<UserStatus>> addUserStatus({
    required String content,
    required String status,
    int? expireAfterTime,
  }) async {
    try {
      logOperation('Adding user status', 'text: $content, emoji: $status');

      final userResult = await getCurrentUserOrError();
      if (userResult.hasError)
        return Resource.error(userResult.error ?? 'Unknown error');

      final currentUser = userResult.data!;

      // Step 2: Create optimistic UserStatus
      final optimisticStatus = UserStatus.create(
        userId: currentUser.userId,
        sessionKey: currentUser.sessionKey,
        content: content,
        status: status,
        expireAfterTime: expireAfterTime,
        isCustom: true,
        isPartial: true,
      );

      // Step 3: Save to local DB optimistically
      await _saveUserStatusRelationship(currentUser, optimisticStatus);
      logOperation('✅ Optimistic user status saved locally');

      // Step 4: Call remote API
      final remoteStatus = await remoteSource.addUserStatus(
        content: content,
        status: status,
        expireAfterTime: expireAfterTime,
      );

      if (remoteStatus != null) {
        // Step 5: Merge optimistic data to avoid losing fields
        final confirmedStatus = optimisticStatus.copyWith(
          isPartial: false,
        );

        await _saveUserStatusRelationship(currentUser, confirmedStatus);
        logOperation('✅ User status confirmed by server');

        return Resource.success(confirmedStatus);
      } else {
        // Step 6: Rollback if remote failed
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Optional: Only delete if it's still marked as optimistic
        currentUser.status.target = null;
        await localSource.insert(currentUser);

        return Resource.error('Failed to add status on server');
      }
    } catch (e) {
      logOperation('❌ Error during status add', e.toString());
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  Future<void> _saveUserStatusRelationship(User user, UserStatus status) async {
    user.status.target = status;
    await localSource.insert(user);
  }

  /// Update user status with optimistic updates
  /// Implements local-first strategy with server synchronization
  Future<Resource<bool>> updateUserStatus({
    required String content,
    required String status,
    int? expireAfterTime,
  }) async {
    try {
      logOperation(
        'Updating user status',
        'text: $content, emoji: $status',
      );

      // Get current user
      final userResult = await getCurrentUserOrError();
      if (userResult.hasError) {
        return Resource.error(userResult.error ?? 'Unknown error');
      }
      final currentUser = userResult.data!;

      // Get existing status for rollback
      final existingStatus = currentUser.status.target;

      UserStatus optimisticStatus;
      if (existingStatus == null) {
        // No status exists, create new
        optimisticStatus = UserStatus.create(
          userId: currentUser.userId,
          sessionKey: currentUser.sessionKey,
          content: content,
          status: status,
          expireAfterTime: expireAfterTime,
          isCustom: true,
          isPartial: true,
        );
      } else {
        // Update existing status with new data, set isPartial true
        optimisticStatus = existingStatus.copyWith(
          content: content,
          status: status,
          expireAfterTime: expireAfterTime,
          isCustom: true,
          isPartial: true,
        );
        await localSource.updateUserStatus(
          currentUser.userId,
          optimisticStatus,
        );
      }
      await _saveUserStatusRelationship(currentUser, optimisticStatus);
      logOperation('✅ Optimistic user status update saved locally');

      // Call remote API
      final success = await remoteSource.updateUserStatus(
        content: content,
        status: status,
      );

      if (success) {
        // Server success - mark as complete
        final confirmedStatus = optimisticStatus.copyWith(isPartial: false);
        await localSource.updateUserStatus(
          currentUser.userId,
          confirmedStatus,
        );
        logOperation('✅ User status update confirmed by server');
        return Resource.success(true);
      } else {
        // Rollback: restore original status or delete if none existed
        if (existingStatus != null) {
          await localSource.updateUserStatus(
            currentUser.userId,
            existingStatus,
          );
        } else {
          // If there was no status before, remove it
          await localSource.deleteUserStatus(currentUser.userId);
        }
        logOperation('🔄 Rollback completed - original status restored');
        return Resource.error('Failed to update status on server');
      }
    } catch (e) {
      logOperation('❌ Error during status update', e.toString());
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Delete user status with optimistic updates
  /// Implements local-first strategy with server synchronization
  Future<Resource<bool>> deleteUserStatus() async {
    try {
      logOperation('Deleting user status');

      // Get current user
      final userResult = await getCurrentUserOrError();
      if (userResult.hasError)
        return Resource.error(userResult.error ?? 'Unknown error');

      final currentUser = userResult.data!;

      // Get existing status for rollback
      final existStatus = currentUser.status.target;
      // Delete optimistically from local database
      await localSource.deleteUserStatus(currentUser.userId);

      // Call remote API
      final success = await remoteSource.deleteUserStatus();

      if (success) {
        logOperation('✅ User status deletion confirmed by server');
        return Resource.success(true);
      } else {
        // Rollback: restore original status
        if (existStatus != null) {
          await _saveUserStatusRelationship(currentUser, existStatus);
        }

        return Resource.error('Failed to delete status on server');
      }
    } catch (e) {
      logOperation('❌ Error during status deletion', e.toString());
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Get user status from local database
  /// Returns current user status or null if not found
  Future<UserStatus?> getUserStatus(String userId) async {
    try {
      logOperation('Getting user status', userId);
      return await localSource.getUserStatus(userId);
    } catch (e) {
      logOperation('❌ Error getting user status', '$userId: $e');
      return null;
    }
  }

  /// Watch user status changes
  /// Returns stream of user status updates
  Stream<UserStatus?> watchUserStatus(String userId) {
    logOperation('Watching user status', userId);
    return localSource.watchUserStatus(userId);
  }

  /// Watch user status changes
  /// Returns stream of user status updates
  Stream<List<Friend>> watchAllUserStatus() {
    return localSource.watchAllUserStatus();
  }

  //endregion

  //region UserPresence Business Logic

  /// Update user presence status with optimistic updates
  /// This method handles presence status changes with local-first approach
  Future<void> updateUserpresenceState(
    String userId,
    PresenceStateEnum presenceState, {
    String? deviceInfo,
  }) async {
    try {
      logOperation(
        'Updating user presence status',
        'userId: $userId, status: $presenceState',
      );

      // Get current user to determine sessionKey
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for presence update');
        throw Exception('Current user not found');
      }

      // Get current presence for rollback
      final currentPresence = await localSource.getUserPresence(userId);

      // Create optimistic update
      final updatedPresence = currentPresence?.copyWith(
            presenceState: presenceState,
            deviceInfo: deviceInfo ?? currentPresence.deviceInfo,
            lastActiveTime: DateTime.now(),
            updateTime: DateTime.now(),
          ) ??
          UserPresence.createNew(
            userId: userId,
            sessionKey: currentUser.sessionKey,
            presenceState: presenceState,
            deviceInfo: deviceInfo ?? '',
            lastActiveTime: DateTime.now(),
          );

      // Apply optimistic update locally
      await localSource.saveUserPresence(updatedPresence);

      try {
        // Sync with remote API
        await remoteSource.updateUserpresenceState(
          userId,
          presenceState.value,
          deviceInfo: deviceInfo,
        );

        logOperation(
          'Updated user presence status successfully',
          'userId: $userId, status: $presenceState',
        );
      } catch (e) {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Rollback: restore original presence or delete if none existed
        if (currentPresence != null) {
          await localSource.saveUserPresence(currentPresence);
        } else {
          await localSource.deleteUserPresence(userId);
        }

        rethrow;
      }
    } catch (e) {
      logOperation(
        'Error updating user presence status',
        'userId: $userId, error: $e',
      );
      rethrow;
    }
  }

  /// Update user typing status with optimistic updates
  /// This method handles typing status changes with local-first approach
  Future<void> updateUserTypingStatus(
    String userId,
    bool isTyping,
    String channelId,
  ) async {
    try {
      logOperation(
        'Updating user typing status',
        'userId: $userId, isTyping: $isTyping, channelId: $channelId',
      );

      // Get current user to determine sessionKey
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for typing status update');
        throw Exception('Current user not found');
      }

      // Get current presence for rollback
      final currentPresence = await localSource.getUserPresence(userId);

      // Create optimistic update
      final updatedPresence = currentPresence?.copyWith(
            isTyping: isTyping,
            typingChannelId: isTyping ? channelId : '',
            updateTime: DateTime.now(),
          ) ??
          UserPresence.createNew(
            userId: userId,
            sessionKey: currentUser.sessionKey,
            isTyping: isTyping,
            typingChannelId: isTyping ? channelId : '',
          );

      // Apply optimistic update locally
      await localSource.saveUserPresence(updatedPresence);

      try {
        // Sync with remote API
        await remoteSource.updateUserTypingStatus(userId, isTyping, channelId);

        logOperation(
          'Updated user typing status successfully',
          'userId: $userId, isTyping: $isTyping',
        );
      } catch (e) {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Rollback: restore original presence or delete if none existed
        if (currentPresence != null) {
          await localSource.saveUserPresence(currentPresence);
        } else {
          await localSource.deleteUserPresence(userId);
        }

        rethrow;
      }
    } catch (e) {
      logOperation(
        'Error updating user typing status',
        'userId: $userId, error: $e',
      );
      rethrow;
    }
  }

  /// Set user as offline with optimistic updates
  /// This method handles user going offline with local-first approach
  Future<void> setUserOffline(String userId) async {
    try {
      logOperation('Setting user offline', userId);

      // Get current user to determine sessionKey
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for offline status');
        throw Exception('Current user not found');
      }

      // Get current presence for rollback
      final currentPresence = await localSource.getUserPresence(userId);

      // Create offline presence
      final offlinePresence = UserPresenceSerializer.createOfflinePresence(
        userId: userId,
        sessionKey: currentUser.sessionKey,
      );

      // Apply optimistic update locally
      await localSource.saveUserPresence(offlinePresence);

      try {
        // Sync with remote API
        await remoteSource.setUserOffline(userId);

        logOperation('Set user offline successfully', userId);
      } catch (e) {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic update',
        );

        // Rollback: restore original presence or delete if none existed
        if (currentPresence != null) {
          await localSource.saveUserPresence(currentPresence);
        } else {
          await localSource.deleteUserPresence(userId);
        }

        rethrow;
      }
    } catch (e) {
      logOperation('Error setting user offline', '$userId: $e');
      rethrow;
    }
  }

  /// Get user presence
  /// This method retrieves user presence with fallback to remote if not found locally
  Future<UserPresence?> getUserPresence(String userId) async {
    try {
      logOperation('Getting user presence', userId);

      // Try local first
      final localPresence = await localSource.getUserPresence(userId);
      if (localPresence != null) {
        logOperation('Found user presence locally', userId);
        return localPresence;
      }

      // Fallback to remote
      logOperation('User presence not found locally, trying remote', userId);
      final remotePresence = await remoteSource.getUserPresence(userId);

      if (remotePresence != null) {
        // Save to local for future use
        await localSource.saveUserPresence(remotePresence);
        logOperation('Found and cached user presence from remote', userId);
        return remotePresence;
      }

      logOperation('User presence not found', userId);
      return null;
    } catch (e) {
      logOperation('Error getting user presence', '$userId: $e');
      return null;
    }
  }

  /// Watch user presence
  /// This method provides reactive stream of user presence updates
  Stream<UserPresence?> watchUserPresence(String userId) {
    logOperation('Watching user presence', userId);
    return localSource.watchUserPresence(userId);
  }

  /// Delete user presence
  /// This method removes user presence data
  Future<bool> deleteUserPresence(String userId) async {
    try {
      logOperation('Deleting user presence', userId);
      final deleted = await localSource.deleteUserPresence(userId);

      if (deleted) {
        logOperation('Deleted user presence successfully', userId);
      } else {
        logOperation('User presence not found for deletion', userId);
      }

      return deleted;
    } catch (e) {
      logOperation('Error deleting user presence', '$userId: $e');
      return false;
    }
  }

  //endregion

  //region VisitedProfile Business Logic

  /// Record visited profile with optimistic updates
  /// Implements local-first strategy with server synchronization
  Future<Resource<bool>> visitedProfile(String visitedUserId) async {
    try {
      logOperation('Recording visited profile', visitedUserId);

      // Get current user
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for visited profile recording');
        return Resource.error('Current user not found');
      }

      // Check if visiting self
      if (currentUser.userId == visitedUserId) {
        logOperation('ℹ️ Cannot visit own profile');
        return Resource.success(true);
      }

      // Create or update local visited profile record
      final existingProfile = await localSource.getVisitedProfile(
        currentUser.sessionKey,
        visitedUserId,
      );

      final visitedProfile = existingProfile?.copyWith(
            updateTime: DateTime.now(),
            isRead: false, // Mark as unread for new visit
          ) ??
          VisitedProfileSerializer.createLocalVisitedProfile(
            sessionKey: currentUser.sessionKey,
            visitedUserId: visitedUserId,
            isRead: false,
          );

      // Save optimistically to local database
      await localSource.saveVisitedProfile(visitedProfile);
      logOperation('✅ Optimistic visited profile record saved locally');

      // Call remote API
      final success = await remoteSource.visitedProfile(visitedUserId);

      if (success) {
        logOperation('✅ Visited profile recording confirmed by server');
        return Resource.success(true);
      } else {
        logOperation(
          '❌ Remote API call failed - keeping local record',
        );
        // Keep local record even if remote fails for better UX
        return Resource.success(true);
      }
    } catch (e) {
      logOperation('❌ Error recording visited profile', '$visitedUserId: $e');
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Delete visited profile with optimistic updates
  /// Implements local-first strategy with server synchronization
  Future<Resource<bool>> deleteVisitedProfile(String visitedUserId) async {
    try {
      logOperation('Deleting visited profile', visitedUserId);

      // Get current user
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for visited profile deletion');
        return Resource.error('Current user not found');
      }

      // Get existing profile for rollback
      final existingProfile = await localSource.getVisitedProfile(
        currentUser.sessionKey,
        visitedUserId,
      );

      if (existingProfile == null) {
        logOperation('ℹ️ No visited profile to delete');
        return Resource.success(true);
      }

      // Delete optimistically from local database
      await localSource.deleteVisitedProfile(
        currentUser.sessionKey,
        visitedUserId,
      );
      logOperation('✅ Optimistic visited profile deletion completed locally');

      // Call remote API
      final success =
          await remoteSource.deleteUserVisitedProfile(visitedUserId);

      if (success) {
        logOperation('✅ Visited profile deletion confirmed by server');
        return Resource.success(true);
      } else {
        logOperation(
          '❌ Remote API call failed - rolling back optimistic deletion',
        );

        // Rollback: restore original profile
        await localSource.saveVisitedProfile(existingProfile);
        logOperation('🔄 Rollback completed - original profile restored');

        return Resource.error('Failed to delete visited profile on server');
      }
    } catch (e) {
      logOperation('❌ Error deleting visited profile', '$visitedUserId: $e');
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Clear all visited profile notifications
  /// Marks all visited profiles as read and clears notifications on server
  Future<Resource<bool>> clearVisitedProfileNotifications() async {
    try {
      logOperation('Clearing visited profile notifications');

      // Get current user
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for clearing notifications');
        return Resource.error('Current user not found');
      }

      // Mark all as read locally first (optimistic update)
      final markedCount = await localSource
          .markAllVisitedProfilesAsRead(currentUser.sessionKey);
      logOperation('✅ Marked $markedCount visited profiles as read locally');

      // Call remote API
      final success = await remoteSource.clearUserVisitedProfileNotifications();

      if (success) {
        logOperation('✅ Visited profile notifications cleared on server');
        return Resource.success(true);
      } else {
        logOperation('⚠️ Remote API call failed but local update kept');
        // Keep local changes even if remote fails for better UX
        return Resource.success(true);
      }
    } catch (e) {
      logOperation(
        '❌ Error clearing visited profile notifications',
        e.toString(),
      );
      return Resource.error('Unexpected error: ${e.toString()}', e);
    }
  }

  /// Watch visited profiles changes
  /// Returns stream of visited profiles updates for current session
  Stream<List<VisitedProfile>> watchVisitedProfiles() async* {
    try {
      // Get current user
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for watching visited profiles');
        yield [];
        return;
      }

      logOperation('Watching visited profiles', currentUser.sessionKey);
      yield* localSource.watchVisitedProfiles(currentUser.sessionKey);
    } catch (e) {
      logOperation('❌ Error watching visited profiles', e.toString());
      yield [];
    }
  }

  /// Get unread visited profiles count
  /// Returns number of unread visited profiles for current session
  Future<int> getUnreadVisitedProfilesCount() async {
    try {
      // Get current user
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for getting unread count');
        return 0;
      }

      logOperation(
        'Getting unread visited profiles count',
        currentUser.sessionKey,
      );
      return await localSource
          .getUnreadVisitedProfilesCount(currentUser.sessionKey);
    } catch (e) {
      logOperation(
        '❌ Error getting unread visited profiles count',
        e.toString(),
      );
      return 0;
    }
  }

  /// Mark visited profile as read
  /// Updates read status for specific visited profile
  Future<bool> markVisitedProfileAsRead(String visitedUserId) async {
    try {
      // Get current user
      final currentUser = await localSource.get('me');
      if (currentUser == null) {
        logOperation('❌ Current user not found for marking as read');
        return false;
      }

      logOperation(
        'Marking visited profile as read',
        '$currentUser.sessionKey:$visitedUserId',
      );
      return await localSource.markVisitedProfileAsRead(
        currentUser.sessionKey,
        visitedUserId,
      );
    } catch (e) {
      logOperation(
        '❌ Error marking visited profile as read',
        '$visitedUserId: $e',
      );
      return false;
    }
  }

  Future<Resource<bool>> generateAndUpdateUserConnectLink() async {
    try {
      final userResult = await getCurrentUserOrError();
      if (userResult.hasError) {
        return Resource.error(userResult.error ?? 'Unknown error');
      }

      final currentUser = userResult.data!;

      final remoteConnectLink = await remoteSource.generateUserConnectLink();
      if (!StringUtils.isNullOrEmpty(remoteConnectLink)) {
        await localSource.updateUserConnectedLink(
          currentUser.userId,
          remoteConnectLink!,
        );
      } else {
        return Resource.error('Remote ConnectLink was null or empty');
      }

      return Resource.success(true);
    } catch (e) {
      return Resource.error('Error when generate and update connect link', e);
    }
  }

  Future<Resource<bool>> onBlockUser(String userId) async {
    try {
      final userResult = await getCurrentUserOrError();

      if (userResult.hasError) {
        return Resource.error(userResult.error ?? 'Unknown error');
      }
      final currentUser = userResult.data!;

      logOperation('Blocking user',
          'userId: $userId, sessionKey: ${currentUser.sessionKey}');

      final originalUser = currentUser.copyWith();

      final updatedUser = currentUser.copyWith(blocked: true);
      await localSource.insert(updatedUser);

      final success = await remoteSource.blockUser(userId);

      if (success) {
        logOperation('✅ Block user API success');
        return Resource.success(true);
      } else {
        await localSource.insert(originalUser);
        logOperation('❌ Block user API failed');

        return Resource.error('Remote block user error');
      }
    } catch (e) {
      try {
        final userResult = await getCurrentUserOrError();
        if (!userResult.hasError) {
          final originalUser = userResult.data!.copyWith(blocked: false);
          await localSource.insert(originalUser);
        }
      } catch (_) {
        logOperation('⚠️ Rollback also failed');
      }
      logOperation('❌ Exception during block user: $e');
      return Resource.error('Remote block user error', e);
    }
  }

  Future<Resource<bool>> onUnBlockUser(String userId) async {
    try {
      final success = await remoteSource.unBlockUser(userId);

      if (success) {
        logOperation('✅ Unblock user API success');
        return Resource.success(true);
      } else {
        logOperation('❌ Unblock user API failed');
        return Resource.error('Remote unblock user error');
      }
    } catch (e) {
      logOperation('❌ Exception during unblock user: $e');
      return Resource.error('Remote unblock user error', e);
    }
  }

  Future<Resource<bool>> onReportUser(
    String userId,
    V3ReportCategory? reportCategory,
    V3PretendingTo? pretendingTo,
    String? reportReason,
  ) async {
    try {
      logOperation(
        'Report user operation with:',
        '[reportCategory: $reportCategory, pretendingTo: $pretendingTo, reportReason:$reportReason],',
      );

      final apiSuccess = await remoteSource.reportUser(
        userId,
        reportCategory,
        pretendingTo,
        reportReason,
      );

      if (apiSuccess) {
        logOperation(
          'Report user operation successfully',
        );
        return Resource.success(true);
      }
      return Resource.error('Remote report user error');
    } catch (e) {
      return Resource.error('Remote report user error', e);
    }
  }

  //endregion
  Stream<List<User>> getUsersStream() {
    try {
      return localSource.getUsersStream();
    } catch (e) {
      logOperation('❌ Error getting users stream: $e');
      return Stream.error(Exception('Failed to get users stream: $e'));
    }
  }

  Future<User?> getUserById(String userId) async {
    try {
      return await localSource.get(userId);
    } catch (e) {
      logOperation('❌ Error getting user by ID: $e');
      return Future.error(Exception('Failed to get user by ID: $e'));
    }
  }

  User? getUserByIdAndSessionKey({
    required String userId,
    required String sessionKey,
  }) {
    return localSource.getUserByIdAndSessionKey(
      userId: userId,
      sessionKey: sessionKey,
    );
  }

  User? getUserByIdSync(String userId) {
    try {
      // Validate userId is not empty to prevent ObjectBox crash
      if (userId.trim().isEmpty) {
        logOperation('Error: userId is empty in getUserByIdSync', '');
        return null;
      }

      return localSource.getUserByIdSync(userId);
    } catch (e) {
      return null;
    }
  }

  Future<void>? syncUserStatus() async {
    final users = await remoteSource.listUserStatus();

    localSource.syncUserStatus(users);
  }

  Future<List<User>> listBlockedUser() async {
    return await remoteSource.listBlockedUser();
  }

  Future<String?> decodeUserConnectLink(String userConnectLink) async {
    try {
      return await remoteSource.decodeUserConnectLink(userConnectLink);
    } catch (e) {
      logOperation('decode user connected link failed: $e');
      return null;
    }
  }

  Future<Resource<List<User>>> getListInvitableUsers() async {
    try {
      final users = await remoteSource.listInvitableUsers();

      if (users.isNotEmpty) {
        return Resource.success(users);
      }

      return Resource.success([]);
    } catch (e) {
      return Resource.error('Failed to fetch invitable users');
    }
  }

  /// Sync users from remote API with local database
  /// Migrated from SyncUsersUseCase to follow data_router architecture
  Future<void> syncUser() async {
    try {
      // No user logged in
      if (Config.getInstance().activeSessionKey == null) {
        return;
      }

      // Get all existing users from local database
      final chatUsers = await localSource.getChatUsers();
      final users = await localSource.getAll();
      final userIds = {
        ...chatUsers.map((u) => u.userId),
        ...users.map((u) => u.userId),
      };

      // Get session metadata for sync timestamp
      var metadata = await localSource.getSessionMetadata();
      String userUpdateTimeAfter = metadata?.userUpdateTimeAfter != null
          ? TimeUtils.formatToISO8601(metadata!.userUpdateTimeAfter!)
          : TimeUtils.formatToISO8601(DateTime.now());

      // Calculate userUpdateTimeAfter if empty but users exist
      if (userUpdateTimeAfter.isEmpty &&
          (chatUsers.isNotEmpty || users.isNotEmpty)) {
        final maxChatUpdateTime = chatUsers.isNotEmpty
            ? chatUsers
                .map((u) => u.updateTime ?? DateTime.now())
                .fold<DateTime>(
                  DateTime.fromMillisecondsSinceEpoch(0),
                  (a, b) => a.isAfter(b) ? a : b,
                )
            : null;

        final maxUserUpdateTime = users.isNotEmpty
            ? users.map((u) => u.updateTime ?? DateTime.now()).fold<DateTime>(
                  DateTime.fromMillisecondsSinceEpoch(0),
                  (a, b) => a.isAfter(b) ? a : b,
                )
            : null;

        if (maxChatUpdateTime != null || maxUserUpdateTime != null) {
          userUpdateTimeAfter = [maxChatUpdateTime, maxUserUpdateTime]
              .whereType<DateTime>()
              .reduce((a, b) => a.isAfter(b) ? a : b)
              .toUtc()
              .toIso8601String();
        }
      }

      // If still empty, use a default timestamp (e.g., 30 days ago)
      if (userUpdateTimeAfter.isEmpty) {
        userUpdateTimeAfter = TimeUtils.getCurrentUTCTimeFormatted();
      }

      // Perform sync operation
      final result = await _getSyncUser(userIds.toList(), userUpdateTimeAfter);

      if (result != null) {
        final (updatedUser, _, deletedUsersList, blockedUsers, syncTime) =
            result;

        // Insert updated users
        if (updatedUser != null && updatedUser.isNotEmpty) {
          await localSource.insertAll(updatedUser);
          await localSource.insertAll(updatedUser);
        }

        // Delete blocked and deleted users
        for (final user in [...deletedUsersList, ...blockedUsers]) {
          await localSource.delete(user.userId);
          await localSource.deleteChatUser(user.userId);
        }

        // Update sync timestamp
        if (metadata != null) {
          final syncDateTime =
              DateTime.tryParse(syncTime) ?? DateTime.now().toUtc();
          await localSource.updateUserUpdateTimeAfter(syncDateTime);
        }
      }
    } catch (e, stackTrace) {
      logOperation('Error during user sync', '$e\n$stackTrace');
    }
  }

  /// Internal method to handle user sync with chunking
  Future<
      (
        List<User>?,
        List<User>,
        List<DeletedUser>,
        List<DeletedUser>,
        String,
      )?> _getSyncUser(List<String> userIds, String updateTimeAfter) async {
    try {
      // Remove current user from sync list
      userIds.removeWhere(
        (userid) => userid == Config.getInstance().activeSessionKey,
      );

      if (userIds.isEmpty) {
        return await _syncUsersOnce([], updateTimeAfter);
      }

      // Chunk user IDs to avoid API limits (50 users per request)
      final chunkedUserIds = [
        for (var i = 0; i < userIds.length; i += 50)
          userIds.sublist(i, i + 50 > userIds.length ? userIds.length : i + 50),
      ];

      final results = await Future.wait(
        chunkedUserIds.map((chunk) => _syncUsersOnce(chunk, updateTimeAfter)),
      );

      // Aggregate results from all chunks
      final totalUpdatedUsers = <User>[];
      final totalUpdatedChatUsers = <User>[];
      final totalDeletedUsers = <DeletedUser>[];
      final totalBlockedUsers = <DeletedUser>[];
      String finalSyncTime = '';

      for (final result in results) {
        final (updatedUser, _, deletedUsersList, blockedUsers, syncTime) =
            result;
        totalUpdatedUsers.addAll(updatedUser);
        totalUpdatedChatUsers.addAll(updatedUser);
        totalDeletedUsers.addAll(deletedUsersList);
        totalBlockedUsers.addAll(blockedUsers);
        if (syncTime.compareTo(finalSyncTime) > 0) finalSyncTime = syncTime;
      }

      return (
        totalUpdatedUsers,
        totalUpdatedChatUsers,
        totalDeletedUsers,
        totalBlockedUsers,
        finalSyncTime
      );
    } catch (e) {
      logOperation('Error in _getSyncUser', '$e');
      return null;
    }
  }

  /// Internal method to sync a single chunk of users
  Future<
      (
        List<User>,
        List<User>,
        List<DeletedUser>,
        List<DeletedUser>,
        String,
      )> _syncUsersOnce(List<String> userIds, String updateTimeAfter) async {
    // Call remote API to sync users
    final result = await remoteSource.syncUsers(
      updateTimeAfter: updateTimeAfter,
      userIds: userIds,
    );

    final users = result.data?.toList() ?? [];
    final deletedUsers = result.userDeleted?.toList() ?? [];

    // Convert API users to local User entities
    final updatedUser = users.map((user) {
      final userEntity = user.toUser();
      userEntity.sessionKey = Config.getInstance().activeSessionKey ?? '';
      return userEntity;
    }).toList();

    // Use the same User entities for chat users (no separate ChatUser needed)

    // Separate blocked and deleted users
    final blockedUsers = deletedUsers
        .where((u) => u.type?.name == 'USER_BLOCKED')
        .map((u) => DeletedUser(u.userId ?? '', u.username ?? ''))
        .toList();

    final deletedUsersList = deletedUsers
        .where((u) => u.type?.name == 'USER_DELETED')
        .map((u) => DeletedUser(u.userId ?? '', u.username ?? ''))
        .toList();

    // Handle deleted users - remove from chat friends
    if (deletedUsersList.isNotEmpty) {
      List<String> usersId =
          deletedUsersList.map((user) => user.userId).toList();
      await localSource.deleteChatFriends(usersId);
    }

    // Handle private data deletion for deleted users
    await localSource.syncDeleteUserPrivateData(deletedUsersList);

    return (
      updatedUser,
      updatedUser,
      deletedUsersList,
      blockedUsers,
      result.syncTime ?? TimeUtils.getCurrentUTCTimeFormatted(),
    );
  }

  /// Register notification subscription
  /// Returns true if registration was successful, false otherwise
  Future<bool> registerNotification({
    required String deviceToken,
    required String appId,
  }) async {
    try {
      logOperation(
        'Registering notification subscription',
        'deviceToken: ${deviceToken.substring(0, 10)}..., appId: $appId',
      );

      final success = await remoteSource.registerNotification(
        deviceToken: deviceToken,
        appId: appId,
      );

      if (success) {
        logOperation('Notification registration successful');
      } else {
        logOperation('Notification registration failed');
      }

      return success;
    } catch (e) {
      logOperation('Error registering notification', e.toString());
      return false;
    }
  }

//endregion
}
