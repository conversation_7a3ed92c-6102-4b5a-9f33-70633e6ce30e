import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data_router.dart';
import '../sources/local/user_local_data_source.dart';
import '../sources/remote/user_remote_data_source.dart';

class SyncVisitedProfileResult {
  final bool hasNew;
  final bool hasUpdated;
  final bool hasDeleted;

  const SyncVisitedProfileResult({
    this.hasNew = false,
    this.hasUpdated = false,
    this.hasDeleted = false,
  });

  bool get hasChanges => hasNew || hasUpdated || hasDeleted;
}

class VisitedProfileLoadResult {
  final List<VisitedProfile> visitedProfiles;
  final List<User> users;

  VisitedProfileLoadResult({
    required this.visitedProfiles,
    required this.users,
  });
}

/// VisitedProfile-specific data operation implementation
/// Provides reactive data access with automatic synchronization for VisitedProfile entities
///
/// This follows the Single Responsibility Principle by handling only VisitedProfile entities
/// and implements the local-first strategy with entity-specific optimizations
///
///
@Singleton()
class VisitedProfileDataOperation extends BaseDataOperation<VisitedProfile> {
  final VisitedProfileLocalDataSource localSource;
  final UserLocalDataSource userLocalSource;
  final UserRemoteDataSource userRemoteSource;
  final VisitedProfileRemoteDataSource remoteSource;
  final VisitedProfileWebSocketDataSource webSocketSource;

  VisitedProfileDataOperation(
    this.userLocalSource,
    this.userRemoteSource,
    this.localSource,
    this.remoteSource,
    this.webSocketSource,
  ) : super(
          localSource: localSource,
          remoteSource: remoteSource,
          webSocketSource: webSocketSource,
        );

  //region WebSocket Event Handling

  @override
  Future<void> handleRealtimeEvent(DataRouterCloudEvent event) async {
    try {
      logOperation('Handling realtime event', event.type.value);

      switch (event.type) {
        case EventType.VISITED_PROFILE:
          await _handleVisitedProfileEvent(event);
          break;
        case EventType.VISITED_PROFILE_DELETE:
          await _handleVisitedProfileDeleteEvent(event);
          break;
        case EventType.CLEAR_VISITED_PROFILE_NOTIFICATIONS:
          await _handleClearVisitedProfileNotificationsEvent(event);
          break;
        default:
          logOperation(
            'Unhandled visited profile event type',
            event.type.value,
          );
      }
    } catch (e) {
      logOperation('Error handling realtime event', '${event.type.value}: $e');
    }
  }

  /// Handle visited profile created event
  Future<void> _handleVisitedProfileEvent(DataRouterCloudEvent event) async {
    logOperation(
      'Processing visited profile event',
      event.id,
    );
    final visitedData = event.parse<UserVisitedProfileEventData>();
    if (visitedData?.userId == null || visitedData?.visitedUserData == null) {
      logOperation(
        'Invalid visited profile event - missing data',
        event.id,
      );
      return;
    }

    final visitedUserId = visitedData!.userData?.userId!;

    logOperation(
      'Processing visited profile event',
      'userId: $visitedUserId, visitorUserId: ${visitedData.userId}',
    );

    final localUserData = await userLocalSource.get(visitedUserId!);
    if (localUserData == null) {
      final remoteUserData = await userRemoteSource.load(visitedUserId);
      logOperation(
        'Get user from server to insert',
        'userId: $visitedUserId, user: ${remoteUserData?.toJson()}',
      );
      await userLocalSource.insert(remoteUserData!);
    }
    final visitedProfile = VisitedProfile.create(
      sessionKey: Config.getInstance().activeSessionKey ?? '',
      visitedUserId: visitedUserId,
      isRead: false,
      createTime: TimeUtils.parseUTCStringToDateTime(visitedData.createTime) ??
          DateTime.now(),
      updateTime: TimeUtils.parseUTCStringToDateTime(visitedData.updateTime) ??
          DateTime.now(),
    );

    localSource.insert(visitedProfile);
    AppEventBus.publish(HasNotificationEvent(hasNotification: true));

    logOperation('✅ Visited profile event processed', visitedUserId);
  }

  /// Handle visited profile updated event

  /// Handle visited profile deleted event
  Future<void> _handleVisitedProfileDeleteEvent(
    DataRouterCloudEvent event,
  ) async {
    final deleteData = event.parse<UserVisitedProfileDeletedEventData>();
    if (deleteData?.userId == null) {
      logOperation(
        'Invalid visited profile delete event - missing userId',
        event.id,
      );
      return;
    }

    final userId = deleteData!.userId!;

    logOperation('Processing visited profile delete event', userId);

    localSource.deleteByVisitedUserId(deleteData.userId!);

    logOperation('✅ Visited profile delete event processed', userId);
  }

  /// Handle visited profile cleared event (all marked as read)
  Future<void> _handleClearVisitedProfileNotificationsEvent(
    DataRouterCloudEvent event,
  ) async {
    logOperation(
      'Processing clear visited profile notifications event',
      event.id,
    );

    await remoteSource.clearNotifications();
    localSource.markAllAsRead();
    AppEventBus.publish(HasNotificationEvent(hasNotification: false));

    logOperation(
      '✅ Clear visited profile notifications event processed',
      event.id,
    );
  }

  //endregion

  //region Local Data Source Operations

  /// Get visited profile by visited user ID
  /// Returns visited profile for the current session
  Future<VisitedProfile?> getByVisitedUserId(String visitedUserId) async {
    try {
      logOperation('Getting visited profile by visited user ID', visitedUserId);
      return await localSource.getByVisitedUserId(visitedUserId);
    } catch (e) {
      logOperation(
        'Error getting visited profile by visited user ID',
        '$visitedUserId: $e',
      );
      return null;
    }
  }

  /// Get all visited profiles for current session
  /// Returns list of visited profiles ordered by update time
  Future<List<VisitedProfile>> getAllForCurrentSession() async {
    try {
      logOperation('Getting all visited profiles for current session');
      return await localSource.getAllForCurrentSession();
    } catch (e) {
      logOperation(
        'Error getting visited profiles for current session',
        e.toString(),
      );
      return [];
    }
  }

  /// Watch visited profiles for current session
  /// Returns stream of visited profiles updates
  Stream<List<VisitedProfile>> watchVisitedProfiles() {
    try {
      logOperation('Watching visited profiles for current session');
      return localSource.watchVisitedProfiles();
    } catch (e) {
      logOperation(
        'Failed to watch visited profiles: $e',
      );
      return Stream.error(Exception('Failed to watch visited profiles: $e'));
    }
  }

  /// Mark visited profile as read
  /// Updates the isRead flag and updateTime
  Future<Resource<bool>> markAsRead(String visitedUserId) async {
    try {
      logOperation('Marking visited profile as read', visitedUserId);

      final existing = await localSource.getByVisitedUserId(visitedUserId);
      if (existing == null) {
        logOperation(
          '❌ Visited profile not found for marking as read',
          visitedUserId,
        );
        return Resource.error('Visited profile not found');
      }

      existing.markAsRead();
      await localSource.insert(existing);

      logOperation('✅ Visited profile marked as read', visitedUserId);
      return Resource.success(true);
    } catch (e) {
      logOperation(
        '❌ Error marking visited profile as read',
        '$visitedUserId: $e',
      );
      return Resource.error('Failed to mark as read: $e');
    }
  }

  /// Mark all visited profiles as read
  /// Updates all visited profiles for current session
  Future<Resource<int>> markAllAsRead() async {
    try {
      logOperation('Marking all visited profiles as read');

      final count = await localSource.markAllAsRead();
      logOperation('✅ Marked $count visited profiles as read');

      return Resource.success(count);
    } catch (e) {
      logOperation(
        '❌ Error marking all visited profiles as read',
        e.toString(),
      );
      return Resource.error('Failed to mark all as read: $e');
    }
  }

  //endregion

  //region Remote Data Source Operations

  /// Load visited profiles from remote API
  /// Fetches visited profiles from server and saves to local storage
  Future<Resource<VisitedProfileLoadResult>> loadFromRemote() async {
    try {
      logOperation('📡 Loading visited profiles from remote');

      final remoteProfiles = await remoteSource.loadAll();

      List<User> resolvedUsers = [];

      if (remoteProfiles.isNotEmpty) {
        resolvedUsers = remoteProfiles
            .map((profile) => profile.visitedUser.target!)
            .toList();

        await userLocalSource.insertAll(resolvedUsers);

        // Save profiles locally
        await localSource.syncVisitedProfiles(remoteProfiles);

        logOperation(
          '✅ Saved ${remoteProfiles.length} visited profiles locally',
        );

        // 2. Extract visited user IDs

        Log.e(
          name: 'VisitedProfileDataOperation.loadFromRemote',
          resolvedUsers.length,
        );
      }

      return Resource.success(
        VisitedProfileLoadResult(
          visitedProfiles: remoteProfiles,
          users: resolvedUsers,
        ),
      );
    } catch (e) {
      logOperation(
        '❌ Error loading visited profiles from remote: $e',
      );
      return Resource.error('Failed to load from remote: $e');
    }
  }

  /// Clear visited profile notifications on server
  /// Marks all visited profiles as read on server and locally
  Future<Resource<bool>> clearNotifications() async {
    try {
      logOperation('Clearing visited profile notifications');

      // Clear on server first
      final serverResult = await remoteSource.clearNotifications();
      if (!serverResult) {
        logOperation('❌ Failed to clear notifications on server');
        return Resource.error('Failed to clear notifications on server');
      }

      // Mark all as read locally
      await localSource.markAllAsRead();

      logOperation('✅ Cleared visited profile notifications');
      return Resource.success(true);
    } catch (e) {
      logOperation(
        '❌ Error clearing visited profile notifications',
        e.toString(),
      );
      return Resource.error('Failed to clear notifications: $e');
    }
  }

  /// Delete visited profile by visited user ID
  /// Removes visited profile locally and optionally on server
  Future<Resource<bool>> deleteByVisitedUserId(String visitedUserId) async {
    try {
      logOperation('Deleting visited profile', visitedUserId);

      // Delete locally first (optimistic update)
      await localSource.deleteByVisitedUserId(visitedUserId);

      // Try to delete on server (best effort)
      try {
        await remoteSource.delete(visitedUserId);
        logOperation('✅ Deleted visited profile on server', visitedUserId);
      } catch (e) {
        logOperation(
          '⚠️ Failed to delete visited profile on server',
          '$visitedUserId: $e',
        );
        // Continue - local deletion is more important
      }

      logOperation('✅ Deleted visited profile', visitedUserId);
      return Resource.success(true);
    } catch (e) {
      logOperation('❌ Error deleting visited profile', '$visitedUserId: $e');
      return Resource.error('Failed to delete visited profile: $e');
    }
  }

  /// Create or update visited profile
  /// Records a visit to a user profile
  Future<Resource<VisitedProfile>> recordVisit(String visitedUserId) async {
    try {
      logOperation('Recording visit to user profile', visitedUserId);

      // Get current session key
      final sessionKey = Config.getInstance().activeSessionKey;
      if (sessionKey == null || sessionKey.isEmpty) {
        return Resource.error('No active session');
      }

      // Check if visited profile already exists
      final existing = await localSource.getByVisitedUserId(visitedUserId);

      VisitedProfile visitedProfile;
      if (existing != null) {
        // Update existing visit
        existing.updateVisitTime();
        existing.markAsUnread(); // New visit should be unread
        visitedProfile = existing;
      } else {
        // Create new visited profile
        visitedProfile = VisitedProfile.create(
          sessionKey: sessionKey,
          visitedUserId: visitedUserId,
          isRead: false,
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
        );
      }

      // Save locally
      // await localSource.insert(visitedProfile);

      // Try to record on server (best effort)
      try {
        await remoteSource.recordVisit(visitedUserId);
        logOperation('✅ Recorded visit on server', visitedUserId);
      } catch (e) {
        logOperation(
          '⚠️ Failed to record visit on server',
          '$visitedUserId: $e',
        );
        // Continue - local recording is more important
      }

      logOperation('✅ Recorded visit to user profile', visitedUserId);
      return Resource.success(visitedProfile);
    } catch (e) {
      logOperation('❌ Error recording visit', '$visitedUserId: $e');
      return Resource.error('Failed to record visit: $e');
    }
  }

  /// Delete all visited profiles by session key
  /// This is a VisitedProfile-specific operation for session cleanup
  Future<void> deleteAllBySessionKey(String sessionKey) async {
    try {
      logOperation('Deleting visited profiles by session key', sessionKey);
      await localSource.deleteAllBySessionKey(sessionKey);
    } catch (e) {
      logOperation(
        'Error deleting visited profiles by session key',
        '$sessionKey: $e',
      );
    }
  }

  Future<bool> syncVisitedProfiles() async {
    try {
      logOperation('🔄 Starting sync of visited profiles');

      final listRemote = await remoteSource.loadAll();
      final listLocal = await localSource.getAllForCurrentSession();

      // If there are new profiles from the API
      if (listRemote.length > listLocal.length) {
        AppEventBus.publish(HasNotificationEvent(hasNotification: true));
        logOperation('🆕 New profiles received from API');
        return true;
      }

      // If local has more profiles (some need to be deleted)
      if (listLocal.length > listRemote.length) {
        List<VisitedProfile> difference = listLocal.where((localVisited) {
          return !listRemote.any(
            (remote) => remote.visitedUserId == localVisited.visitedUserId,
          );
        }).toList();

        for (var item in difference) {
          await localSource.deleteByVisitedUserId(item.visitedUserId);
          logOperation(
            '🗑️ Deleted local profile no longer present on server',
            item.visitedUserId,
          );
        }
      }

      // Check for changed items (updateTime, isRead)
      final listChanged = listRemote.where((api) {
        return listLocal.any((local) {
          return api.visitedUserId == local.visitedUserId &&
              (api.updateTime!.isAfter(local.updateTime!) ||
                  api.isRead != local.isRead);
        });
      }).toList();

      if (listChanged.isNotEmpty) {
        AppEventBus.publish(HasNotificationEvent(hasNotification: true));
        logOperation(
          '✏️ Profiles updated on server',
          listChanged.length.toString(),
        );
        return true;
      }

      // No changes found
      AppEventBus.publish(HasNotificationEvent(hasNotification: false));
      logOperation('✅ No changes found in visited profiles');
      return false;
    } catch (e) {
      logOperation('❌ Failed to sync visited profiles: $e');
      return false; // or throw if you want to propagate the error
    }
  }

//endregion
}
