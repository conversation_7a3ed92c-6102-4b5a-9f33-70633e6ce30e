//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_ringback_tone_renamed_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3RingbackToneRenamedEventData {
  /// Returns a new [V3RingbackToneRenamedEventData] instance.
  V3RingbackToneRenamedEventData({
    this.ringbackToneId,
    this.name,
    this.updateTime,
  });

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'ringbackToneId',
    required: false,
    includeIfNull: false,
  )
  final String? ringbackToneId;

  @JsonKey(
    name: r'name',
    required: false,
    includeIfNull: false,
  )
  final String? name;

  @JsonKey(
    name: r'updateTime',
    required: false,
    includeIfNull: false,
  )
  final String? updateTime;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3RingbackToneRenamedEventData &&
          other.ringbackToneId == ringbackToneId &&
          other.name == name &&
          other.updateTime == updateTime;

  @override
  int get hashCode =>
      ringbackToneId.hashCode + name.hashCode + updateTime.hashCode;

  factory V3RingbackToneRenamedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3RingbackToneRenamedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3RingbackToneRenamedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
