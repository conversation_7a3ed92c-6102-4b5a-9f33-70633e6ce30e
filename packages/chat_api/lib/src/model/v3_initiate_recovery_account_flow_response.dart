//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_recovery_request.dart';
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_recovery_account_flow_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateRecoveryAccountFlowResponse {
  /// Returns a new [V3InitiateRecoveryAccountFlowResponse] instance.
  V3InitiateRecoveryAccountFlowResponse({
    this.ok,
    this.error,
    this.recoveryRequest,
  });

  @Json<PERSON>ey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @J<PERSON><PERSON><PERSON>(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @J<PERSON><PERSON><PERSON>(
    name: r'recoveryRequest',
    required: false,
    includeIfNull: false,
  )
  final V3RecoveryRequest? recoveryRequest;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateRecoveryAccountFlowResponse &&
          other.ok == ok &&
          other.error == error &&
          other.recoveryRequest == recoveryRequest;

  @override
  int get hashCode => ok.hashCode + error.hashCode + recoveryRequest.hashCode;

  factory V3InitiateRecoveryAccountFlowResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateRecoveryAccountFlowResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateRecoveryAccountFlowResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
