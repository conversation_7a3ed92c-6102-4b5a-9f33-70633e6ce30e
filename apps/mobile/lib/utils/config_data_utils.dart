import 'dart:io';

import 'package:apns_notification/apns_notification.dart';
import 'package:app_core/core.dart' as core;
import 'package:auth/auth.dart' as auth;
import 'package:call/call.dart' as call;
import 'package:chat/chat.dart' as chat;
import 'package:data_router/data_router.dart' as data;
import 'package:fcm_notification/fcm_notification.dart';
import 'package:search/search.dart' as search;
import 'package:share_to/share_to.dart' as shareTo;
import 'package:shared/shared.dart' as share;
import 'package:sticker/sticker.dart' as sticker;
import 'package:upload_manager/upload_manager.dart' as upload;
import 'package:user_manager/user_manager.dart' as user;

import '../common/di/di.dart';
import '../domain/handler/app_initializer.dart';

class ConfigDataUtils {
  static void clearConfigData() {
    // skip handling token invalid after logout or account deletion
    share.ValidTokenInterceptor.addTokenInvalidProcessed(
      chat.Config.getInstance().apiAuthToken,
    );

    chat.Config.getInstance().authData = null;
    user.Config.getInstance().authData = null;
    auth.Config.getInstance().authData = null;
    search.Config.getInstance().authData = null;
    sticker.Config.getInstance().authData = null;
    core.Config.getInstance().authData = null;
    shareTo.Config.getInstance().authData = null;
    upload.Config.getInstance().authData = null;
    call.Config.getInstance().authData = null;
    upload.Config.getInstance().authData = null;
    data.Config.getInstance().authData = null;

    getIt<shareTo.DeleteSuggestion>().deleteSuggestion();
    getIt<shareTo.ShareTo>().close();
    getIt<core.WebSocketManager>().disconnect();

    if (Platform.isIOS) {
      ApnsNotification()
        ..setBadgeNotification(0)
        ..clearAllNotification();
    } else {
      FCMNotification().clearAllNotification();
    }

    getIt<AppInitializer>().dispose();
  }
}
