import 'dart:async';
import 'dart:convert';

import 'package:injectable/injectable.dart' hide Order;
import 'package:rxdart/rxdart.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart';
import '../../core/cache/cache.dart';
import '../../core/metrics/metrics.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/friend_repository.dart';
import '../database/generated/objectbox.g.dart';
import '../events/private_data_events.dart';
import '../manager/relation_manager.dart';

/// Implementation of FriendRepository using SessionBox
/// Handles Friend entity operations with automatic session filtering and ObjectBox native watching
@LazySingleton(as: FriendRepository)
class FriendRepositoryImpl extends BaseRepository<Friend>
    implements FriendRepository {
  FriendRepositoryImpl(
    this._sessionBox,
    this._friendBox,
    this._userBox,
    this._relationManager,
    this._store,
  ) : super(_friendBox) {
    _relationManager.registerUserListener(_onUserCacheUpdated);

    // [RACE CONDITION FIX] Listen for private data sync complete events
    _privateDataSyncSubscription = PrivateDataEventBus()
        .onPrivateDataSyncComplete
        .listen(_onPrivateDataSyncComplete);
  }

  final SessionBox<Session> _sessionBox;
  final SessionBox<Friend> _friendBox;
  final SessionBox<User> _userBox;
  final Store _store;

  final Map<String, User> _userCache = {};

  StreamSubscription<List<User>>? _userDataSubscription;
  final RelationManager _relationManager;
  StreamSubscription<PrivateDataSyncCompleteEvent>?
      _privateDataSyncSubscription;

  Session? _currentActiveSession;

  // [OPTIMIZATION] Debounce timer to prevent duplicate processing
  Timer? _updateDebounceTimer;
  final Set<String> _pendingUserIds = {};

  // Enhanced Cache Management with timestamp validation
  final EnhancedCacheManager<Friend> _friendCacheManager =
      EnhancedCacheManager<Friend>(1000);
  final CacheManager<List<Friend>> _friendListCacheManager =
      CacheManager<List<Friend>>(100);

  // Performance tracking
  final FriendRepositoryMetrics _metrics = FriendRepositoryMetrics();
  DateTime _lastPerformanceLog = DateTime.now();
  static const Duration _performanceLogInterval = Duration(minutes: 5);

  /// [RACE CONDITION FIX] Merged handler for both user cache updates and private data sync
  /// Uses debouncing to prevent duplicate processing
  void _onUserCacheUpdated(
    String sessionKey,
    CacheUpdate<User> event,
  ) {
    final updatedUserIds = event.updated.keys.toList();
    if (updatedUserIds.isEmpty) return;

    // Add to pending updates and debounce
    _pendingUserIds.addAll(updatedUserIds);
    _debounceUserUpdate(event.updated);
  }

  /// [RACE CONDITION FIX] Handle private data sync complete event
  /// This triggers relationship linking after UserPrivateData is available
  void _onPrivateDataSyncComplete(PrivateDataSyncCompleteEvent event) {
    try {
      // Add to pending updates and debounce
      _pendingUserIds.addAll(event.userIds);
      _debounceUserUpdate({});
    } catch (e) {
      Log.e('[FriendRepositoryImpl] Error in _onPrivateDataSyncComplete: $e');
    }
  }

  /// [OPTIMIZATION] Debounced update to prevent race conditions
  void _debounceUserUpdate(Map<String, User> updatedUsers) {
    _updateDebounceTimer?.cancel();
    _updateDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      _processPendingUserUpdates(updatedUsers);
    });
  }

  /// [OPTIMIZATION] Process all pending user updates in a single transaction
  void _processPendingUserUpdates(Map<String, User> updatedUsers) {
    if (_pendingUserIds.isEmpty) return;

    final userIdsToProcess = List<String>.from(_pendingUserIds);
    _pendingUserIds.clear();

    // [OPTIMIZATION] Single batch query instead of N+1 queries
    final friendsToUpdate = _friendBox.query(
      Friend_.participantId.oneOf(userIdsToProcess),
    );

    _store.runInTransaction(TxMode.write, () {
      _trackTransaction();
      final friendsNeedingUpdate = <Friend>[];

      for (final friend in friendsToUpdate) {
        final updatedUser = updatedUsers[friend.participantId] ??
            _userCache[friend.participantId];
        if (updatedUser == null) continue;
        friend.friendUser.target = updatedUser;
        friendsNeedingUpdate.add(friend);
      }

      // [OPTIMIZATION] Batch update instead of individual puts
      if (friendsNeedingUpdate.isNotEmpty) {
        _friendBox.putMany(friendsNeedingUpdate);
        // Update cache for modified friends
        for (final friend in friendsNeedingUpdate) {
          _updateFriendCache(friend.friendId, friend);
        }
      }
    });
  }

  // Enhanced cache management methods
  /// Update friend cache with timestamp validation
  void _updateFriendCache(String friendId, Friend friend) {
    final cached = _friendCacheManager.putIfNewer(friendId, friend);
    if (!cached) {
      // Log when we skip caching due to stale data
      _metrics.recordCacheSkip('friend', friendId);
    }
  }

  /// Update friend list cache
  void _updateFriendListCache(String cacheKey, List<Friend> friends) {
    _friendListCacheManager.put(cacheKey, friends);
  }

  /// Remove friend from cache
  void _removeFromFriendCache(String friendId) {
    _friendCacheManager.remove(friendId);
  }

  /// Check if cached friend is newer than database version
  bool _isCachedFriendNewer(String friendId, Friend dbFriend) {
    final cachedFriend = _friendCacheManager.get(friendId);
    if (cachedFriend == null) return false;

    // Compare update times - handle null safety
    final cachedUpdateTime = cachedFriend.updateTime;
    final dbUpdateTime = dbFriend.updateTime;

    if (cachedUpdateTime == null || dbUpdateTime == null) {
      return false; // If either is null, consider cached version not newer
    }

    return cachedUpdateTime.isAfter(dbUpdateTime) ||
        cachedUpdateTime.isAtSameMomentAs(dbUpdateTime);
  }

  void _trackTransaction() {
    _metrics.transactionCount++;
  }

  void _logPerformanceMetrics() {
    final now = DateTime.now();
    if (now.difference(_lastPerformanceLog) >= _performanceLogInterval) {
      _metrics.logMetrics();
      _lastPerformanceLog = now;
    }
  }

  void initializeRelationshipsWatching() {
    _sessionBox.watch(Session_.active.equals(true)).listen((event) {
      final activeSession = event.findFirst();

      if (_shouldInitRelationSubscription(activeSession)) {
        _currentActiveSession = activeSession;
        _initUserDataRelation(activeSession!.sessionKey);
        return;
      }

      if (_shouldCleanRelationSubscription(activeSession)) {
        _disposeDataSubscriptions();
        _userCache.clear();
        _currentActiveSession = null;
      }
    });
  }

  bool _shouldInitRelationSubscription(Session? activeSession) =>
      activeSession != null && _currentActiveSession == null;

  bool _shouldCleanRelationSubscription(Session? activeSession) =>
      activeSession == null && _currentActiveSession != null;

  void _initUserDataRelation(String sessionKey) {
    _userDataSubscription = _userBox
        .watch(User_.sessionKey.equals(sessionKey))
        .map((query) => query.find())
        .listen((users) {
      final userIds = users.map((u) => u.userId).toList();
      if (userIds.isEmpty) return;

      for (final user in users) {
        _userCache[user.userId] = user;
      }

      final friendsToUpdate =
          _friendBox.query(Friend_.participantId.oneOf(userIds));

      _store.runInTransaction(TxMode.write, () {
        for (final friend in friendsToUpdate) {
          final cachedUser = _userCache[friend.participantId];

          if (cachedUser != null) {
            if (friend.friendUser.target == null) {
              friend.friendUser.target = cachedUser;
              _friendBox.put(friend);
              return;
            }

            friend.friendUser.target = cachedUser;
            _friendBox.put(friend);
          }
        }

        final friendUpdatedUserIds =
            friendsToUpdate.map((friend) => friend.participantId);
        final userNeedInsertFriends = _userCache.values.where(
          (user) =>
              !StringUtils.isNullOrEmpty(user.chatFriendDataRaw) &&
              !friendUpdatedUserIds.contains(user.userId),
        );

        for (final user in userNeedInsertFriends) {
          final json = jsonDecode(user.chatFriendDataRaw!);
          if (json['friendId'] == null) {
            continue;
          }
          final friend = FriendSerializer.fromV3Friend(
            V3Friend.fromJson(json),
            sessionKey: Config.getInstance().activeSessionKey,
          )!;
          friend.friendUser.target = user;
          _friendBox.put(friend);
        }
      });
    });
  }

  void _disposeDataSubscriptions() {
    _userDataSubscription?.cancel();
    _userDataSubscription = null;
    _privateDataSyncSubscription?.cancel();
    _privateDataSyncSubscription = null;
  }

  User? getUserCached(String id) => _userCache[id];

  void _updateUsers(List<Friend> friends) {
    _store.runInTransaction(TxMode.write, () {
      final friendsMap = {
        for (final friend in friends) friend.participantId: friend,
      };
      final usersToUpdate =
          _userBox.query(User_.userId.oneOf(friendsMap.keys.toList()));
      for (final user in usersToUpdate) {
        user.chatFriendDataRaw = jsonEncode(friendsMap[user.userId]);
      }
      _userBox.putMany(usersToUpdate);
    });
  }

  @override
  int insert(Friend friend) {
    try {
      if (friend.id != 0) {
        if (friend.friendUser.target != null) {
          friend.friendUser.target = _userCache[friend.participantId];
        }
        final id = _friendBox.put(friend);
        // [OPTIMIZATION] Update cache after successful insert
        if (id > 0) {
          _updateFriendCache(friend.friendId, friend);
          // Invalidate list caches as they may be outdated
          _friendListCacheManager.clear();
        }
        return id;
      }

      final existingFriend = getFirst(Friend_.friendId.equals(friend.friendId));
      if (existingFriend != null) {
        friend.id = existingFriend.id;
        friend.friendUser.target = existingFriend.friendUser.target;
      }

      final id = _friendBox.put(friend);
      _updateUsers([friend]);

      // [OPTIMIZATION] Update cache after successful insert
      if (id > 0) {
        _updateFriendCache(friend.friendId, friend);
        // Invalidate list caches as they may be outdated
        _friendListCacheManager.clear();
      }

      return id;
    } catch (e) {
      throw Exception('Failed to insert friend ${friend.friendId}: $e');
    }
  }

  @override
  List<int> insertAll(List<Friend> friends) {
    try {
      if (friends.isEmpty) return [];

      final friendIdsToCheck = friends.map((f) => f.friendId).toList();

      final existingFriendsMap =
          _getExistingFriendsByFriendIds(friendIdsToCheck);

      for (final friend in friends) {
        final existingFriend = existingFriendsMap[friend.friendId];
        if (existingFriend != null) {
          friend.id = existingFriend.id;
          friend.friendUser.target = existingFriend.friendUser.target;
        } else if (friend.friendUser.target == null &&
            friend.friendUser.targetId > 0) {
          final user = _userBox.get(friend.friendUser.targetId);
          if (user != null) {
            friend.friendUser.target = user;
          }
        }
      }

      final ids = _friendBox.putMany(friends);
      _updateUsers(friends);

      // [OPTIMIZATION] Update cache after successful batch insert
      if (ids.isNotEmpty) {
        for (final friend in friends) {
          _updateFriendCache(friend.friendId, friend);
        }
        // Invalidate list caches as they may be outdated
        _friendListCacheManager.clear();
      }

      return ids;
    } catch (e) {
      throw Exception('Failed to insert friends: $e');
    }
  }

  /// [OPTIMIZATION] Efficient batch query for existing friends
  Map<String, Friend> _getExistingFriendsByFriendIds(List<String> friendIds) {
    if (friendIds.isEmpty) return {};

    try {
      // [OPTIMIZATION] Single batch query using oneOf() instead of N+1 queries
      final query =
          _friendBox.queryWith(Friend_.friendId.oneOf(friendIds)).build();
      final existingFriends = query.find();
      query.close();

      // [OPTIMIZATION] Use Map.fromEntries for better performance
      return Map.fromEntries(
        existingFriends.map((f) => MapEntry(f.friendId, f)),
      );
    } catch (e) {
      Log.e('[FriendRepositoryImpl] Error getting existing friends: $e');
      // [OPTIMIZATION] Even fallback uses batch query when possible
      return _getFallbackExistingFriends(friendIds);
    }
  }

  /// [OPTIMIZATION] Fallback method that still tries to minimize queries
  Map<String, Friend> _getFallbackExistingFriends(List<String> friendIds) {
    final Map<String, Friend> fallbackMap = {};

    // Process in smaller batches to avoid overwhelming the database
    const batchSize = 50;
    for (int i = 0; i < friendIds.length; i += batchSize) {
      final batch = friendIds.skip(i).take(batchSize).toList();
      try {
        final query =
            _friendBox.queryWith(Friend_.friendId.oneOf(batch)).build();
        final batchResults = query.find();
        query.close();

        for (final friend in batchResults) {
          fallbackMap[friend.friendId] = friend;
        }
      } catch (e) {
        // If batch fails, fall back to individual queries for this batch only
        for (final friendId in batch) {
          try {
            final friend = _friendBox
                .queryWith(Friend_.friendId.equals(friendId))
                .build()
                .findFirst();
            if (friend != null) fallbackMap[friendId] = friend;
          } catch (_) {
            // Skip individual failures
          }
        }
      }
    }
    return fallbackMap;
  }

  @override
  Friend? getFriend(String friendId) {
    try {
      final stopwatch = Stopwatch()..start();

      // Check cache first
      final cachedFriend = _friendCacheManager.get(friendId);
      if (cachedFriend != null) {
        // Verify cache freshness by checking database
        final dbFriend = getFirst(Friend_.friendId.equals(friendId));
        if (dbFriend != null) {
          // If cached version is newer or same, return cached
          if (_isCachedFriendNewer(friendId, dbFriend)) {
            _metrics.cacheHits++;
            stopwatch.stop();
            _metrics.recordOperation('getFriend_cache_hit', stopwatch.elapsed);
            _logPerformanceMetrics();
            return cachedFriend;
          } else {
            // Database has newer version, update cache and return db version
            _updateFriendCache(friendId, dbFriend);
            stopwatch.stop();
            _metrics.recordOperation(
              'getFriend_cache_refresh',
              stopwatch.elapsed,
            );
            _logPerformanceMetrics();
            return dbFriend;
          }
        } else {
          // Friend no longer exists in database, remove from cache
          _removeFromFriendCache(friendId);
          stopwatch.stop();
          _metrics.recordOperation(
            'getFriend_cache_invalidate',
            stopwatch.elapsed,
          );
          return null;
        }
      }

      // Cache miss - query database
      _metrics.cacheMisses++;
      final friend = getFirst(Friend_.friendId.equals(friendId));

      // Update cache if friend found
      if (friend != null) {
        _updateFriendCache(friendId, friend);
      }

      stopwatch.stop();
      _metrics.recordOperation('getFriend_cache_miss', stopwatch.elapsed);
      _logPerformanceMetrics();
      return friend;
    } catch (e) {
      Log.e('Error getting friend $friendId: $e');
      return null;
    }
  }

  @override
  Friend? getFriendRequest(String friendId) {
    try {
      return getFirst(Friend_.requestedFromUserId.equals(friendId));
    } catch (e) {
      Log.e('Error getting friend $friendId: $e');
      return null;
    }
  }

  @override
  Future<Friend?> getFriendAsync(String friendId) async {
    return getFriend(friendId);
  }

  @override
  List<Friend> getFriends() {
    try {
      const cacheKey = 'all_friends';

      // [OPTIMIZATION] Check cache first
      final cachedFriends = _friendListCacheManager.get(cacheKey);
      if (cachedFriends != null) {
        _metrics.cacheHits++;
        return cachedFriends;
      }

      // [OPTIMIZATION] Cache miss - query database
      _metrics.cacheMisses++;
      final friends = getAll();

      // [OPTIMIZATION] Update cache
      _updateFriendListCache(cacheKey, friends);

      return friends;
    } catch (e) {
      Log.e('Error getting all friends: $e');
      return [];
    }
  }

  @override
  Future<List<Friend>> getFriendsAsync() async {
    return getFriends();
  }

  @override
  List<Friend> getFriendsBySession(String sessionKey) {
    try {
      // For cross-session access, use the underlying box
      final query = _friendBox
          .queryWith(Friend_.sessionKey.equals(sessionKey))
          .order(Friend_.updateTime, flags: Order.descending)
          .build();
      final friends = query.find();
      query.close();
      return friends;
    } catch (e) {
      Log.e('Error getting friends for session $sessionKey: $e');
      return [];
    }
  }

  @override
  List<Friend> getActiveFriendsBySession() {
    try {
      // For cross-session access, use the underlying box
      final query = _friendBox
          .queryWith(
            Friend_.statusValue.equals(FriendStatusEnum.friend.value),
          )
          .order(Friend_.updateTime, flags: Order.descending)
          .build();
      final friends = query.find();
      query.close();
      return friends;
    } catch (e) {
      Log.e('Error getting active friends for session: $e');
      return [];
    }
  }

  @override
  List<Friend> getPendingRequestsBySession(String sessionKey) {
    try {
      // For cross-session access, use the underlying box
      final query = _friendBox
          .queryWith(
            Friend_.sessionKey.equals(sessionKey) &
                (Friend_.statusValue
                        .equals(FriendStatusEnum.requestSent.value) |
                    Friend_.statusValue
                        .equals(FriendStatusEnum.requestReceived.value)),
          )
          .order(Friend_.updateTime, flags: Order.descending)
          .build();
      final friends = query.find();
      query.close();
      return friends;
    } catch (e) {
      Log.e('Error getting pending requests for session $sessionKey: $e');
      return [];
    }
  }

  @override
  List<Friend> getIncomingRequestsBySession(String sessionKey) {
    try {
      // For cross-session access, use the underlying box
      final query = _friendBox
          .queryWith(
            Friend_.sessionKey.equals(sessionKey) &
                Friend_.statusValue
                    .equals(FriendStatusEnum.requestReceived.value),
          )
          .order(Friend_.updateTime, flags: Order.descending)
          .build();
      final friends = query.find();
      query.close();
      return friends;
    } catch (e) {
      Log.e('Error getting incoming requests for session $sessionKey: $e');
      return [];
    }
  }

  @override
  Friend? getFriendByParticipants(
    String sessionKey,
    String userId1,
    String userId2,
  ) {
    try {
      final query = _friendBox
          .queryWith(
            Friend_.participantIds.containsElement(userId1).and(
                  Friend_.participantIds.containsElement(userId2),
                ),
          )
          .build();

      final friend = query.findFirst();
      query.close();
      return friend;
    } catch (e) {
      return null;
    }
  }

  @override
  Stream<List<Friend>> getFriendsStream() {
    return _friendBox
        .queryWith(Friend_.statusValue.equals(FriendStatusEnum.friend.value))
        .watch()
        .map((query) => query.find())
        .distinct() // [OPTIMIZATION] Prevent duplicate events
        .handleError((error) {
      Log.e('[FriendRepository] getFriendsStream error: $error');
      return <Friend>[];
    });
  }

  @override
  Stream<Friend?> getFriendStream(String friendId) {
    // Use ObjectBox native watch for specific friend
    return watch(Friend_.friendId.equals(friendId))
        .map((query) => query.findFirst())
        .distinct() // [OPTIMIZATION] Prevent duplicate events
        .handleError((error) {
      Log.e('[FriendRepository] getFriendStream error for $friendId: $error');
      return null;
    });
  }

  @override
  Stream<List<Friend>> getFriendsBySessionStream(String sessionKey) {
    // For cross-session watching, use the underlying box
    return _friendBox
        .watch(Friend_.sessionKey.equals(sessionKey))
        .map((query) => query.find());
  }

  @override
  bool deleteFriend(String friendId) {
    try {
      final friend = getFirst(Friend_.friendId.equals(friendId));
      if (friend != null) {
        // ObjectBox will automatically notify watchers
        final success = remove(friend.id);

        // [OPTIMIZATION] Remove from cache after successful delete
        if (success) {
          _removeFromFriendCache(friendId);
          // Invalidate list caches as they may be outdated
          _friendListCacheManager.clear();
        }

        return success;
      }
      return false;
    } catch (e) {
      Log.e('Error deleting friend $friendId: $e');
      return false;
    }
  }

  @override
  int deleteFriendsBySession(String sessionKey) {
    try {
      // For cross-session delete operations, use the underlying box
      final query =
          _friendBox.box.query(Friend_.sessionKey.equals(sessionKey)).build();

      // ObjectBox will automatically notify watchers
      final deletedCount = query.remove();
      query.close();

      return deletedCount;
    } catch (e) {
      Log.e('Error deleting friends for session $sessionKey: $e');
      return 0;
    }
  }

  @override
  int deleteAllFriends() {
    try {
      // ObjectBox will automatically notify watchers
      final deletedCount = removeAll();

      // [OPTIMIZATION] Clear all caches after successful delete
      if (deletedCount > 0) {
        _friendCacheManager.clear();
        _friendListCacheManager.clear();
      }

      return deletedCount;
    } catch (e) {
      Log.e('Error deleting all friends: $e');
      return 0;
    }
  }

  @override
  bool friendExists(String friendId) {
    return getFirst(Friend_.friendId.equals(friendId)) != null;
  }

  @override
  bool friendshipExists(String sessionKey, String userId1, String userId2) {
    return getFriendByParticipants(sessionKey, userId1, userId2) != null;
  }

  @override
  int getFriendCount() {
    return count();
  }

  @override
  int getFriendCountBySession(String sessionKey) {
    // For cross-session access, use the underlying box
    final query =
        _friendBox.queryWith(Friend_.sessionKey.equals(sessionKey)).build();
    final count = query.count();
    query.close();
    return count;
  }

  @override
  Stream<List<Friend>> watchFriendsRequestStream() {
    // Use ObjectBox native watch for all friends in current session
    return _friendBox
        .queryWith(
          Friend_.statusValue.equals(FriendStatusEnum.requestReceived.value),
        )
        .order(Friend_.createTime, flags: Order.descending)
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  List<Friend> getAllFriendRequests() {
    return _friendBox.query(
      Friend_.statusValue.equals(FriendStatusEnum.requestReceived.value),
    );
  }
}
