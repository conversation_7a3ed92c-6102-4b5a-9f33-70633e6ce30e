import '../../core/models/base_model.dart';

/// Enhanced cache manager with timestamp-based validation and LRU eviction
class EnhancedCacheManager<T extends BaseModel> {
  final Map<String, _CacheEntry<T>> _cache = {};
  final List<String> _lruOrder = [];
  final int maxSize;

  EnhancedCacheManager(this.maxSize);

  /// Get cached item if it exists and is not stale
  T? get(String key) {
    final entry = _cache[key];
    if (entry != null) {
      _updateLRU(key);
      return entry.value;
    }
    return null;
  }

  /// Check if cached item exists and is newer than the provided entity
  bool isNewerThan(String key, T entity) {
    final entry = _cache[key];
    if (entry == null) return false;

    // Compare by updateTime first, then by id if updateTime is same
    final cachedUpdateTime = entry.value.updateTime;
    final entityUpdateTime = entity.updateTime;

    if (cachedUpdateTime == null || entityUpdateTime == null) {
      // If either doesn't have updateTime, compare by id
      return entry.value.id >= entity.id;
    }

    final timeDiff =
        cachedUpdateTime.toUtc().compareTo(entityUpdateTime.toUtc());
    if (timeDiff != 0) {
      return timeDiff > 0; // Cached is newer if updateTime is later
    }

    // If updateTime is same, compare by id (higher id = newer)
    return entry.value.id >= entity.id;
  }

  /// Put item in cache only if it's newer than existing cached version
  bool putIfNewer(String key, T value) {
    final existingEntry = _cache[key];

    // If no existing entry, always cache
    if (existingEntry == null) {
      _putInternal(key, value);
      return true;
    }

    // Only cache if the new value is newer
    if (_isNewer(value, existingEntry.value)) {
      _putInternal(key, value);
      return true;
    }

    return false; // Not cached because existing is newer
  }

  /// Force put item in cache (bypass freshness check)
  void put(String key, T value) {
    _putInternal(key, value);
  }

  void _putInternal(String key, T value) {
    _updateLRU(key);
    _cache[key] = _CacheEntry(value, DateTime.now());
    _evictIfNeeded();
  }

  /// Check if first entity is newer than second entity
  bool _isNewer(T first, T second) {
    final firstUpdateTime = first.updateTime;
    final secondUpdateTime = second.updateTime;

    if (firstUpdateTime == null || secondUpdateTime == null) {
      // If either doesn't have updateTime, compare by id
      return first.id > second.id;
    }

    final timeDiff =
        firstUpdateTime.toUtc().compareTo(secondUpdateTime.toUtc());
    if (timeDiff != 0) {
      return timeDiff > 0; // First is newer if updateTime is later
    }

    // If updateTime is same, compare by id (higher id = newer)
    return first.id > second.id;
  }

  void remove(String key) {
    _lruOrder.remove(key);
    _cache.remove(key);
  }

  void clear() {
    _cache.clear();
    _lruOrder.clear();
  }

  int get length => _cache.length;

  /// Get cache statistics
  Map<String, dynamic> getStats() {
    final now = DateTime.now();
    var totalAge = Duration.zero;
    var oldestEntry = DateTime.now();
    var newestEntry = DateTime.fromMillisecondsSinceEpoch(0);

    for (final entry in _cache.values) {
      final age = now.difference(entry.cachedAt);
      totalAge += age;

      if (entry.cachedAt.isBefore(oldestEntry)) {
        oldestEntry = entry.cachedAt;
      }
      if (entry.cachedAt.isAfter(newestEntry)) {
        newestEntry = entry.cachedAt;
      }
    }

    return {
      'size': _cache.length,
      'maxSize': maxSize,
      'averageAge':
          _cache.isNotEmpty ? totalAge.inMilliseconds / _cache.length : 0,
      'oldestEntry': oldestEntry,
      'newestEntry': newestEntry,
    };
  }

  void _updateLRU(String key) {
    _lruOrder.remove(key);
    _lruOrder.add(key);
  }

  void _evictIfNeeded() {
    while (_cache.length > maxSize) {
      final oldestKey = _lruOrder.removeAt(0);
      _cache.remove(oldestKey);
    }
  }
}

/// Cache entry wrapper with metadata
class _CacheEntry<T> {
  final T value;
  final DateTime cachedAt;

  _CacheEntry(this.value, this.cachedAt);
}
