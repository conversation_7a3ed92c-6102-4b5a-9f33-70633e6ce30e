//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_user_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_user_status_updated_event_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UserStatusUpdatedEventData {
  /// Returns a new [V3UserStatusUpdatedEventData] instance.
  V3UserStatusUpdatedEventData({
    this.userId,
    this.statusData,
  });

  @JsonKey(
    name: r'userId',
    required: false,
    includeIfNull: false,
  )
  final String? userId;

  @JsonKey(
    name: r'statusData',
    required: false,
    includeIfNull: false,
  )
  final V3UserStatus? statusData;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UserStatusUpdatedEventData &&
          other.userId == userId &&
          other.statusData == statusData;

  @override
  int get hashCode => userId.hashCode + statusData.hashCode;

  factory V3UserStatusUpdatedEventData.fromJson(Map<String, dynamic> json) =>
      _$V3UserStatusUpdatedEventDataFromJson(json);

  Map<String, dynamic> toJson() => _$V3UserStatusUpdatedEventDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
