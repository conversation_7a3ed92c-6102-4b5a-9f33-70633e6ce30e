//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/initiate_file_collection_response_storage_space.dart';
import 'package:chat_api/src/model/v3_message_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_file_collection_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateFileCollectionResponseData {
  /// Returns a new [V3InitiateFileCollectionResponseData] instance.
  V3InitiateFileCollectionResponseData({
    this.collectionId,
    this.storageSpace,
    this.message,
  });

  @JsonKey(
    name: r'collectionId',
    required: false,
    includeIfNull: false,
  )
  final String? collectionId;

  @<PERSON><PERSON><PERSON><PERSON>(
    name: r'storageSpace',
    required: false,
    includeIfNull: false,
  )
  final InitiateFileCollectionResponseStorageSpace? storageSpace;

  @J<PERSON><PERSON>ey(
    name: r'message',
    required: false,
    includeIfNull: false,
  )
  final V3MessageData? message;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateFileCollectionResponseData &&
          other.collectionId == collectionId &&
          other.storageSpace == storageSpace &&
          other.message == message;

  @override
  int get hashCode =>
      collectionId.hashCode + storageSpace.hashCode + message.hashCode;

  factory V3InitiateFileCollectionResponseData.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateFileCollectionResponseDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateFileCollectionResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
