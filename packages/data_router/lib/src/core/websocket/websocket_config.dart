/// Configuration class for WebSocket functionality
/// Centralizes all WebSocket-related configuration parameters
class WebSocketConfig {
  /// Connection timeout duration
  /// Maximum time to wait for WebSocket connection establishment
  static Duration connectTimeout = const Duration(seconds: 30);

  /// Ping interval for keeping connection alive
  /// How often to send ping frames to maintain connection
  static Duration pingInterval = const Duration(seconds: 30);

  /// Base reconnection interval
  /// Initial delay before attempting reconnection (exponential backoff applies)
  static Duration reconnectInterval = const Duration(seconds: 5);

  /// Maximum reconnection attempts
  /// After this many failed attempts, reconnection stops
  static int maxReconnectAttempts = 10;

  /// Maximum message queue size
  /// Prevents memory issues by limiting queued messages
  static int maxQueueSize = 100;

  /// Circuit breaker failure threshold
  /// Number of consecutive failures before opening circuit
  static int circuitBreakerFailureThreshold = 5;

  /// Circuit breaker open duration before half-open state
  /// Time to wait before attempting to close circuit
  static Duration circuitBreakerOpenDuration = const Duration(minutes: 5);

  /// Health check interval when max attempts reached
  /// How often to perform health checks when circuit is open
  static Duration healthCheckInterval = const Duration(seconds: 30);

  /// Health check timeout
  /// Maximum time to wait for health check response
  static Duration healthCheckTimeout = const Duration(seconds: 5);

  /// Maximum exponential backoff delay
  /// Upper limit for reconnection delay
  static Duration maxBackoffDelay = const Duration(minutes: 5);

  /// Network stability count for circuit breaker reset
  /// Number of successful connections needed to reset circuit breaker
  static int networkStabilityThreshold = 3;

  /// Logging configuration
  static bool logConnectionError = false;
  static bool enableDebugLogging = false;
  static bool enablePerformanceLogging = false;

  /// Network monitoring configuration
  static bool enableNetworkMonitoring = true;
  static Duration networkCheckInterval = const Duration(seconds: 10);

  /// Message processing configuration
  static bool enableMessageBatching = false;
  static int messageBatchSize = 10;
  static Duration messageBatchTimeout = const Duration(milliseconds: 100);

  /// Performance thresholds
  static Duration maxMessageLatency = const Duration(milliseconds: 1000);
  static int maxConcurrentConnections = 1;

  /// Security configuration
  static bool validateMessageFormat = true;
  static bool enableMessageEncryption = false;

  /// Development/testing configuration
  static bool enableMockMode = false;
  static String? mockServerUrl;

  /// Reset all configuration to defaults
  static void resetToDefaults() {
    connectTimeout = const Duration(seconds: 30);
    pingInterval = const Duration(seconds: 20);
    reconnectInterval = const Duration(seconds: 5);
    maxReconnectAttempts = 10;
    maxQueueSize = 100;
    circuitBreakerFailureThreshold = 5;
    circuitBreakerOpenDuration = const Duration(minutes: 5);
    healthCheckInterval = const Duration(seconds: 30);
    healthCheckTimeout = const Duration(seconds: 5);
    maxBackoffDelay = const Duration(minutes: 5);
    networkStabilityThreshold = 3;
    logConnectionError = false;
    enableDebugLogging = false;
    enablePerformanceLogging = false;
    enableNetworkMonitoring = true;
    networkCheckInterval = const Duration(seconds: 10);
    enableMessageBatching = false;
    messageBatchSize = 10;
    messageBatchTimeout = const Duration(milliseconds: 100);
    maxMessageLatency = const Duration(milliseconds: 1000);
    maxConcurrentConnections = 1;
    validateMessageFormat = true;
    enableMessageEncryption = false;
    enableMockMode = false;
    mockServerUrl = null;
  }

  /// Load configuration from environment or other sources
  static void loadFromEnvironment() {
    // This can be implemented to load configuration from environment variables
    // or other configuration sources in the future
  }

  /// Validate current configuration
  static bool validateConfiguration() {
    if (connectTimeout.inMilliseconds <= 0) return false;
    if (pingInterval.inMilliseconds <= 0) return false;
    if (reconnectInterval.inMilliseconds <= 0) return false;
    if (maxReconnectAttempts <= 0) return false;
    if (maxQueueSize <= 0) return false;
    if (messageBatchSize <= 0) return false;
    if (maxConcurrentConnections <= 0) return false;
    if (circuitBreakerFailureThreshold <= 0) return false;
    if (circuitBreakerOpenDuration.inMilliseconds <= 0) return false;
    if (healthCheckInterval.inMilliseconds <= 0) return false;
    if (healthCheckTimeout.inMilliseconds <= 0) return false;
    if (maxBackoffDelay.inMilliseconds <= 0) return false;
    if (networkStabilityThreshold <= 0) return false;

    return true;
  }

  /// Get configuration summary for debugging
  static Map<String, dynamic> getConfigurationSummary() {
    return {
      'connectTimeout': connectTimeout.inMilliseconds,
      'pingInterval': pingInterval.inMilliseconds,
      'reconnectInterval': reconnectInterval.inMilliseconds,
      'maxReconnectAttempts': maxReconnectAttempts,
      'maxQueueSize': maxQueueSize,
      'circuitBreakerFailureThreshold': circuitBreakerFailureThreshold,
      'circuitBreakerOpenDuration': circuitBreakerOpenDuration.inMilliseconds,
      'healthCheckInterval': healthCheckInterval.inMilliseconds,
      'healthCheckTimeout': healthCheckTimeout.inMilliseconds,
      'maxBackoffDelay': maxBackoffDelay.inMilliseconds,
      'networkStabilityThreshold': networkStabilityThreshold,
      'enableDebugLogging': enableDebugLogging,
      'enableNetworkMonitoring': enableNetworkMonitoring,
      'enableMessageBatching': enableMessageBatching,
      'enableMockMode': enableMockMode,
    };
  }
}
