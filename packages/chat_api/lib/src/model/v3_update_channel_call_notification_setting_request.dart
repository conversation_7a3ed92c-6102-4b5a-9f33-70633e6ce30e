//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:json_annotation/json_annotation.dart';

part 'v3_update_channel_call_notification_setting_request.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3UpdateChannelCallNotificationSettingRequest {
  /// Returns a new [V3UpdateChannelCallNotificationSettingRequest] instance.
  V3UpdateChannelCallNotificationSettingRequest({
    this.status,
  });

  @JsonKey(
    name: r'status',
    required: false,
    includeIfNull: false,
  )
  final bool? status;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3UpdateChannelCallNotificationSettingRequest &&
          other.status == status;

  @override
  int get hashCode => status.hashCode;

  factory V3UpdateChannelCallNotificationSettingRequest.fromJson(
          Map<String, dynamic> json) =>
      _$V3UpdateChannelCallNotificationSettingRequestFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3UpdateChannelCallNotificationSettingRequestToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
