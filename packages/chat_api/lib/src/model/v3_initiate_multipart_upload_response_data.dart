//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/initiate_multipart_upload_response_policies.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_initiate_multipart_upload_response_data.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3InitiateMultipartUploadResponseData {
  /// Returns a new [V3InitiateMultipartUploadResponseData] instance.
  V3InitiateMultipartUploadResponseData({
    this.uploadId,
    this.policies,
  });

  @JsonKey(
    name: r'uploadId',
    required: false,
    includeIfNull: false,
  )
  final String? uploadId;

  @JsonKey(
    name: r'policies',
    required: false,
    includeIfNull: false,
  )
  final InitiateMultipartUploadResponsePolicies? policies;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3InitiateMultipartUploadResponseData &&
          other.uploadId == uploadId &&
          other.policies == policies;

  @override
  int get hashCode => uploadId.hashCode + policies.hashCode;

  factory V3InitiateMultipartUploadResponseData.fromJson(
          Map<String, dynamic> json) =>
      _$V3InitiateMultipartUploadResponseDataFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3InitiateMultipartUploadResponseDataToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
