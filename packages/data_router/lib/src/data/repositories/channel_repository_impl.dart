// lib/src/data/repositories/channel_repository_impl.dart

import 'dart:async';
import 'dart:collection';

import 'package:injectable/injectable.dart' hide Order;
import 'package:rxdart/rxdart.dart';
import 'package:shared/shared.dart';

import '../../../data_router.dart' show ChannelTypeEnum;
import '../../core/cache/cache.dart';
import '../../core/enums/direct_message_status_enum.dart'
    show DirectMessageStatusEnum;
import '../../core/metrics/metrics.dart';
import '../../domain/repositories/base_repository.dart';
import '../../domain/repositories/channel_repository.dart';
import '../database/entities/entities.dart';
import '../database/generated/objectbox.g.dart';
import '../events/private_data_events.dart';
import '../local/session_box.dart';
import '../manager/relation_manager.dart';

@LazySingleton(as: ChannelRepository)
class ChannelRepositoryImpl extends BaseRepository<Channel>
    implements ChannelRepository {
  ChannelRepositoryImpl(
    this._relationManager,
    this._channelBox,
    this._sessionBox,
    this._channelMetaDataBox,
    this._channelPrivateDataBox,
    this._userPrivateDataBox,
    this._store,
  ) : super(_channelBox) {
    _initializeRelationshipsWatching();
    _relationManager.registerUserListener(_onUserCacheUpdated);

    // [RACE CONDITION FIX] Listen for private data sync complete events
    _privateDataSyncSubscription = PrivateDataEventBus()
        .onPrivateDataSyncComplete
        .listen(_onPrivateDataSyncComplete);
  }

  final SessionBox<Channel> _channelBox;
  final SessionBox<ChannelMetadata> _channelMetaDataBox;
  final SessionBox<ChannelPrivateData> _channelPrivateDataBox;
  final SessionBox<UserPrivateData> _userPrivateDataBox;
  final SessionBox<Session> _sessionBox;
  final RelationManager _relationManager;
  final Store _store;

  // Enhanced Cache Management with timestamp validation
  final EnhancedCacheManager<Channel> _channelCacheManager =
      EnhancedCacheManager<Channel>(1000);
  final EnhancedCacheManager<ChannelMetadata> _metadataCacheManager =
      EnhancedCacheManager<ChannelMetadata>(1000);
  final EnhancedCacheManager<ChannelPrivateData> _privateDataCacheManager =
      EnhancedCacheManager<ChannelPrivateData>(1000);

  // Query cache for repeated patterns
  final Map<String, Query<Channel>> _queryCache = <String, Query<Channel>>{};

  // Performance tracking
  final ChannelRepositoryMetrics _metrics = ChannelRepositoryMetrics();
  DateTime _lastPerformanceLog = DateTime.now();

  StreamSubscription<List<ChannelPrivateData>>? _privateDataSubscription;
  StreamSubscription<List<ChannelMetadata>>? _metadataSubscription;
  StreamSubscription<PrivateDataSyncCompleteEvent>?
      _privateDataSyncSubscription;

  bool _isDisposed = false;
  Session? _currentActiveSession;

  final Queue<_UserUpdateEvent> _userUpdateQueue = Queue<_UserUpdateEvent>();
  bool _isProcessingUserUpdates = false;

  /// [DEBOUNCING] Timer for debouncing user updates to prevent excessive processing
  Timer? _userUpdateDebounceTimer;
  static const Duration _userUpdateDebounceDelay = Duration(milliseconds: 100);

  void _onUserCacheUpdated(
    String sessionKey,
    CacheUpdate<User> event,
  ) {
    // [OPTIMIZATION] Queue user updates to prevent race conditions
    _userUpdateQueue.add(_UserUpdateEvent(sessionKey, event));

    // [DEBOUNCING] Cancel previous timer and start a new one
    _userUpdateDebounceTimer?.cancel();
    _userUpdateDebounceTimer = Timer(_userUpdateDebounceDelay, () {
      _processUserUpdateQueue();
    });
  }

  Future<void> _processUserUpdateQueue() async {
    if (_isProcessingUserUpdates || _userUpdateQueue.isEmpty) return;

    _isProcessingUserUpdates = true;

    try {
      while (_userUpdateQueue.isNotEmpty) {
        final updateEvent = _userUpdateQueue.removeFirst();
        await _processUserUpdateEvent(updateEvent);
      }
    } finally {
      _isProcessingUserUpdates = false;
    }
  }

  Future<void> _processUserUpdateEvent(_UserUpdateEvent updateEvent) async {
    final sessionKey = updateEvent.sessionKey;
    final event = updateEvent.event;

    final updatedUserIds = [
      ...event.updated.keys.toList(),
      ...event.added.keys.toList(),
    ];

    if (updatedUserIds.isNotEmpty) {
      await _handleUserUpdates(sessionKey, updatedUserIds);
    }

    final deletedUserIds = event.deleted.keys.toList();
    if (deletedUserIds.isNotEmpty) {
      await _handleUserDeletions(sessionKey, deletedUserIds);
    }
  }

  Future<void> _handleUserUpdates(
    String sessionKey,
    List<String> userIds,
  ) async {
    final startTime = DateTime.now();
    final dmChannels = getDMChannelByUserIds(sessionKey, userIds);
    if (dmChannels.isEmpty) {
      _metrics.recordOperation(
        'handle_user_updates_empty',
        DateTime.now().difference(startTime),
      );
      return;
    }

    final channelsToUpdate = <Channel>[];

    for (final channel in dmChannels) {
      final user = getUserFromCache(channel.recipientId);
      if (user != null) {
        channel.recipient.target = user;
        // [RACE CONDITION FIX] UserPrivateData relationship removed
        // Load user data is handled through aliasName field
        channelsToUpdate.add(channel);
      }
    }

    if (channelsToUpdate.isNotEmpty) {
      _store.runInTransaction(TxMode.write, () {
        _trackTransaction();
        putMany(channelsToUpdate);
      });

      // Update channel cache after transaction
      for (final channel in channelsToUpdate) {
        _updateChannelCache(channel);
      }
    }

    _metrics.recordOperation(
      'handle_user_updates',
      DateTime.now().difference(startTime),
    );
  }

  Future<void> _handleUserDeletions(
    String sessionKey,
    List<String> userIds,
  ) async {
    final startTime = DateTime.now();
    final deletedDmChannels = getDMChannelByUserIds(sessionKey, userIds);

    if (deletedDmChannels.isEmpty) {
      _metrics.recordOperation(
        'handle_user_deletions_empty',
        DateTime.now().difference(startTime),
      );
      return;
    }

    for (final ch in deletedDmChannels) {
      ch.recipient.target = null;
      // UserPrivateData relationship removed
    }

    if (deletedDmChannels.isNotEmpty) {
      _store.runInTransaction(TxMode.write, () {
        _trackTransaction();
        putMany(deletedDmChannels);
      });

      // Update channel cache after transaction
      for (final channel in deletedDmChannels) {
        _updateChannelCache(channel);
      }
    }

    _metrics.recordOperation(
      'handle_user_deletions',
      DateTime.now().difference(startTime),
    );
  }

  /// Handle private data sync complete event
  /// This triggers relationship linking after UserPrivateData is available
  void _onPrivateDataSyncComplete(PrivateDataSyncCompleteEvent event) {
    final startTime = DateTime.now();
    try {
      // Trigger user cache refresh for the affected users
      // This will cause _onUserCacheUpdated to be called with proper UserPrivateData
      _relationManager.refreshUserCache(event.sessionKey, event.userIds);
      _metrics.recordOperation(
        'private_data_sync_complete',
        DateTime.now().difference(startTime),
      );
    } catch (e) {
      _metrics.recordOperation(
        'private_data_sync_error',
        DateTime.now().difference(startTime),
      );
      // Handle error silently
    }
  }

  @override
  int insert(Channel channel) {
    _trackTransaction();
    final startTime = DateTime.now();

    if (channel.id == 0) {
      final existingChannel = channel.channelTypeRaw == 0
          ? getDMChannel(recipientId: channel.recipientId)
          : getChannel(
              workspaceId: channel.workspaceId,
              channelId: channel.channelId,
            );
      if (existingChannel != null) {
        if (!_shouldInsert(channel, existingChannel)) {
          // If the existing channel is newer, skip the insert.
          _metrics.recordOperation(
            'insert_skipped',
            DateTime.now().difference(startTime),
          );
          return existingChannel.id;
        }
        channel.id = existingChannel.id;
      }
    }

    // Step 1: Synchronously resolve metadata conflicts before anything else.
    _resolveMetadataBeforeInsert(channel);

    // Step 2: Handle other relationships as usual.
    _handleChannelRelationship(channel);

    // Step 3: Put the channel, which will cascade the correctly resolved metadata.
    final result = super.put(channel);

    // Step 4: Update channel cache
    _updateChannelCache(channel);

    _metrics.recordOperation('insert', DateTime.now().difference(startTime));
    return result;
  }

  /// Determines if the new channel should be inserted based on its update time
  bool _shouldInsert(Channel newChannel, Channel existingChannel) {
    // If the existing channel is newer, skip the insert.
    if (existingChannel.isPartial ||
        (existingChannel.updateTime != null &&
            newChannel.updateTime != null &&
            existingChannel.updateTime!.isBefore(newChannel.updateTime!)) ||
        existingChannel.totalMembers != newChannel.totalMembers) {
      if (existingChannel.isPartial) {
        AppEventBus.publish(
          UpdateChannelIdForTempDMMessage(
            oldWorkspaceId: existingChannel.workspaceId,
            oldChannelId: existingChannel.channelId,
            newWorkspaceId: newChannel.workspaceId,
            newChannelId: newChannel.channelId,
          ),
        );
      }

      return true;
    }
    return false;
  }

  // Enhanced batch insert with performance tracking
  @override
  Future<List<int>> insertAll(List<Channel> channels) async {
    _trackTransaction();
    final startTime = DateTime.now();

    final channelsToUpdate = _existsAndUpdate(channels);

    // Batch resolve metadata for all channels
    final resolvedMetadata = _batchResolveMetadata(channelsToUpdate);

    // Apply resolved metadata to channels
    for (final channel in channelsToUpdate) {
      final cacheKey = '${channel.workspaceId}:${channel.channelId}';
      final resolved = resolvedMetadata[cacheKey];
      if (resolved != null && channel.metadata.target != null) {
        final incomingTime = channel.metadata.target!.updateTime;
        final existingTime = resolved.updateTime;

        final shouldUpdate = incomingTime != null &&
            (existingTime == null || incomingTime.isAfter(existingTime));

        if (shouldUpdate) {
          channel.metadata.target!.id = resolved.id;
        } else {
          channel.metadata.target = resolved;
        }
      }

      // Handle other relationships
      await _handleChannelRelationship(channel);
    }

    Log.d(
      name: 'ChannelRepositoryImpl.insertAll',
      'Inserting ${channelsToUpdate.length} channels',
    );
    final result = putMany(channelsToUpdate);

    // Update channel cache for all inserted channels
    for (final channel in channelsToUpdate) {
      _updateChannelCache(channel);
    }

    _metrics.recordOperation('insertAll', DateTime.now().difference(startTime));
    return result;
  }

  void _resolveMetadataBeforeInsert(Channel channel) {
    // Only act if there's an incoming metadata attached to the channel.
    if (channel.metadata.target == null) {
      return;
    }

    final incomingMetadata = channel.metadata.target!;
    final cacheKey = '${channel.workspaceId}:${channel.channelId}';

    // Check cache first using EnhancedCacheManager
    var existingMetadata = _metadataCacheManager.get(cacheKey);

    // If not in cache, query database
    if (existingMetadata == null) {
      _metrics.cacheMisses++;
      existingMetadata = getChannelMetadata(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
      );

      // Cache the result
      if (existingMetadata != null) {
        _updateMetadataCache(existingMetadata);
      }
    } else {
      _metrics.cacheHits++;
    }

    if (existingMetadata != null) {
      // A record exists. We must decide whether to update it or discard the incoming one.
      final incomingTime = incomingMetadata.updateTime;
      final existingTime = existingMetadata.updateTime;

      // Condition to update: incoming time must be valid and later than existing time.
      final shouldUpdate = incomingTime != null &&
          (existingTime == null || incomingTime.isAfter(existingTime));

      if (shouldUpdate) {
        // The incoming metadata is newer. We'll update the existing record.
        incomingMetadata.id = existingMetadata.id;
        // Update cache
        _updateMetadataCache(incomingMetadata);
      } else {
        // The incoming metadata is older or the same. Discard it and
        // re-link the channel to the existing, more up-to-date metadata from the DB.
        channel.metadata.target = existingMetadata;

        if (channel.lastMessageCreateTime == null) {
          channel.lastMessageCreateTime = existingMetadata.createTime;
        }
      }
    }
  }

  // Batch metadata resolution for multiple channels using EnhancedCacheManager
  Map<String, ChannelMetadata> _batchResolveMetadata(List<Channel> channels) {
    final startTime = DateTime.now();
    if (channels.isEmpty) return {};

    final channelKeys = channels
        .where((c) => c.metadata.target != null)
        .map((c) => '${c.workspaceId}:${c.channelId}')
        .toSet()
        .toList();

    if (channelKeys.isEmpty) return {};

    // Check cache first
    final resolvedMetadata = <String, ChannelMetadata>{};
    final keysToQuery = <String>[];

    for (final key in channelKeys) {
      final cached = _metadataCacheManager.get(key);
      if (cached != null) {
        resolvedMetadata[key] = cached;
        _metrics.cacheHits++;
      } else {
        keysToQuery.add(key);
        _metrics.cacheMisses++;
      }
    }

    // Batch query for missing metadata
    if (keysToQuery.isNotEmpty) {
      final channelIds = keysToQuery.map((k) => k.split(':')[1]).toList();
      final workspaceIds = keysToQuery.map((k) => k.split(':')[0]).toList();

      final query = _channelMetaDataBox
          .queryWith(
            ChannelMetadata_.channelId
                .oneOf(channelIds)
                .and(ChannelMetadata_.workspaceId.oneOf(workspaceIds)),
          )
          .build();

      final existingMetadata = query.find();
      query.close();

      // Cache and add to resolved metadata
      for (final meta in existingMetadata) {
        final key = '${meta.workspaceId}:${meta.channelId}';
        _updateMetadataCache(meta);
        resolvedMetadata[key] = meta;
      }
    }

    _metrics.recordOperation(
      'batch_resolve_metadata',
      DateTime.now().difference(startTime),
    );
    return resolvedMetadata;
  }

  // Optimized relationship handling with batch loading and performance tracking
  Future<void> _handleChannelRelationship(Channel channel) async {
    final startTime = DateTime.now();
    final relatedData = await _loadChannelRelatedData(channel);

    // Synchronous assignment to avoid race conditions
    if (relatedData.metadata != null) {
      channel.metadata.target = relatedData.metadata;
      channel.lastMessageCreateTime = relatedData.metadata!.createTime;
    }

    if (relatedData.privateData != null) {
      channel.privateData.target = relatedData.privateData;
      channel.pinned = relatedData.privateData!.pinned;
      channel.sort = relatedData.privateData!.sort;
    }

    if (channel.isDM && relatedData.user != null) {
      channel.recipient.target = relatedData.user;
      // [RACE CONDITION FIX] UserPrivateData relationship removed
      // Sync aliasName and blocked from UserPrivateData to User and Channel
      if (relatedData.userPrivateData != null) {
        final userPrivateData = relatedData.userPrivateData!;
        final user = relatedData.user!;

        // Update User entity with UserPrivateData fields
        if (user.aliasName != userPrivateData.aliasName) {
          user.aliasName = userPrivateData.aliasName;
        }
        if (user.isPartial && (user.blocked != userPrivateData.blocked)) {
          user.blocked = userPrivateData.blocked;
        }

        // Update Channel entity with UserPrivateData fields
        if (channel.aliasName != userPrivateData.aliasName) {
          channel.aliasName = userPrivateData.aliasName;
        }

        // Note: Channel entity doesn't have blocked field
        // blocked status is handled through User entity only
      }
    }

    _metrics.recordOperation(
      'handle_channel_relationship',
      DateTime.now().difference(startTime),
    );
  }

  Future<_ChannelRelatedData> _loadChannelRelatedData(Channel channel) async {
    final startTime = DateTime.now();
    final futures = <Future>[];

    // Parallel loading of related data
    final metadataFuture =
        _loadMetadata(channel.workspaceId, channel.channelId);
    final privateDataFuture = _loadPrivateData(channel.channelId);

    futures.addAll([metadataFuture, privateDataFuture]);

    Future<User?>? userFuture;
    Future<UserPrivateData?>? userPrivateDataFuture;
    if (channel.isDM && channel.recipientId.isNotEmpty) {
      userFuture = _loadUser(channel.recipientId);
      // [RACE CONDITION FIX] Load UserPrivateData directly from database
      userPrivateDataFuture = _loadUserPrivateData(channel.recipientId);
      futures.addAll([userFuture, userPrivateDataFuture]);
    }

    final results = await Future.wait(futures);

    final relatedData = _ChannelRelatedData(
      metadata: results[0] as ChannelMetadata?,
      privateData: results[1] as ChannelPrivateData?,
      user: userFuture != null ? results[2] as User? : null,
      userPrivateData:
          userPrivateDataFuture != null ? results[3] as UserPrivateData? : null,
    );

    _metrics.recordOperation(
      'load_channel_related_data',
      DateTime.now().difference(startTime),
    );
    return relatedData;
  }

  Future<ChannelMetadata?> _loadMetadata(
    String workspaceId,
    String channelId,
  ) async {
    final cacheKey = '$workspaceId:$channelId';
    final cached = _metadataCacheManager.get(cacheKey);
    if (cached != null) {
      _metrics.cacheHits++;
      return cached;
    }

    _metrics.cacheMisses++;
    final metadata = getChannelMetadata(
      workspaceId: workspaceId,
      channelId: channelId,
    );

    if (metadata != null) {
      _updateMetadataCache(metadata);
    }

    return metadata;
  }

  Future<ChannelPrivateData?> _loadPrivateData(String channelId) async {
    final cached = _privateDataCacheManager.get(channelId);
    if (cached != null) {
      _metrics.cacheHits++;
      return cached;
    }

    _metrics.cacheMisses++;
    final privateData = getChannelPrivateData(channelId);

    if (privateData != null) {
      _updatePrivateDataCache(privateData);
    }

    return privateData;
  }

  // Helper method to get user from cache via relation manager
  Future<User?> _loadUser(String userId) async {
    return getUserFromCache(userId);
  }

  User? getUserFromCache(String userId) {
    final user = _relationManager.userCache[userId];

    return user;
  }

  /// Load UserPrivateData directly from database to avoid race conditions
  /// This method queries UserPrivateData independently of User relationships
  Future<UserPrivateData?> _loadUserPrivateData(String userId) async {
    final startTime = DateTime.now();

    if (userId.trim().isEmpty) {
      _metrics.recordOperation(
        'load_user_private_data_empty_id',
        DateTime.now().difference(startTime),
      );
      return null;
    }

    try {
      // Query UserPrivateData directly from database using userId
      final query = _userPrivateDataBox
          .queryWith(UserPrivateData_.userIdField.equals(userId))
          .build();
      final userPrivateData = query.findFirst();
      query.close();

      _metrics.recordOperation(
        userPrivateData != null
            ? 'load_user_private_data_success'
            : 'load_user_private_data_not_found',
        DateTime.now().difference(startTime),
      );

      return userPrivateData;
    } catch (e) {
      _metrics.recordOperation(
        'load_user_private_data_error',
        DateTime.now().difference(startTime),
      );
      Log.e(
        name: 'ChannelRepositoryImpl._loadUserPrivateData',
        'Failed to load UserPrivateData for userId: $userId, error: $e',
      );
      return null;
    }
  }

  ChannelMetadata? getChannelMetadata({
    required String workspaceId,
    required String channelId,
  }) {
    final startTime = DateTime.now();

    // Try to get from cache first
    final cachedMetadata = _metadataCacheManager.get(channelId);
    if (cachedMetadata != null && cachedMetadata.workspaceId == workspaceId) {
      _metrics.recordOperation(
        'get_channel_metadata_cache_hit',
        DateTime.now().difference(startTime),
      );
      return cachedMetadata;
    }

    // Cache miss, query from database
    final query = _channelMetaDataBox
        .queryWith(
          ChannelMetadata_.workspaceId
              .equals(workspaceId)
              .and(ChannelMetadata_.channelId.equals(channelId)),
        )
        .build();
    final metadata = query.findFirst();
    query.close();

    if (metadata != null) {
      _updateMetadataCache(metadata);
      _metrics.recordOperation(
        'get_channel_metadata_db_hit',
        DateTime.now().difference(startTime),
      );
    } else {
      _metrics.recordOperation(
        'get_channel_metadata_miss',
        DateTime.now().difference(startTime),
      );
    }

    return metadata;
  }

  @override
  List<Channel> getAllChannel({
    int limit = 100,
    int offset = 0,
  }) {
    final query = messageBox
        .queryWith(
          Channel_.channelTypeRaw.notEquals(ChannelTypeEnum.broadcast.value),
        )
        .order(Channel_.lastMessageCreateTime, flags: Order.descending)
        .build()
      ..limit = limit
      ..offset = offset;
    final channels = query.find();
    query.close();
    return channels;
  }

  @override
  Channel? getChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final startTime = DateTime.now();
    // Try to get from cache first
    final cachedChannel = _channelCacheManager.get(channelId);
    if (cachedChannel != null && cachedChannel.workspaceId == workspaceId) {
      _metrics.recordOperation(
        'get_channel_cache_hit',
        DateTime.now().difference(startTime),
      );
      return cachedChannel;
    }

    // Cache miss, query from database
    final channel = getFirst(
      Channel_.workspaceId
          .equals(workspaceId)
          .and(Channel_.channelId.equals(channelId)),
    );

    if (channel != null) {
      _updateChannelCache(channel);
      _metrics.recordOperation(
        'get_channel_db_hit',
        DateTime.now().difference(startTime),
      );
    } else {
      _metrics.recordOperation(
        'get_channel_miss',
        DateTime.now().difference(startTime),
      );
    }

    return channel;
  }

  @override
  bool deleteChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final startTime = DateTime.now();
    final channel = getFirst(
      Channel_.workspaceId
          .equals(workspaceId)
          .and(Channel_.channelId.equals(channelId)),
    );

    if (channel != null) {
      if (channel.metadata.target != null) {
        _channelMetaDataBox.remove(channel.metadata.target!.id);
        _removeFromMetadataCache(channelId);
      }

      if (channel.privateData.target != null) {
        _removeFromPrivateDataCache(channelId);
      }

      _removeFromChannelCache(channelId);
      final result = remove(channel.id);

      _metrics.recordOperation(
        'delete_channel_success',
        DateTime.now().difference(startTime),
      );
      return result;
    }

    _metrics.recordOperation(
      'delete_channel_not_found',
      DateTime.now().difference(startTime),
    );
    return false;
  }

  @override
  int deleteAllChannel() {
    return removeAll();
  }

  @override
  bool isEmpty() {
    return count() == 0;
  }

  @override
  Channel? getDMChannel({required String recipientId}) {
    if (recipientId.trim().isEmpty) {
      print('[ChannelRepository] getDMChannel: recipientId is empty');
      return null;
    }
    return getFirst(
      Channel_.channelTypeRaw
          .equals(ChannelTypeEnum.dm.value)
          .and(Channel_.recipientIdField.equals(recipientId)),
    );
  }

  @override
  Stream<List<Channel>> observerListChannels() {
    final condition = (Channel_.channelTypeRaw
            .equals(ChannelTypeEnum.dm.value)
            .and(
              Channel_.dmStatusRaw
                  .equals(DirectMessageStatusEnum.contacted.value)
                  .or(Channel_.userId.equals(messageBox.activeSessionKey)),
            )
            .or(
              Channel_.channelTypeRaw.equals(ChannelTypeEnum.channel.value),
            )) &
        Channel_.lastMessageCreateTime.notNull();

    return messageBox
        .queryWith(condition)
        .order(Channel_.pinned, flags: Order.descending)
        .order(Channel_.sort, flags: Order.descending)
        .order(Channel_.lastMessageCreateTime, flags: Order.descending)
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  Stream<List<Channel>> watchMessageRequests() {
    final condition = (Channel_.channelTypeRaw
            .equals(ChannelTypeEnum.dm.value)
            .and(
              Channel_.dmStatusRaw
                  .equals(DirectMessageStatusEnum.pending.value),
            )
            .and(Channel_.userId.notEquals(messageBox.activeSessionKey))) &
        Channel_.lastMessageCreateTime.notNull();
    return messageBox
        .queryWith(condition)
        .order(Channel_.lastMessageCreateTime, flags: Order.descending)
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.find());
  }

  @override
  int deleteDMChannel({
    required String recipientId,
  }) {
    if (recipientId.trim().isEmpty) {
      print('[ChannelRepository] deleteDMChannel: recipientId is empty');
      return 0;
    }
    final condition = Channel_.channelTypeRaw
        .equals(0) // DM type
        .and(Channel_.recipientIdField.equals(recipientId));

    final query = messageBox.queryWith(condition).build();
    final result = query.remove();
    query.close();
    return result;
  }

  @override
  Stream<Channel?> watchChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final condition = Channel_.workspaceId
        .equals(workspaceId)
        .and(Channel_.channelId.equals(channelId));

    return messageBox
        .queryWith(condition)
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.findFirst());
  }

  @override
  Stream<Channel?> watchDMChannel(String recipientId) {
    if (recipientId.trim().isEmpty) {
      print('[ChannelRepository] watchDMChannel: recipientId is empty');
      return Stream.value(null);
    }
    final condition = Channel_.participantId.equals(recipientId);

    return messageBox
        .queryWith(condition)
        .watch(triggerImmediately: true)
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) => query.findFirst());
  }

  @override
  int updateAvatarChannel({
    required String workspaceId,
    required String channelId,
    required String avatarPath,
  }) {
    final channel = getFirst(
      Channel_.workspaceId
          .equals(workspaceId)
          .and(Channel_.channelId.equals(channelId)),
    );

    if (channel != null) {
      channel.avatar = avatarPath;
      return insert(channel);
    }
    return 0;
  }

  @override
  List<Channel> getChannelsBySessionKey(String sessionKey) {
    final query = _channelBox
        .queryWith(Channel_.sessionKeyField.equals(sessionKey))
        .build();
    final channels = query.find();
    query.close();
    return channels;
  }

  @override
  void deleteSession(String sessionKey) {
    final query = _channelBox.box
        .query(Channel_.sessionKeyField.equals(sessionKey))
        .build();
    query.remove();
    query.close();
  }

  @override
  int updateNotificationChannel({
    required String workspaceId,
    required String channelId,
    required bool isNotification,
  }) {
    final channel = getFirst(
      Channel_.workspaceId
          .equals(workspaceId)
          .and(Channel_.channelId.equals(channelId)),
    );

    if (channel != null && channel.metadata.target != null) {
      final metadata = channel.metadata.target!;
      metadata.notificationStatus = isNotification;

      final result = _channelMetaDataBox.put(metadata);
      channel.updateTime = DateTime.now();
      _channelBox.put(channel);
      return result;
    }
    return 0;
  }

  List<Channel> getDMChannelByUserIds(String sessionKey, List<String> userIds) {
    final condition = Channel_.sessionKey
        .equals(sessionKey)
        .and(
          Channel_.recipientIdField.oneOf(userIds),
        )
        .and(Channel_.channelTypeRaw.equals(ChannelTypeEnum.dm.value));
    final query = _channelBox.queryWith(condition).build();
    final channels = query.find();
    query.close();

    return channels;
  }

  @override
  List<Channel> getAllDMChannel() {
    final condition = Channel_.recipientIdField.notNull().and(
          Channel_.channelTypeRaw.equals(ChannelTypeEnum.dm.value) &
              Channel_.dmStatusRaw
                  .equals(DirectMessageStatusEnum.contacted.value)
                  .or(
                    Channel_.userId.equals(messageBox.activeSessionKey),
                  ),
        );
    final query = messageBox
        .queryWith(condition)
        .order(Channel_.lastMessageCreateTime, flags: Order.descending)
        .build();
    final channels = query.find();
    query.close();
    return channels;
  }

  @override
  List<String> getUnreadChannelIds() {
    final channels = getAll();
    final unreadChannelIds = channels
        .where(
          (channel) =>
              channel.metadata.target?.unreadCount != null &&
              channel.metadata.target!.unreadCount > 0,
        )
        .map((channel) => channel.channelId)
        .toList();
    return unreadChannelIds;
  }

  List<Channel> _existsAndUpdate(List<Channel> channels) {
    if (channels.isEmpty) return [];
    final uniqueChannelsMap = <String, Channel>{};
    for (final channel in channels) {
      final key = channel.channelTypeRaw == 0
          ? 'dm-${channel.recipientId}'
          : 'channel-${channel.workspaceId}-${channel.channelId}';
      uniqueChannelsMap[key] = channel;
    }
    final uniqueChannels = uniqueChannelsMap.values.toList();

    final channelsToUpdated = <Channel>[];
    final dmChannels = uniqueChannels
        .where((ch) => ch.channelTypeRaw == 0 && ch.recipientId.isNotEmpty)
        .toList();
    final nonDmChannels =
        uniqueChannels.where((ch) => ch.channelTypeRaw != 0).toList();

    // Early return if no channels to process
    if (dmChannels.isEmpty && nonDmChannels.isEmpty) return [];

    final dmRecipientIds =
        dmChannels.map((ch) => ch.recipientId).toSet().toList();
    final wsIds = nonDmChannels.map((ch) => ch.workspaceId).toSet().toList();
    final chIds = nonDmChannels.map((ch) => ch.channelId).toSet().toList();

    // [OPTIMIZATION] Single optimized query for both DM and non-DM channels
    Condition<Channel>? condition;

    if (dmRecipientIds.isNotEmpty && (wsIds.isNotEmpty || chIds.isNotEmpty)) {
      // Both DM and non-DM channels
      condition = Channel_.channelTypeRaw
          .equals(0)
          .and(Channel_.recipientIdField.oneOf(dmRecipientIds))
          .or(
            Channel_.channelTypeRaw
                .notEquals(0)
                .and(Channel_.workspaceId.oneOf(wsIds))
                .and(Channel_.channelId.oneOf(chIds)),
          );
    } else if (dmRecipientIds.isNotEmpty) {
      // Only DM channels
      condition = Channel_.channelTypeRaw
          .equals(0)
          .and(Channel_.recipientIdField.oneOf(dmRecipientIds));
    } else if (wsIds.isNotEmpty || chIds.isNotEmpty) {
      // Only non-DM channels
      condition = Channel_.channelTypeRaw
          .notEquals(0)
          .and(Channel_.workspaceId.oneOf(wsIds))
          .and(Channel_.channelId.oneOf(chIds));
    }

    final existingChannels = condition != null ? query(condition) : <Channel>[];

    final existingMap = <String, Channel>{};
    for (final existing in existingChannels) {
      final key = existing.channelTypeRaw == 0
          ? 'dm-${existing.recipientId}'
          : 'channel-${existing.workspaceId}-${existing.channelId}';
      existingMap[key] = existing;
    }

    for (final channel in uniqueChannels) {
      final key = channel.channelTypeRaw == 0
          ? 'dm-${channel.recipientId}'
          : 'channel-${channel.workspaceId}-${channel.channelId}';
      if (existingMap.containsKey(key)) {
        final existingChannel = existingMap[key]!;
        if (_shouldInsert(channel, existingChannel)) {
          // If the existing channel is newer, skip the insert.
          channel.id = existingChannel.id;
          channelsToUpdated.add(channel);
        }
      } else {
        // If the channel does not exist, we will insert it.
        channelsToUpdated.add(channel);
      }
    }

    return channelsToUpdated;
  }

  void _initializeRelationshipsWatching() {
    final startTime = DateTime.now();
    _sessionBox.box
        .query(Session_.active.equals(true))
        .watch(triggerImmediately: true)
        .listen((event) {
      final watchStartTime = DateTime.now();
      final activeSession = event.findFirst();

      if (_shouldInitRelationSubscription(activeSession)) {
        _currentActiveSession = activeSession;
        _initChannelPrivateDataRelation(activeSession!.sessionKey);
        _metrics.recordOperation(
          'init_relation_subscription',
          DateTime.now().difference(watchStartTime),
        );
        return;
      }

      if (_shouldCleanRelationSubscription(activeSession)) {
        _disposeDataSubscriptions();
        _currentActiveSession = null;
        _metrics.recordOperation(
          'clean_relation_subscription',
          DateTime.now().difference(watchStartTime),
        );
      }
    });
    _metrics.recordOperation(
      'initialize_relationships_watching',
      DateTime.now().difference(startTime),
    );
  }

  // Added debounce, error handling with retry, and performance tracking
  void _initChannelPrivateDataRelation(String sessionKey) {
    final startTime = DateTime.now();
    _privateDataSubscription =
        watchAllChannelPrivateData(sessionKey).distinct().listen(
      _handleChannelPrivateData,
      onError: (error) {
        _metrics.recordOperation(
          'private_data_relation_error',
          DateTime.now().difference(startTime),
        );
        Future.delayed(const Duration(seconds: 5), () {
          if (!_isDisposed && _currentActiveSession != null) {
            _initChannelPrivateDataRelation(_currentActiveSession!.sessionKey);
          }
        });
      },
    );

    _metadataSubscription = watchAllChannelMetadata(sessionKey)
        .distinct() // Avoid duplicate events
        .listen(
      _handleMetadata,
      onError: (error) {
        Future.delayed(const Duration(seconds: 5), () {
          if (!_isDisposed && _currentActiveSession != null) {
            _initChannelPrivateDataRelation(_currentActiveSession!.sessionKey);
          }
        });
      },
    );
  }

  bool _shouldCleanRelationSubscription(Session? activeSession) =>
      activeSession == null && _currentActiveSession != null;

  bool _shouldInitRelationSubscription(Session? activeSession) =>
      activeSession != null && _currentActiveSession == null;

  // Optimized _handleChannelPrivateData with reduced transaction scope and performance tracking
  void _handleChannelPrivateData(
    List<ChannelPrivateData> listPrivateData,
  ) {
    if (_isDisposed) return;
    final startTime = DateTime.now();

    try {
      final Map<String, ChannelPrivateData> newDataMap = {
        for (var pd in listPrivateData) pd.channelId: pd,
      };

      // Use EnhancedCacheManager to check for updates
      final Set<String> newIds = newDataMap.keys.toSet();
      final Set<String> removedIds = <String>{};
      final Set<String> updatedIds = newIds.where((id) {
        final newData = newDataMap[id]!;
        return !_privateDataCacheManager.isNewerThan(id, newData);
      }).toSet();

      final idsToQuery = [...removedIds, ...updatedIds];

      if (idsToQuery.isNotEmpty) {
        // Prepare data outside transaction
        final channelsToUpdate =
            _channelBox.query(Channel_.channelId.oneOf(idsToQuery));

        final Map<String, Channel> channelMap = {
          for (var ch in channelsToUpdate) ch.channelId: ch,
        };

        // Prepare channel updates outside transaction
        final List<Channel> channelsToSave = [];

        for (final id in removedIds) {
          final channel = channelMap[id];
          if (channel != null) {
            channel.privateData.target = null;
            channel.pinned = false;
            channel.sort = -1;
            channelsToSave.add(channel);
          }
        }

        for (final id in updatedIds) {
          final newPrivateData = newDataMap[id]!;
          final channel = channelMap[id];
          if (channel != null) {
            channel.privateData.target = newPrivateData;
            channel.pinned = newPrivateData.pinned;
            channel.sort = newPrivateData.sort;
            channelsToSave.add(channel);
          }
        }

        // Minimal transaction scope - only database writes
        _store.runInTransaction(TxMode.write, () {
          _trackTransaction();
          if (channelsToSave.isNotEmpty) {
            _channelBox.putMany(channelsToSave);
          }
        });

        // Update cache after successful transaction
        for (final id in removedIds) {
          _removeFromPrivateDataCache(id);
        }

        for (final id in updatedIds) {
          final newPrivateData = newDataMap[id]!;
          _updatePrivateDataCache(newPrivateData);
        }

        _metrics.recordOperation(
          'handle_channel_private_data_updates',
          DateTime.now().difference(startTime),
        );
      } else {
        _metrics.recordOperation(
          'handle_channel_private_data_no_updates',
          DateTime.now().difference(startTime),
        );
      }
    } catch (e) {
      _metrics.recordOperation(
        'handle_channel_private_data_error',
        DateTime.now().difference(startTime),
      );
      Log.e('Error updating ChannelPrivateData cache: $e');
    }
  }

  // Optimized _handleMetadata with reduced transaction scope and performance tracking
  void _handleMetadata(List<ChannelMetadata> listMetadata) {
    if (_isDisposed) return;
    final startTime = DateTime.now();

    try {
      final Map<String, ChannelMetadata> newMetaMap = {
        for (var md in listMetadata) md.channelId: md,
      };

      // Use EnhancedCacheManager to check for updates
      final newIds = newMetaMap.keys.toSet();
      final removedIds = <String>{};
      final updatedIds = newIds.where((id) {
        final updated = newMetaMap[id]!;
        return !_metadataCacheManager.isNewerThan(id, updated);
      }).toSet();

      final idsToQuery = [...removedIds, ...updatedIds];
      if (idsToQuery.isNotEmpty) {
        // Prepare data outside transaction
        final channelsToUpdate =
            _channelBox.query(Channel_.channelId.oneOf(idsToQuery));

        final channelMap = {
          for (var ch in channelsToUpdate) ch.channelId: ch,
        };

        // Prepare channel updates outside transaction
        final List<Channel> channelsToSave = [];

        for (final id in removedIds) {
          final channel = channelMap[id];
          if (channel != null) {
            channel.metadata.target = null;
            channel.lastMessageCreateTime = null;
            channelsToSave.add(channel);
          }
        }

        for (final id in updatedIds) {
          final newMeta = newMetaMap[id]!;
          final channel = channelMap[id];
          if (channel != null) {
            channel.metadata.target = newMeta;
            channel.lastMessageCreateTime = newMeta.createTime;
            channelsToSave.add(channel);
          }
        }

        // Minimal transaction scope - only database writes
        _store.runInTransaction(TxMode.write, () {
          _trackTransaction();
          if (channelsToSave.isNotEmpty) {
            _channelBox.putMany(channelsToSave);
          }
        });

        // Update cache after successful transaction
        for (final id in removedIds) {
          _removeFromMetadataCache(id);
        }

        for (final id in updatedIds) {
          final newMeta = newMetaMap[id]!;
          _updateMetadataCache(newMeta);
        }

        _metrics.recordOperation(
          'handle_metadata_updates',
          DateTime.now().difference(startTime),
        );
      } else {
        _metrics.recordOperation(
          'handle_metadata_no_updates',
          DateTime.now().difference(startTime),
        );
      }
    } catch (e) {
      _metrics.recordOperation(
        'handle_metadata_error',
        DateTime.now().difference(startTime),
      );
      Log.e('Error updating ChannelMetadata cache: $e');
    }
  }

  Stream<List<ChannelMetadata>> watchAllChannelMetadata(String sessionKey) {
    final startTime = DateTime.now();
    _metrics.recordOperation('watch_all_channel_metadata_start', Duration.zero);

    return _channelMetaDataBox
        .watch(ChannelMetadata_.sessionKey.equals(sessionKey))
        .debounceTime(GlobalConfig.dbInternalDebounceTime)
        .map((query) {
      final queryStartTime = DateTime.now();
      final result = query.find();
      _metrics.recordOperation(
        'watch_all_channel_metadata_query',
        DateTime.now().difference(queryStartTime),
      );
      return result;
    }).doOnListen(() {
      _metrics.recordOperation(
        'watch_all_channel_metadata_setup',
        DateTime.now().difference(startTime),
      );
    }).doOnCancel(() {
      _metrics.recordOperation(
        'watch_all_channel_metadata_cancel',
        Duration.zero,
      );
    });
  }

  @override
  int insertChannelPrivateData(ChannelPrivateData channelPrivateData) {
    final startTime = DateTime.now();
    final result = _channelPrivateDataBox.put(channelPrivateData);
    _updatePrivateDataCache(channelPrivateData);
    _metrics.recordOperation(
      'insert_channel_private_data',
      DateTime.now().difference(startTime),
    );
    return result;
  }

  @override
  ChannelPrivateData? getChannelPrivateData(String channelId) {
    final startTime = DateTime.now();

    // Try to get from cache first
    final cachedPrivateData = _privateDataCacheManager.get(channelId);
    if (cachedPrivateData != null) {
      _metrics.recordOperation(
        'get_channel_private_data_cache_hit',
        DateTime.now().difference(startTime),
      );
      return cachedPrivateData;
    }

    // Cache miss, try to get from database
    final privateData = _channelPrivateDataBox.getFirstWith(
      ChannelPrivateData_.channelIdField.equals(channelId),
    );

    // If found in database, update cache
    if (privateData != null) {
      _updatePrivateDataCache(privateData);
      _metrics.recordOperation(
        'get_channel_private_data_db_hit',
        DateTime.now().difference(startTime),
      );
    } else {
      _metrics.recordOperation(
        'get_channel_private_data_miss',
        DateTime.now().difference(startTime),
      );
    }

    _logPerformanceMetrics();
    return privateData;
  }

  Stream<List<ChannelPrivateData>> watchAllChannelPrivateData(
    String sessionKey,
  ) {
    final startTime = DateTime.now();
    _metrics.recordOperation(
      'watch_all_channel_private_data_start',
      Duration.zero,
    );

    return _channelPrivateDataBox
        .watch(ChannelPrivateData_.sessionKey.equals(sessionKey))
        .debounceTime(GlobalConfig.dbInternalDebounceTime)
        .map((query) {
      final queryStartTime = DateTime.now();
      final result = query.find();
      _metrics.recordOperation(
        'watch_all_channel_private_data_query',
        DateTime.now().difference(queryStartTime),
      );
      return result;
    }).doOnListen(() {
      _metrics.recordOperation(
        'watch_all_channel_private_data_setup',
        DateTime.now().difference(startTime),
      );
    }).doOnCancel(() {
      _metrics.recordOperation(
        'watch_all_channel_private_data_cancel',
        Duration.zero,
      );
    });
  }

  @override
  bool markChannelAsRead(String channelId, String messageId) {
    final startTime = DateTime.now();
    final channelPrivateData = _privateDataCacheManager.get(channelId);
    if (channelPrivateData != null) {
      final updatedData =
          channelPrivateData.copyWith(lastSeenMessageId: messageId);
      _channelPrivateDataBox.put(updatedData);
      _updatePrivateDataCache(updatedData);
      _metrics.recordOperation(
        'mark_channel_as_read_success',
        DateTime.now().difference(startTime),
      );
      return true;
    }
    _metrics.recordOperation(
      'mark_channel_as_read_fail',
      DateTime.now().difference(startTime),
    );
    return false;
  }

  @override
  bool pinChannel(String channelId) {
    final startTime = DateTime.now();
    final channelPrivateData = getChannelPrivateData(channelId);
    if (channelPrivateData != null) {
      final updatedData = channelPrivateData.copyWith(pinned: true);
      _channelPrivateDataBox.put(updatedData);
      _updatePrivateDataCache(updatedData);
      _metrics.recordOperation(
        'pin_channel_success',
        DateTime.now().difference(startTime),
      );
      return true;
    }
    _metrics.recordOperation(
      'pin_channel_fail',
      DateTime.now().difference(startTime),
    );
    return false;
  }

  @override
  bool unpinChannel(String channelId) {
    final startTime = DateTime.now();
    final channelPrivateData = getChannelPrivateData(channelId);
    if (channelPrivateData != null) {
      final updatedData = channelPrivateData.copyWith(pinned: false);
      _channelPrivateDataBox.put(updatedData);
      _updatePrivateDataCache(updatedData);
      _metrics.recordOperation(
        'unpin_channel_success',
        DateTime.now().difference(startTime),
      );
      return true;
    }
    _metrics.recordOperation(
      'unpin_channel_fail',
      DateTime.now().difference(startTime),
    );
    return false;
  }

  @override
  bool updateChannelUnreadCount(String channelId, int unreadCount) {
    final startTime = DateTime.now();
    final channelPrivateData = getChannelPrivateData(channelId);
    if (channelPrivateData != null) {
      final updatedData = channelPrivateData.copyWith(unreadCount: unreadCount);
      _channelPrivateDataBox.put(updatedData);
      _updatePrivateDataCache(updatedData);
      _metrics.recordOperation(
        'update_channel_unread_count_success',
        DateTime.now().difference(startTime),
      );
      return true;
    }
    _metrics.recordOperation(
      'update_channel_unread_count_fail',
      DateTime.now().difference(startTime),
    );
    return false;
  }

  @override
  List<ChannelPrivateData> getPinnedChannels() {
    final query = _channelPrivateDataBox
        .queryWith(ChannelPrivateData_.pinned.equals(true))
        .order(ChannelPrivateData_.sort, flags: Order.descending)
        .build();
    final result = query.find();
    query.close();
    return result;
  }

  @override
  Stream<int> streamHomeBadgeCount() {
    return messageBox
        .watchAll()
        .debounceTime(GlobalConfig.dbDebounceTime)
        .map((query) {
      final channels = query.find();
      final channelRequestCount = channels
          .where(
            (c) =>
                c.channelTypeRaw == ChannelTypeEnum.dm.value &&
                c.dmStatusRaw == DirectMessageStatusEnum.pending.value &&
                c.userId != messageBox.activeSessionKey &&
                c.lastMessageCreateTime != null,
          )
          .length;
      final channelHasUnreadMessageCount = channels
          .where(
            (c) =>
                ((c.channelTypeRaw == ChannelTypeEnum.dm.value &&
                        c.dmStatusRaw ==
                            DirectMessageStatusEnum.contacted.value) ||
                    c.channelTypeRaw == ChannelTypeEnum.channel.value) &&
                c.lastMessageCreateTime != null &&
                (c.metadata.target?.unreadCount ?? 0) != 0,
          )
          .length;
      return channelRequestCount + channelHasUnreadMessageCount;
    });
  }

  // Enhanced cache management methods
  void _updateChannelCache(Channel channel) {
    _channelCacheManager.putIfNewer(channel.channelId, channel);
  }

  void _updateMetadataCache(ChannelMetadata metadata) {
    _metadataCacheManager.putIfNewer(metadata.channelId, metadata);
  }

  void _updatePrivateDataCache(ChannelPrivateData privateData) {
    _privateDataCacheManager.putIfNewer(privateData.channelId, privateData);
  }

  void _removeFromChannelCache(String channelId) {
    _channelCacheManager.remove(channelId);
  }

  void _removeFromMetadataCache(String channelId) {
    _metadataCacheManager.remove(channelId);
  }

  void _removeFromPrivateDataCache(String channelId) {
    _privateDataCacheManager.remove(channelId);
  }

  // Enhanced performance monitoring and logging
  void _logPerformanceMetrics() {
    final now = DateTime.now();
    if (now.difference(_lastPerformanceLog).inMinutes >= 5) {
      final channelStats = _channelCacheManager.getStats();
      final metadataStats = _metadataCacheManager.getStats();
      final privateDataStats = _privateDataCacheManager.getStats();

      Log.d(
          name: 'ChannelRepository Performance',
          'Cache Hit Rate: ${_metrics.cacheHitRate.toStringAsFixed(1)}% (${_metrics.cacheHits}/${_metrics.cacheHits + _metrics.cacheMisses}), '
          'Transactions: ${_metrics.transactionCount}, '
          'Cache Sizes: Channel=${channelStats['size']}, Meta=${metadataStats['size']}, Private=${privateDataStats['size']}, '
          'Query Cache: ${_queryCache.length}, '
          'User Update Queue: ${_userUpdateQueue.length}');

      _lastPerformanceLog = now;
      _metrics.logMetrics();
    }
  }

  void _trackTransaction() {
    _metrics.transactionCount++;
  }

  Map<String, dynamic> getCacheStats() {
    return {
      'metrics': _metrics.toMap(),
      'channelCache': _channelCacheManager.getStats(),
      'metadataCache': _metadataCacheManager.getStats(),
      'privateDataCache': _privateDataCacheManager.getStats(),
      'queryCache': _queryCache.length,
      'userUpdateQueue': _userUpdateQueue.length,
    };
  }

  // Xóa tất cả cache
  void clearAllCaches() {
    final startTime = DateTime.now();

    // Xóa tất cả cache manager
    _channelCacheManager.clear();
    _metadataCacheManager.clear();
    _privateDataCacheManager.clear();

    // Đóng và xóa query cache
    for (final query in _queryCache.values) {
      query.close();
    }
    _queryCache.clear();

    _metrics.recordOperation(
      'clear_all_caches',
      DateTime.now().difference(startTime),
    );
    Log.d(name: 'ChannelRepository', 'All caches cleared');
  }

  void _disposeDataSubscriptions() {
    _privateDataSubscription?.cancel();
    _privateDataSubscription = null;

    _metadataSubscription?.cancel();
    _metadataSubscription = null;

    _privateDataSyncSubscription?.cancel();
    _privateDataSyncSubscription = null;
  }

  // Enhanced dispose method
  void dispose() {
    _isDisposed = true;

    // Cancel all subscriptions
    _disposeDataSubscriptions();

    // [MEMORY SAFETY] Cancel debounce timer
    _userUpdateDebounceTimer?.cancel();
    _userUpdateDebounceTimer = null;

    // Clear all caches
    clearAllCaches();
    _userUpdateQueue.clear();

    // Log final metrics before disposing
    _metrics.logMetrics();
  }
}

// [OPTIMIZATION] Helper classes for optimized channel repository
class _UserUpdateEvent {
  final String sessionKey;
  final CacheUpdate<User> event;

  _UserUpdateEvent(this.sessionKey, this.event);
}

class _ChannelRelatedData {
  final ChannelMetadata? metadata;
  final ChannelPrivateData? privateData;
  final User? user;
  final UserPrivateData? userPrivateData;

  _ChannelRelatedData({
    this.metadata,
    this.privateData,
    this.user,
    this.userPrivateData,
  });
}
