//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:chat_api/src/model/v3_error.dart';
import 'package:json_annotation/json_annotation.dart';

part 'v3_list_dm_message_fragments_response.g.dart';

@JsonSerializable(
  checked: true,
  createToJson: true,
  disallowUnrecognizedKeys: false,
  explicitToJson: true,
)
class V3ListDMMessageFragmentsResponse {
  /// Returns a new [V3ListDMMessageFragmentsResponse] instance.
  V3ListDMMessageFragmentsResponse({
    this.ok,
    this.data,
    this.error,
  });

  @JsonKey(
    name: r'ok',
    required: false,
    includeIfNull: false,
  )
  final bool? ok;

  @JsonKey(
    name: r'data',
    required: false,
    includeIfNull: false,
  )
  final List<String>? data;

  @JsonKey(
    name: r'error',
    required: false,
    includeIfNull: false,
  )
  final V3Error? error;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is V3ListDMMessageFragmentsResponse &&
          other.ok == ok &&
          other.data == data &&
          other.error == error;

  @override
  int get hashCode => ok.hashCode + data.hashCode + error.hashCode;

  factory V3ListDMMessageFragmentsResponse.fromJson(
          Map<String, dynamic> json) =>
      _$V3ListDMMessageFragmentsResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$V3ListDMMessageFragmentsResponseToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
